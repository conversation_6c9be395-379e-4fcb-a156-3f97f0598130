package com.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.R;
import com.exam.domain.StInstitutionDTO;
import com.exam.domain.qo.StInstitutionQO;
import com.exam.domain.vo.StInstitutionVo;
import com.exam.service.IStInstitutionService;
import java.util.List;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 制度办法
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/institution")
public class StInstitutionController extends BaseController {

    @Resource
    private final IStInstitutionService stInstitutionService;

    /**
     * 分页查询制度办法
     */
    @PostMapping(value = "/page")
    @ResponseBody
    public R<IPage<StInstitutionVo>> page(@RequestBody StInstitutionQO stInstitutionQO) {
        return R.ok(stInstitutionService.page(stInstitutionQO));
    }

//    /**
//     * 导出制度办法列表
//     */
//    @SaCheckPermission("exam:institution:export")
//    @Log(title = "制度办法", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(StInstitutionBo bo, HttpServletResponse response) {
//        List<StInstitutionVo> list = stInstitutionService.queryList(bo);
//        ExcelUtil.exportExcel(list, "制度办法", StInstitutionVo.class, response);
//    }

//    /**
//     * 获取制度办法详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("exam:institution:query")
//    @GetMapping("/{id}")
//    public R<StInstitutionVo> getInfo(@NotNull(message = "主键不能为空")
//    @PathVariable Long id) {
//        return R.ok(stInstitutionService.queryById(id));
//    }

    /**
     * 保存制度办法
     */
    @PostMapping(value = "/save")
    @ResponseBody
    public R save(@RequestBody StInstitutionDTO stInstitutionDTO) {
        stInstitutionService.save(stInstitutionDTO);
        return R.ok();
    }

    /**
     * 修改制度办法
     */
    @PostMapping(value = "/update")
    @ResponseBody
    public R update(@RequestBody StInstitutionDTO stInstitutionDTO) {
        stInstitutionService.update(stInstitutionDTO);
        return R.ok();
    }

    /**
     * 删除制度办法
     */
    @GetMapping(value = "/delete")
    @ResponseBody
    public R<String> delete(Long id) {
        String result = null;
        try {
            stInstitutionService.delete(id);
        } catch (Exception e) {
            result = e.getMessage();
        }
        return R.ok(result);
    }


    /**
     * 获取制度办法List
     */
    @GetMapping(value = "/listInstitution")
    @ResponseBody
    public R<List<StInstitutionVo>> list() {
        return R.ok(stInstitutionService.listVo());
    }


    /**
     * 置顶
     *
     * @param id ID
     * @param isTop 0：取消置顶，1：置顶
     */
    @GetMapping(value = "/setTop")
    @ResponseBody
    public R setTop(Long id, Integer isTop) {
        StInstitutionDTO stInstitutionDTO = new StInstitutionDTO();
        stInstitutionDTO.setId(id);
        stInstitutionDTO.setIsTop(isTop);
        stInstitutionService.setTop(stInstitutionDTO);
        return R.ok();
    }
}
