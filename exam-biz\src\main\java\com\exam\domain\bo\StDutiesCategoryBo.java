package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户证件业务对象 st_duties_category
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StDutiesCategoryBo extends BaseEntity {

    /**
     * id
     */
    @NotBlank(message = "id不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 职务名称
     */
    @NotBlank(message = "职务名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;



}
