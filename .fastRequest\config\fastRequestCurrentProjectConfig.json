{"dataList": [{"hostGroup": [{"env": "local", "url": "http://localhost:19005"}], "name": "exam-admin"}, {"hostGroup": [{"env": "local", "url": "http://localhost:8080"}], "name": "exam-biz"}], "envList": ["local"], "headerList": [{"enabled": true, "type": "Authorization", "value": "Bearer  eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoyIiwicm5TdHIiOiJHVklVSWVxWDlYc1ZlTnJYY2ZjTUx4Q25TUmFNMWZ5NSJ9.3qn14Bxr71GB2OI6ql8_Dm2kWBMM4F6-19W77tzKTwY"}], "postScript": "", "preScript": "", "projectList": ["exam-admin", "exam-biz"], "syncModel": {"branch": "master", "domain": "https://github.com", "enabled": false, "namingPolicy": "byDoc", "owner": "", "repo": "", "repoUrl": "", "syncAfterRun": false, "token": "", "type": "github"}, "urlEncodedKeyValueList": [], "urlParamsKeyValueList": [], "urlSuffix": ""}