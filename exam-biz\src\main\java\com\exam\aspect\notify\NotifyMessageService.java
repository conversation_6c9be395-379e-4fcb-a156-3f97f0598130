package com.exam.aspect.notify;


import com.exam.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class NotifyMessageService {

    @Autowired
    private NotifyMessageFactory operLogFactory;

    @Async
    public void addMessage(String msgType, String msgDetailType, Object... args) throws BizException {
        if(args != null){
            NotifyMessageObject operObject = operLogFactory.getOperObject(msgType);
            operObject.notifyMessage(Integer.valueOf(msgType), Integer.valueOf(msgDetailType), args);
        }
    }
}

