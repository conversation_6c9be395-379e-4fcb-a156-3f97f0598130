package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StGroupCourseBo;
import com.exam.domain.vo.StGroupCourseVo;
import com.exam.service.IStGroupCourseService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.common.core.page.TableDataInfo;

/**
 * 组群课程关系
 *
 * <AUTHOR>
 * @date 2023-11-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/groupCourse")
public class StGroupCourseController extends BaseController {

    private final IStGroupCourseService iStGroupCourseService;

    /**
     * 查询组群课程关系列表
     */
    @SaCheckPermission("exam:groupCourse:list")
    @GetMapping("/list")
    public TableDataInfo<StGroupCourseVo> list(StGroupCourseBo bo, PageQuery pageQuery) {
        return iStGroupCourseService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出组群课程关系列表
     */
    @SaCheckPermission("exam:groupCourse:export")
    @Log(title = "组群课程关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StGroupCourseBo bo, HttpServletResponse response) {
        List<StGroupCourseVo> list = iStGroupCourseService.queryList(bo);
        ExcelUtil.exportExcel(list, "组群课程关系", StGroupCourseVo.class, response);
    }

    /**
     * 获取组群课程关系详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:groupCourse:query")
    @GetMapping("/{id}")
    public R<StGroupCourseVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStGroupCourseService.queryById(id));
    }

    /**
     * 新增组群课程关系
     */
    @SaCheckPermission("exam:groupCourse:add")
    @Log(title = "组群课程关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StGroupCourseBo bo) {
        return toAjax(iStGroupCourseService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改组群课程关系
     */
    @SaCheckPermission("exam:groupCourse:edit")
    @Log(title = "组群课程关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StGroupCourseBo bo) {
        return toAjax(iStGroupCourseService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除组群课程关系
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:groupCourse:remove")
    @Log(title = "组群课程关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable Long[] ids) {
        return toAjax(iStGroupCourseService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
