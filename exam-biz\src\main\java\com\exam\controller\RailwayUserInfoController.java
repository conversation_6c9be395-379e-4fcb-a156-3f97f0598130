package com.exam.controller;


import cn.hutool.Hutool;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.RandomUtil;
import com.exam.common.annotation.Anonymous;
import com.exam.common.annotation.Log;
import com.exam.common.core.domain.R;
import com.exam.common.enums.BusinessType;
import com.exam.common.helper.LoginHelper;
import com.exam.common.utils.poi.WordUtil;
import com.exam.common.utils.redis.RedisUtils;
import com.exam.domain.RailwayUserInfo;
import com.exam.domain.StUserExam;
import com.exam.domain.bo.RegionBo;
import com.exam.domain.vo.RailwayUserInfoVo;
import com.exam.domain.vo.UserCourseCategoryVO;
import com.exam.service.RailwayUserInfoService;
import com.exam.utils.SmsUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

import static com.exam.constant.RedisKeyConstant.VERIFICATION_CODE;

/**
 * @ClassName RailwayUserInfoController
 * @Description 个人中心信息
 * <AUTHOR>
 * @Date
 * @Version 1.0
 */

@RequestMapping("RailwayUserInfoController")
@RestController
@Api(tags = "RailwayUserInfoController(个人中心)")
public class RailwayUserInfoController {
    private static final Logger logger = LoggerFactory.getLogger(RailwayUserInfoController.class);
    @Autowired
    RailwayUserInfoService railwayUserInfoService;

    @PostMapping(value = "/selectInfo")
    @ApiOperation(value = "查询个人中心信息")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    @ApiImplicitParams({@ApiImplicitParam(name = "userId", value = "用户id", paramType = "query", required = true, dataType = "long")})
    public R<RailwayUserInfoVo> selectInfo() {
        RailwayUserInfoVo railwayUserInfoVo = railwayUserInfoService.selectInfo(3000000000001000011L);
        return R.ok(railwayUserInfoVo);
    }


    @PostMapping(value = "/saveInfo")
    @ApiOperation(value = "保存个人中心信息")
    @ApiResponse(code = 1000, message = "操作成功")
    @Log(title = "个人中心", businessType = BusinessType.UPDATE)
    @ResponseBody
    public R saveInfo(@RequestBody RailwayUserInfo railwayUserInfo) {
        boolean save = railwayUserInfoService.save(railwayUserInfo);
        if (save) {
            return R.ok();
        }
        return R.fail("保存失败请联系管理员");
    }

    @Anonymous
    @PostMapping(value = "/getRegion")
    @ApiOperation(value = "获取地址")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<Object>> getRegion(@RequestBody RegionBo bo) {

        return R.ok(railwayUserInfoService.getRegion(bo));
    }





    @ApiOperationSupport(order = 4)
    @PostMapping(value = "/getExam")
    @ApiOperation(value = "查看准考证")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<RailwayUserInfoVo> getExam(@RequestBody RailwayUserInfo railwayUserInfo) {

        return R.ok(railwayUserInfoService.getExamList(railwayUserInfo));
    }


    @ApiOperationSupport(order = 5)
    @PostMapping(value = "/downloadExam")
    @ApiOperation(value = "下载准考证")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public void downloadExam(Long examId, HttpServletResponse response, HttpServletRequest request) {

        try {

            RailwayUserInfo railwayUserInfo = railwayUserInfoService.getById(LoginHelper.getUserId());

            StUserExam exam = railwayUserInfoService.getExam(examId);

            Map<String, Object> date = BeanUtil.beanToMap(exam);

            date.put("nameAndSex", railwayUserInfo.getRuiName() + "/" + railwayUserInfo.getRuiSex());
//            date.put("organization", railwayUserInfo.get;
            date.put("idCard", railwayUserInfo.getRuiIdCard());

            String str = UUID.randomUUID() + ".docx";
            //获取yml配置地址
            String tempDir = "";
            String template = "准考证模板.docx";
            String name = WordUtil.easyPoiExport(template, "tempDir", str, date, request, response);

            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition",
                "attachment;filename=\"" + URLEncoder.encode(exam.getAdmissionTicketNumber() + "准考证", "UTF-8") + "\"");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}

