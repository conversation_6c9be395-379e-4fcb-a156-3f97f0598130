package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.TempUserTableBo;
import com.exam.domain.vo.TempUserTableVo;
import com.exam.service.ITempUserTableService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.common.core.page.TableDataInfo;

/**
 * 临时处理数据
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/userTable")
public class TempUserTableController extends BaseController {

    private final ITempUserTableService iTempUserTableService;

    /**
     * 查询临时处理数据列表
     */
    @SaCheckPermission("exam:userTable:list")
    @GetMapping("/list")
    public TableDataInfo<TempUserTableVo> list(TempUserTableBo bo, PageQuery pageQuery) {
        return iTempUserTableService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出临时处理数据列表
     */
    @SaCheckPermission("exam:userTable:export")
    @Log(title = "临时处理数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TempUserTableBo bo, HttpServletResponse response) {
        List<TempUserTableVo> list = iTempUserTableService.queryList(bo);
        ExcelUtil.exportExcel(list, "临时处理数据", TempUserTableVo.class, response);
    }

    /**
     * 获取临时处理数据详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:userTable:query")
    @GetMapping("/{id}")
    public R<TempUserTableVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iTempUserTableService.queryById(id));
    }

    /**
     * 新增临时处理数据
     */
    @SaCheckPermission("exam:userTable:add")
    @Log(title = "临时处理数据", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TempUserTableBo bo) {
        return toAjax(iTempUserTableService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改临时处理数据
     */
    @SaCheckPermission("exam:userTable:edit")
    @Log(title = "临时处理数据", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TempUserTableBo bo) {
        return toAjax(iTempUserTableService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除临时处理数据
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:userTable:remove")
    @Log(title = "临时处理数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTempUserTableService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
