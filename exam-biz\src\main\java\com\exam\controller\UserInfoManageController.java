package com.exam.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.annotation.Log;
import com.exam.common.core.domain.R;
import com.exam.common.core.domain.entity.SysUser;
import com.exam.common.enums.BusinessType;
import com.exam.common.helper.LoginHelper;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.constant.CommonDataBaseConst;
import com.exam.domain.SaveUserGroupTreeDTO;
import com.exam.domain.StGroup;
import com.exam.domain.StGroupCourse;
import com.exam.domain.StUserGroup;
import com.exam.domain.qo.UserTrainSituationQO;
import com.exam.domain.vo.*;
import com.exam.service.*;
import com.exam.system.service.ISysUserService;
import com.exam.utils.CopyUtils;
import com.exam.utils.GyUtils;
import com.exam.utils.SequenceBean;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.*;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 用户管理
 * <AUTHOR>
 * @Date 2021/6/15 16:10 下午
 * @Version 1.0
 */
@RequestMapping("UserInfoManageController")
@RestController
public class UserInfoManageController {
    private static final Logger logger = LoggerFactory.getLogger(UserInfoManageController.class);


    @Autowired
    IStGroupService stGroupService;
    @Autowired
    IStUserGroupService stUserGroupService;
    @Autowired
    IStCertificateTypeService stCertificateTypeService;
    @Autowired
    IStGroupCourseService stGroupCourseService;
    @Autowired
    UserInfoManageService userInfoManageService;
    @Autowired
    IStCourseService courseManageService;
    @Autowired
    ISysUserService iSysUserService;

    /**
     * 5.1 查看组群信息
     * @param groupName
     * @param createTime
     * @param size
     * @param current
     * @return
     * @throws ParseException
     */
    @GetMapping(value = "/getGroupInfo")
    @ResponseBody
    public R<IPage<StGroupVo>> getGroupInfo(@RequestParam(value="groupName",required=false) String groupName, @RequestParam(value="createTime",required=false) String createTime, @RequestParam(value="size",required=false) Integer size, @RequestParam(value="current",required=false)Integer current) throws ParseException {
        StGroup stGroup=new StGroup();
        stGroup.setGroupName(groupName);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if(GyUtils.isNotNull(createTime)){
            Date date = sdf.parse(createTime);
            stGroup.setCreateTime(date);
        }
//        stGroup.setProjectId(LoginHelper.getProjectId());
//        stGroup.setTenantId(LoginHelper.getTenantId());
        IPage<StGroupVo>list=stGroupService.getGroupInfo(new Page<>(current, size),stGroup);
        return R.ok(list);
    }

    /**
     * 5.2 保存&修改组群信息
     * @param stGroupVO
     * @return
     */
    @Log(title = "用户组群", businessType = BusinessType.INSERT)
    @PostMapping(value = "/saveGroupInfo")
    @ResponseBody
    public R<?> saveGroupInfo(@RequestBody StGroupVo stGroupVO) {
        //根据是否有id判断是新增还是更新
        String id=stGroupVO.getId();
        StGroup stGroup=new StGroup();
        CopyUtils.copyPropertiesVoToEty(stGroupVO,stGroup);
        if(stGroup.getFatherId() == null){
            //0代表一级组
            stGroup.setFatherId(0L);
        }
        String resStr="";
        if(GyUtils.isNotNull(id)){//更新 需要赋值修改人和时间
            stGroup.setUpdateBy(LoginHelper.getUserId());
            stGroup.setUpdateTime(new Date());
            stGroupService.updateGroupInfoById(stGroup);
            resStr="修改成功";
        }
        else{
            //赋值
            stGroup.setId(SequenceBean.getSequence());
            stGroup.setDelFlag(CommonDataBaseConst.YES_OR_NO.NO.getCode());
            stGroup.setCreateBy(LoginHelper.getUserId());
            stGroup.setCreateTime(new Date());
            //stGroup.setUpdateTime(new Date());
            if(GyUtils.isNotNull(stGroup.getFatherId()) && stGroup.getFatherId() != 0){
                StGroup stGroupLevel1 = stGroupService.getById(stGroup.getFatherId());
                stGroup.setProjectId(stGroupLevel1.getProjectId());
                stGroup.setTenantId(stGroupLevel1.getTenantId());
            }else{
                stGroup.setProjectId(LoginHelper.getProjectId());
                stGroup.setTenantId(LoginHelper.getTenantId());
            }
            stGroupService.saveGroupInfo(stGroup);
            resStr="保存成功";
        }
        return R.ok(resStr);
    }

    /**
     * 5.3删除组群信息
     * @param stGroupVO
     * @return
     */
    @Log(title = "用户组群", businessType = BusinessType.DELETE)
    @PostMapping(value = "/deleteGroupInfo")
    @ResponseBody
    public R<?> deleteGroupInfo(@RequestBody StGroupVo stGroupVO) {
        StGroup stGroup=new StGroup();
        CopyUtils.copyPropertiesVoToEty(stGroupVO,stGroup);
        //删除时候要先判断组群下是否有人和有课程，有的情况不删
       String groupId=stGroupVO.getId();
       //查人
        List<StUserGroup>sug=stUserGroupService.getUserGroup(groupId);
        //查课
        List<StGroupCourse>sgc=stGroupCourseService.getGroupCourseByGroupId(groupId);
        //查二级组
        QueryWrapper  queryWrapper = new QueryWrapper();
        queryWrapper.eq("father_id",groupId);
        queryWrapper.eq("del_flag", CommonDataBaseConst.YES_OR_NO.NO.getCode());
        List<StGroupCourse> secondGroup =stGroupService.list(queryWrapper);
        if(GyUtils.isNotNull(sug)||GyUtils.isNotNull(sgc)||GyUtils.isNotNull(secondGroup)){
            return R.fail("该组群存在人员或者课程或者二级组，不能删除");
        }
        stGroup.setDelFlag(CommonDataBaseConst.YES_OR_NO.YES.getCode());
        stGroup.setUpdateBy(LoginHelper.getUserId());
        stGroup.setUpdateTime(new Date());
//        stGroupService.updateGroupInfoById(stGroup);
        stGroupService.removeById(stGroup);
        //删除组群后,同时删除组群关联的课程，st_group_course
//        stGroupCourseService.updateGroupCourseBygroupId(stGroupVO.getId());
        return R.ok("删除成功");
    }

    /**
     * 5.4根据部门id查人员
     * @param deptId
     * @return
     */
    @GetMapping(value = "/getUserInfoByDeptId")
    @ResponseBody
    public R<List<SysUser>> getUserInfoByDeptId(String deptId) {
        SysUser sysUser = new SysUser();
       sysUser.setDeptId(Long.parseLong(deptId));
       List<SysUser>list=iSysUserService.selectUserList(sysUser);
       return R.ok(list);
    }

    /**
     * 5.5根据组群id查人员
     * @param groupId
     * @param userName
     * @param projectId
     * @param deptName
     * @param jobName
     * @param certificateType
     * @param current
     * @param size
     * @return
     */
    @GetMapping(value = "/getUserInfoByGroupId")
    @ResponseBody
    public R<IPage<Map<String,Object>>> getUserInfoByGroupId(String groupId,String userName,String projectId,
                                                                            String deptName,String jobName,String certificateType,Integer current,Integer size) {

        IPage<Map<String,Object>>list=stGroupService.getUserInfoByGroupId(new Page<>(current, size),groupId,userName,deptName,jobName,certificateType);
//        for(int i=0;i<list.size();i++){
//            CUserEntityVO cUserEntityVO=new CUserEntityVO();
//            CopyUtils.copyPropertiesEtyToVo(list.get(i),cUserEntityVO);
//            newList.add(cUserEntityVO);
//
//        }
        return R.ok(list);
    }

    /**
     * 5.6 保存&修改用户组群信息
     * @param stUserGroupVO
     * @return
     */
    @Log(title = "用户组群", businessType = BusinessType.INSERT)
    @PostMapping(value = "/saveUserGroupInfo")
    @ResponseBody
    public R<?> saveUserGroupInfo(@RequestBody StUserGroupVo stUserGroupVO) {
        //根据是否有id判断是新增还是更新
        Long id=stUserGroupVO.getId();
        String resStr="";
        StUserGroup stUserGroup=new StUserGroup();
        CopyUtils.copyPropertiesVoToEty(stUserGroupVO,stUserGroup);

        if(GyUtils.isNotNull(id)){//更新 需要赋值修改人和时间
            stUserGroup.setUpdateBy(LoginHelper.getUserId());
            stUserGroup.setUpdateTime(new Date());
            stUserGroupService.updateById(stUserGroup);
            resStr="修改成功";

        }
        else{
            //赋值
            stUserGroup.setId(SequenceBean.getSequence());
            stUserGroup.setDelFlag(CommonDataBaseConst.YES_OR_NO.NO.getCode());
            stUserGroup.setCreateBy(LoginHelper.getUserId());
            stUserGroup.setCreateTime(new Date());
            stUserGroup.setUpdateTime(new Date());
            stUserGroupService.save(stUserGroup);
            resStr="保存成功";
        }
        return R.ok(resStr);
    }

//    @ApiOperationSupport(order = 7)
//    @PostMapping(value = "/getUserInfoAndCert")
//    @ApiOperation(value = "5.7 查看用户信息带证件类型名称")
//    @ApiImplicitParams({@ApiImplicitParam(name = "CUserVo", value = "用户信息查询条件", paramType = "query", required = false, dataType = "CUserVo")})
//    @ApiResponse(code = 1000, message = "操作成功")
//
//    public R<List<Map<String,Object>>> getUserInfoAndCert(@RequestBody(required=false) CUserEntityVO cUserEntityVO) {
//            CUserVo CUserVo=new CUserVo();
//            CopyUtils.copyPropertiesVoToEty(cUserEntityVO,CUserVo);
//        return R.ok(userInfoService.getUserInfoAndCert(CUserVo));
//    }

    /**
     * 5.8 移除组群下用户
     * @param id
     * @return
     */
    @Log(title = "用户组群", businessType = BusinessType.DELETE)
    @GetMapping(value = "/updateUserGroupById")
    public R<?> updateUserGroupById(String id) {
        stUserGroupService.delUserGroupById(id);
        //删除该组的二级成员
        StUserGroup  stUserGroup = stUserGroupService.getById(id);
        if(stUserGroup != null){
            Map conditionParam = new HashMap();
            conditionParam.put("groupId",stUserGroup.getGroupId());
            conditionParam.put("userId",stUserGroup.getUserId());
            List<StUserGroup> userGroupList = stUserGroupService.getSecGroupUserByGroupId(conditionParam);
            for(StUserGroup userGroup:userGroupList){
                stUserGroupService.delUserGroupById(String.valueOf(userGroup.getId()));
            }
        }
        return R.ok("删除成功");
    }

    /**
     * 5.9 批量移除组群下用户
     * @param ids
     * @return
     */
    @Log(title = "用户组群", businessType = BusinessType.DELETE)
    @GetMapping(value = "/plUpdateUserGroupById")
    public R<?> plUpdateUserGroupById(String ids) {
        String idArray[]= ids.split(",");
        for(int i=0;i<idArray.length;i++){
            stUserGroupService.delUserGroupById(String.valueOf(idArray[i]));
            //删除该组的二级成员
            StUserGroup  stUserGroup = stUserGroupService.getById(idArray[i]);
            if(stUserGroup != null){
                Map conditionParam = new HashMap();
                conditionParam.put("groupId",stUserGroup.getGroupId());
                conditionParam.put("userId",stUserGroup.getUserId());
                List<StUserGroup> userGroupList = stUserGroupService.getSecGroupUserByGroupId(conditionParam);
                for(StUserGroup userGroup:userGroupList){
                    stUserGroupService.delUserGroupById(String.valueOf(userGroup.getId()));
                }
            }
        }
        return R.ok("删除成功");
    }

    /**
     * 5.11树形结构维护组群人员
     * @param saveUserGroupTreeDTO
     * @return
     */
    @PostMapping(value = "/saveUserGroupTree")
    @Transactional
    @ResponseBody
    @Log(title = "用户组群", businessType = BusinessType.INSERT)
    public R<?> saveUserGroupTree(@RequestBody SaveUserGroupTreeDTO saveUserGroupTreeDTO) {
        String groupId = saveUserGroupTreeDTO.getGroupId();
        String userIds = saveUserGroupTreeDTO.getUserIds();
        //step-1 先给groupID的记录都删了。。。
        //删除改组人员的培训进度 2022.9.6 修改 --开始
//        stUserGroupService.delPlayProgresByGroupId(groupId);

        //删除改组人员的培训进度 2022.9.6 修改 --结束
//        stUserGroupService.deleteByGroupId(groupId);
        //逻辑删除
//        StUserGroup delUserGroup = new StUserGroup();
//        delUserGroup.setUpdateBy(LoginHelper.getUserId());
//        delUserGroup.setUpdateTime(new Date());
//        delUserGroup.setDelFlag(Long.valueOf(Long.valueOf(CommonDataBaseConst.YES_OR_NO.YES.getCode())));
        UpdateWrapper delUserGroupWrapper = new UpdateWrapper();
        delUserGroupWrapper.eq("group_id",groupId);
//        stUserGroupService.update(delUserGroup,delUserGroupWrapper);
        stUserGroupService.remove(delUserGroupWrapper);
        //step-2把传过来的用户再插入
        //使用批量插入，传到后面的是list<vo>,开始构建
        List<StUserGroup> list=new ArrayList<>();
        long currentUserId = LoginHelper.getUserId();
        if(GyUtils.isNotNull(userIds)){
            String idArray[]= userIds.split(",");
            for(int i=0;i<idArray.length;i++){
                StUserGroup stUserGroup=new StUserGroup();
                stUserGroup.setId(SequenceBean.getSequence());
                stUserGroup.setGroupId(Long.parseLong(groupId));
                if(GyUtils.isNull(idArray[i])){
                    continue;
                }
                stUserGroup.setUserId(Long.parseLong(idArray[i]));
                stUserGroup.setUpdateBy(currentUserId);
                stUserGroup.setDelFlag(0);
                stUserGroup.setCreateBy(currentUserId);
                stUserGroup.setCreateTime(new Date());
                stUserGroup.setUpdateTime(new Date());
                stUserGroup.setTenantId(LoginHelper.getTenantId());
                //System.out.println(idArray[i]);
                list.add(stUserGroup);
            }

            //step-3批量添加
            stUserGroupService.saveUserGroupInfoPl(list);
            //添加改组人员的培训进度 2022.9.6 修改 --开始
//            stUserGroupService.addPlayProgresByGroupId(groupId);
            //添加改组人员的培训进度 2022.9.6 修改 --结束
            //删除子分组的人员
            List<StGroup> list1 = stGroupService.list(new QueryWrapper<StGroup>().eq("father_id", groupId));
            if(!CollectionUtil.isEmpty(list1))
            {
                List<Long> groupIds = list1.stream().map(StGroup::getId).collect(Collectors.toList());
                UpdateWrapper<StUserGroup> updateWrapper = new UpdateWrapper<StUserGroup>().in("group_id", groupIds);
                if(!CollectionUtil.isEmpty(list)) {
                    updateWrapper.notIn("user_id", list.stream().map(StUserGroup::getUserId).collect(Collectors.toList()));
                }
                StUserGroup entity = new StUserGroup();
                entity.setDelFlag(CommonDataBaseConst.YES_OR_NO.YES.getCode());
//                stUserGroupService.getBaseMapper().update(entity, updateWrapper);
                stUserGroupService.remove(updateWrapper);
            }
        }




        return R.ok("添加成功");
    }

    /**
     * 5.12 证件类型下拉选
     * @return
     */
    @GetMapping(value = "/getCertificateTypeList")
    @ResponseBody
    public R<List<StCertificateTypeVo>>getCertificateTypeList(){
        return R.ok(stCertificateTypeService.getCertificateTypeList());

    }

    /**
     * 5.15 组群删除前置判别
     * @param stGroupVO
     * @return
     */
    @PostMapping(value = "/checkGroupDel")
    @ResponseBody
    public R<Map<String,Object>>checkGroupDel(@RequestBody StGroupVo stGroupVO){
       String groupId=stGroupVO.getId();
        //查人
        List<StUserGroup>sug=stUserGroupService.getUserGroup(groupId);
        //查课
        List<StGroupCourse>sgc=stGroupCourseService.getGroupCourseByGroupId(groupId);
        Map<String,Object>map=new HashMap<>();
        if(GyUtils.isNotNull(sug)||GyUtils.isNotNull(sgc)){
           map.put("key","1");//存在
        }
        else {
            map.put("key","2");//不存在
        }
       return R.ok(map);

    }


    /**
     * 5.18根据二级组id查对应一级组的人员
     * @param groupId
     * @return
     */
    @GetMapping(value = "/getUserInfoBySecondGroup")
    @ResponseBody
    public R<List<SysUser>> getUserInfoBySecondGroup(@RequestParam(value="groupId",required = false) String groupId) {
        Map param= new HashMap<String,String>();
        param.put("groupId",groupId);
        return R.ok(userInfoManageService.getUserInfoBySecondGroup(param));
    }


    /**
     * 5.20 根据查询条件获取用户信息列表
     * @param queryUserParamVO
     * @return
     */
    @PostMapping(value = "/queryUserListInfo")
    @ResponseBody
    public R<IPage<SysUserVo>> queryUserListInfo(@ApiParam @RequestBody QueryUserParamVO queryUserParamVO){
        return R.ok(stUserGroupService.queryUserListInfo(queryUserParamVO));
    }

    /**
     * 5.21根据组群id查人员不分页
     * @param groupId
     * @return
     */
    @GetMapping(value = "/getUserInfoByGroupIdNoPage")
    @ResponseBody
    public R<List<SysUserVo>> getUserInfoByGroupIdNoPage(String groupId) {
        List<SysUserVo> list=stUserGroupService.getUserInfoByGroupIdNoPage(groupId);
        return R.ok(list);
    }

    /**
     * 5.22查询用户培训情况
     * @param userTrainSituationQO
     * @return
     */
    @PostMapping(value = "/getUserTrainSituation")
    @ResponseBody
    public R<UserTrainSituationGeneralVo> getUserTrainSituation(@ApiParam @RequestBody UserTrainSituationQO userTrainSituationQO) {

        UserTrainSituationGeneralVo resultData=userInfoManageService.getUserTrainSituation(userTrainSituationQO);
//        for(int i=0;i<list.size();i++){
//            CUserEntityVO cUserEntityVO=new CUserEntityVO();
//            CopyUtils.copyPropertiesEtyToVo(list.get(i),cUserEntityVO);
//            newList.add(cUserEntityVO);
//
//        }
        return R.ok(resultData);
    }

    /**
     * 5.23 重置用户培训记录
     * @param userId
     * @param courseId
     * @param courseSource
     * @return
     */
    @GetMapping(value = "/resetUserTrain")
    @ResponseBody
    public R<String> resetUserTrain(String userId, String courseId,String courseSource) {
        return userInfoManageService.resetUserTrain(userId,courseId,courseSource);
    }

    /**
     * 5.24 群组用户导出Excel
     * @param groupId
     * @param userName
     * @param companyCode
     * @param deptName
     * @param jobName
     * @param certificateType
     * @param jobAttrId
     * @param current
     * @param size
     * @param response
     * @throws Exception
     */
    @GetMapping(value = "/groupUserExport")
	@ResponseBody
	public void groupUserExport(
			String groupId,String userName,String companyCode,
	        String deptName,String jobName,String certificateType,Long jobAttrId,Integer current,Integer size,
	        HttpServletResponse response) throws Exception{
    	current = 1;
    	final int max_size = 500;

	    IPage<Map<String,Object>> groupPage = stGroupService.getUserInfoByGroupId(new Page<>(current, max_size),groupId,userName,deptName,jobName,certificateType);

	    List<Map<String, Object>> records = groupPage.getRecords();

	    long pages = groupPage.getPages();

	    if(pages > 1L) {
	    	Stream.iterate(2, i -> i + 1).limit(pages).forEach(i -> {
	    		IPage<Map<String,Object>> groupNextPage = stGroupService.getUserInfoByGroupId(new Page<>(i, max_size),groupId,userName,deptName,jobName,certificateType);
	    		List<Map<String, Object>> nextPageRecords = groupNextPage.getRecords();
	    		records.addAll(nextPageRecords);
	    	});
	    }

	 // Excel标题
        String[] title = {"序号", "用户姓名", "单位名称", "部门", "岗位", "性质", "证件类别"};
        // Excel文件名
        String fileName =  "导出用户组用户.xls";
        // sheet名
        String sheetName = "用户";
        int titleLength = title.length;

        String[][] content = new String[records.size()][titleLength];
        Stream.iterate(0, i -> i + 1).limit(records.size()).forEach(i -> {
        	content[i][0] = (i + 1)+"";
        	content[i][1] = Objects.isNull(records.get(i).get("user_name"))?"":records.get(i).get("user_name")+"";
        	content[i][2] = Objects.isNull(records.get(i).get("company_name"))?"":records.get(i).get("company_name")+"";
        	content[i][3] = Objects.isNull(records.get(i).get("dept_name"))?"":records.get(i).get("dept_name")+"";
        	content[i][4] = Objects.isNull(records.get(i).get("job_name"))?"":records.get(i).get("job_name")+"";
        	content[i][5] = Objects.isNull(records.get(i).get("nature_personnel"))?"":records.get(i).get("nature_personnel")+"";
        	content[i][6] = Objects.isNull(records.get(i).get("certificate_type_name"))?"":records.get(i).get("certificate_type_name")+"";
        });


        // 导出Excel
        HSSFWorkbook hssfWorkbook = ExcelUtil.getHSSFWorkbook(sheetName, title, content, null);
        setResponseHeader(response, fileName);
        OutputStream outputStream = response.getOutputStream();
        hssfWorkbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }

    public void setResponseHeader(HttpServletResponse response, String fileName) {
        try {
            fileName = new String(fileName.getBytes(), StandardCharsets.UTF_8);
            response.setContentType("application/octet-stream;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 5.25 导入用户组成员
     * @param groupId
     * @param groupUserFile
     * @param response
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/importUserByGroup")
    @ResponseBody
    @Log(title = "用户组群", businessType = BusinessType.IMPORT)
    public R<String> importUserByGroup(
            @RequestParam(name = "groupId") Long groupId, @RequestParam(name = "groupUserFile") MultipartFile groupUserFile,
            HttpServletResponse response) throws Exception{
        userInfoManageService.importUserByGroup(groupId, groupUserFile);
        return R.ok();
    }


}
