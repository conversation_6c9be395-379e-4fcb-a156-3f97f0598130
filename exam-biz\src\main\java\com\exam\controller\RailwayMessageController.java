package com.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.exam.common.core.domain.R;
import com.exam.domain.RailwayMessageDto;
import com.exam.domain.qo.SearchQO;
import com.exam.domain.vo.RailwayMessagePageVo;
import com.exam.domain.vo.RailwayMessageVo;
import com.exam.service.RailwayMessageService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 消息提醒有关接口
 */
@RestController
@RequestMapping("/railway/message")
@AllArgsConstructor
public class RailwayMessageController {

    private final RailwayMessageService railwayMessageService;

    /**
     * 发布消息提醒内容
     * @param railwayMessageDto
     * @return
     */
    @PostMapping("/publishMessage")
    public R<Boolean> publishMessage(@RequestBody RailwayMessageDto railwayMessageDto) {
        return R.ok(railwayMessageService.publishMessage(railwayMessageDto));
    }

    /**
     * 查询消息提醒的内容
     * @param rmId
     * @return
     */
    @GetMapping("/showMessage/{rmId}")
    public R<RailwayMessageVo> showMessage(@PathVariable Long rmId) {
        return R.ok(railwayMessageService.getMessageById(rmId));
    }

    /**
     * 将全部消息标记为已读
     * @return
     */
    @GetMapping("/updateMessageReadAll")
    public R<Boolean> updateMessageReadAll() {
        return R.ok(railwayMessageService.updateMessageRead());
    }

    /**
     * 将单挑消息标记为已读
     * @param rmId
     * @return
     */
    @GetMapping("/updateMessageReadOne/{rmId}")
    public R<Boolean> updateMessageReadOne(@PathVariable Long rmId) {
        return R.ok(railwayMessageService.updateMessageReadOne(rmId));
    }

    /**
     * 查询消息记录列表页
     * @return
     */
    @PostMapping("/fingMessagePage")
    public R<IPage<RailwayMessagePageVo>> fingMessagePage(@RequestBody SearchQO searchQO) {
        return R.ok(railwayMessageService.fingMessagePage(searchQO));
    }
}
