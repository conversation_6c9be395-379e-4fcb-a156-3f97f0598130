package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.OperationLogBo;
import com.exam.domain.vo.OperationLogVo;
import com.exam.service.IOperationLogService;
import com.exam.common.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;


/**
 * 操作日志
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/log")
public class OperationLogController extends BaseController {

    private final IOperationLogService iOperationLogService;

    /**
     * 查询操作日志列表
     */
    @SaCheckPermission("exam:log:list")
    @GetMapping("/list")
    public TableDataInfo<OperationLogVo> list(OperationLogBo bo, PageQuery pageQuery) {
        return iOperationLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出操作日志列表
     */
    @SaCheckPermission("exam:log:export")
    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OperationLogBo bo, HttpServletResponse response) {
        List<OperationLogVo> list = iOperationLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "操作日志", OperationLogVo.class, response);
    }

    /**
     * 获取操作日志详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:log:query")
    @GetMapping("/{id}")
    public R<OperationLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iOperationLogService.queryById(id));
    }

    /**
     * 新增操作日志
     */
    @SaCheckPermission("exam:log:add")
    @Log(title = "操作日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OperationLogBo bo) {
        return toAjax(iOperationLogService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改操作日志
     */
    @SaCheckPermission("exam:log:edit")
    @Log(title = "操作日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OperationLogBo bo) {
        return toAjax(iOperationLogService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除操作日志
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:log:remove")
    @Log(title = "操作日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iOperationLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
