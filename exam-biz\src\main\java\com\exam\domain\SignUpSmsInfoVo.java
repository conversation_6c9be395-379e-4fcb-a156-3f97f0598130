package com.exam.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
public class SignUpSmsInfoVo {

    private Long userId;

    private String phonenumber;

    private String examName;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String examRoomAddress;

    private String examRoomName;

    private String examNum;

}
