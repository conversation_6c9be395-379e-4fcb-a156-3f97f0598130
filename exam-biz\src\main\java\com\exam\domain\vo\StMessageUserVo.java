package com.exam.domain.vo;

import java.util.Date;

import com.exam.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 用户消息关联视图对象 st_message_user
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StMessageUserVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private String id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userInfoId;

    /**
     * 对应消息id
     */
    @ExcelProperty(value = "对应消息id")
    private Long mainTableId;

    /**
     * 消息ID
     */
    @ExcelProperty(value = "消息ID")
    private Long messageId;

    /**
     * 消息类型
     */
    @ExcelProperty(value = "消息类型")
    private Integer msgDetailType;

    /**
     * 追加消息
     */
    @ExcelProperty(value = "追加消息")
    private String appendMsg;

    /**
     * 状态（0：未读；1：已读；）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=：未读；1：已读；")
    private Integer status;

    /**
     * 阅读时间
     */
    @ExcelProperty(value = "阅读时间")
    private Date readTime;

    /**
     * 推送时间
     */
    @ExcelProperty(value = "推送时间")
    private Date pushTime;


}
