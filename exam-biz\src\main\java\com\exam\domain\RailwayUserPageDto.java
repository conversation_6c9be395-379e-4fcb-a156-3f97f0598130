package com.exam.domain;

import com.exam.domain.qo.SearchQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 铁路企业用户分页
 */
@ApiModel(description = "铁路企业用户分页")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RailwayUserPageDto extends SearchQO {

    @ApiModelProperty(value = "企业Id")
    private Long tenantId;

    @ApiModelProperty(value = "子公司或项目的id")
    private Long projectId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "审核状态:0未审核｜1通过｜2驳回")
    private Integer approveStatus;

    @ApiModelProperty(value = "用户状态：0正常｜1停用")
    private Integer status;

    @ApiModelProperty(value = "企业类型：1国铁｜2企业'")
    private Integer firmType;
}
