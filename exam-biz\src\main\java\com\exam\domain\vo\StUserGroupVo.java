package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 用户组群关系视图对象 st_user_group
 *
 * <AUTHOR>
 * @date 2023-11-07
 */
@Data
@ExcelIgnoreUnannotated
public class StUserGroupVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 组群id
     */
    @ExcelProperty(value = "组群id")
    private Long groupId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;


}
