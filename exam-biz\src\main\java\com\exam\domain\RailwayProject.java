package com.exam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 铁路局-企业管理-子公司或者项目
    */
@ApiModel(description="铁路局-企业管理-子公司或者项目")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "railway_project")
public class RailwayProject {
    /**
     * 子公司或者项目Id
     */
    @TableId(value = "rp_id")
    @ApiModelProperty(value="子公司或者项目Id")
    private Long rpId;

    /**
     * 企业Id
     */
    @TableField(value = "rf_id")
    @ApiModelProperty(value="企业Id")
    private Long rfId;

    /**
     * 子公司或者项目名称
     */
    @TableField(value = "rp_name")
    @ApiModelProperty(value="子公司或者项目名称")
    private String rpName;
}
