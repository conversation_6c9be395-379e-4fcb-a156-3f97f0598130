package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户证件业务对象 st_user_certificate
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StUserCertificateBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户
     */
    @NotNull(message = "用户不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 行业
     */
    @NotBlank(message = "行业不能为空", groups = { AddGroup.class, EditGroup.class })
    private String industry;

    /**
     * 工作单位
     */
    @NotBlank(message = "工作单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String work;

    /**
     * 证件类别
     */
    @NotBlank(message = "证件类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String certificateType;

    /**
     * 专业名称
     */
    @NotBlank(message = "专业名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String professionalName;

    /**
     * 职务
     */
    @NotBlank(message = "职务不能为空", groups = { AddGroup.class, EditGroup.class })
    private String duties;

    /**
     * 技术职称
     */
    @NotBlank(message = "技术职称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String technical;

    /**
     * 证书编号
     */
    @NotBlank(message = "证书编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String certificateNo;

    /**
     * 发证日期
     */
    @NotBlank(message = "发证日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private String issueDate;

    /**
     * 有效期
     */
    @NotBlank(message = "有效期不能为空", groups = { AddGroup.class, EditGroup.class })
    private String validityDate;

    /**
     * 发证机关
     */
    @NotBlank(message = "发证机关不能为空", groups = { AddGroup.class, EditGroup.class })
    private String issueAuthority;

    /**
     * 文件路径
     */
    @NotBlank(message = "文件路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String filePath;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;


    /**
     * 领域 1：安全教育培训  2：技能提升 3：经管考试
     */
    @NotBlank(message = "领域 1：安全教育培训  2：技能提升 3：经管考试不能为空", groups = { AddGroup.class, EditGroup.class })
    private String domainCode;


}
