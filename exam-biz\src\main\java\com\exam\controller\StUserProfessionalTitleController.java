package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StUserProfessionalTitleBo;
import com.exam.domain.vo.StUserProfessionalTitleVo;
import com.exam.service.IStUserProfessionalTitleService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 用户职称关联
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/userProfessionalTitle")
public class StUserProfessionalTitleController extends BaseController {

    private final IStUserProfessionalTitleService iStUserProfessionalTitleService;

    /**
     * 查询用户职称关联列表
     */
    @SaCheckPermission("exam:userProfessionalTitle:list")
    @GetMapping("/list")
    public TableDataInfo<StUserProfessionalTitleVo> list(StUserProfessionalTitleBo bo, PageQuery pageQuery) {
        return iStUserProfessionalTitleService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户职称关联列表
     */
    @SaCheckPermission("exam:userProfessionalTitle:export")
    @Log(title = "用户职称关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StUserProfessionalTitleBo bo, HttpServletResponse response) {
        List<StUserProfessionalTitleVo> list = iStUserProfessionalTitleService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户职称关联", StUserProfessionalTitleVo.class, response);
    }

    /**
     * 获取用户职称关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:userProfessionalTitle:query")
    @GetMapping("/{id}")
    public R<StUserProfessionalTitleVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStUserProfessionalTitleService.queryById(id));
    }

    /**
     * 新增用户职称关联
     */
    @SaCheckPermission("exam:userProfessionalTitle:add")
    @Log(title = "用户职称关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StUserProfessionalTitleBo bo) {
        return toAjax(iStUserProfessionalTitleService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改用户职称关联
     */
    @SaCheckPermission("exam:userProfessionalTitle:edit")
    @Log(title = "用户职称关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StUserProfessionalTitleBo bo) {
        return toAjax(iStUserProfessionalTitleService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户职称关联
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:userProfessionalTitle:remove")
    @Log(title = "用户职称关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStUserProfessionalTitleService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
