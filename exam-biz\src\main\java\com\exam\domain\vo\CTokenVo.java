package com.exam.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 用户token视图对象 c_token
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class CTokenVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userInfoId;

    /**
     * 用户token
     */
    @ExcelProperty(value = "用户token")
    private String userToken;

    /**
     * 设备ID
     */
    @ExcelProperty(value = "设备ID")
    private String uuid;

    /**
     * 设备注册ID
     */
    @ExcelProperty(value = "设备注册ID")
    private String registrationId;

    /**
     * 设备类型（-1:web;0：IOS、1：Andorid、2:小程序）
     */
    @ExcelProperty(value = "设备类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "-=1:web;0：IOS、1：Andorid、2:小程序")
    private Long deviceType;

    /**
     * token有效期
     */
    @ExcelProperty(value = "token有效期")
    private Date availableTime;

    /**
     * 登录时间
     */
    @ExcelProperty(value = "登录时间")
    private Date loginTime;

    /**
     * 登录时间(显示用)
     */
    @ExcelProperty(value = "登录时间(显示用)")
    private String loginTimeStr;


}
