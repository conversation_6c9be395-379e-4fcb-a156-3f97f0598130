package com.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.R;
import com.exam.domain.bo.StMockAnswerCardBo;
import com.exam.domain.vo.StMockAnswerCardDTO;
import com.exam.domain.vo.StMockAnswerCardVo;
import com.exam.service.IStMockAnswerCardService;
import java.util.Map;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 答题卡
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/mockAnswerCard")
public class StMockAnswerCardController extends BaseController {

    @Resource
    private final IStMockAnswerCardService mockAnswerCardService;

//    /**
//     * 查询答题卡列表
//     */
//    @SaCheckPermission("exam:mockAnswerCard:list")
//    @GetMapping("/list")
//    public TableDataInfo<StMockAnswerCardVo> list(StMockAnswerCardBo bo, PageQuery pageQuery) {
//        return iStMockAnswerCardService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出答题卡列表
//     */
//    @SaCheckPermission("exam:mockAnswerCard:export")
//    @Log(title = "答题卡", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(StMockAnswerCardBo bo, HttpServletResponse response) {
//        List<StMockAnswerCardVo> list = iStMockAnswerCardService.queryList(bo);
//        ExcelUtil.exportExcel(list, "答题卡", StMockAnswerCardVo.class, response);
//    }
//
//    /**
//     * 获取答题卡详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("exam:mockAnswerCard:query")
//    @GetMapping("/{id}")
//    public R<StMockAnswerCardVo> getInfo(@NotNull(message = "主键不能为空")
//    @PathVariable String id) {
//        return R.ok(iStMockAnswerCardService.queryById(id));
//    }
//
//    /**
//     * 新增答题卡
//     */
//    @SaCheckPermission("exam:mockAnswerCard:add")
//    @Log(title = "答题卡", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody StMockAnswerCardBo bo) {
//        return toAjax(iStMockAnswerCardService.insertByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 修改答题卡
//     */
//    @SaCheckPermission("exam:mockAnswerCard:edit")
//    @Log(title = "答题卡", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StMockAnswerCardBo bo) {
//        return toAjax(iStMockAnswerCardService.updateByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 删除答题卡
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("exam:mockAnswerCard:remove")
//    @Log(title = "答题卡", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//    @PathVariable String[] ids) {
//        return toAjax(iStMockAnswerCardService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
//    }

    /**
     * 立即考试
     */
    @PostMapping(value = "/saveStMockAnswerCard")
    @ResponseBody
    public R<StMockAnswerCardVo> saveStMockAnswerCard(@RequestBody StMockAnswerCardDTO stMockAnswerCardDTO) {
        return R.ok(mockAnswerCardService.saveMockAnserCard(stMockAnswerCardDTO));
    }


    /**
     * 重新刷题
     */
    @GetMapping(value = "/reTestMock")
    @ResponseBody
    public R<StMockAnswerCardVo> reTestMock(Long id) {
        return R.ok(mockAnswerCardService.reTestMock(id));
    }

    /**
     * 分页模拟考试
     */
    @PostMapping(value = "/pageStMockAnswerCard")
    @ResponseBody
    public R<IPage<StMockAnswerCardVo>> pageStMockAnswerCard(@RequestBody StMockAnswerCardBo stMockAnswerCardBo) {
        return R.ok(mockAnswerCardService.pageByBO(stMockAnswerCardBo));
    }


    /**
     * 删除模拟卡
     */
    @GetMapping(value = "/deleteStMockAnswerCard")
    @ResponseBody
    public R<Void> deleteStMockAnswerCard(Long id) {
        mockAnswerCardService.delete(id);
        return R.ok();
    }


    /**
     * 查看试卷解析
     */
    @GetMapping(value = "/selectExamPaper")
    @ResponseBody
    public R<Map<String, Object>> selectExamPaper(String answerCardId) {
        return R.ok(mockAnswerCardService.selectExamPaper(answerCardId));
    }

    /**
     * 提交模拟考试
     */
    @PostMapping(value = "/submitMockExamPapers")
    @ResponseBody
    public R<StMockAnswerCardVo> submitMockExamPapers(@RequestBody StMockAnswerCardDTO stMockAnswerCardDTO) {
        return R.ok(mockAnswerCardService.submitExamPapers(stMockAnswerCardDTO));
    }
}
