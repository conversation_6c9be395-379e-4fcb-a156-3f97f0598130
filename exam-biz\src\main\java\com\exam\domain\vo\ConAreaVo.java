package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 区域视图对象 con_area
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class ConAreaVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 编码
     */
    @ExcelProperty(value = "编码")
    private String code;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 创建人id
     */
    @ExcelProperty(value = "创建人id")
    private Long createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Long createTime;

    /**
     * 创建时间(显示)
     */
    @ExcelProperty(value = "创建时间(显示)")
    private String tabTimeStr;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @ExcelProperty(value = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))")
    private Long dataStatus;


}
