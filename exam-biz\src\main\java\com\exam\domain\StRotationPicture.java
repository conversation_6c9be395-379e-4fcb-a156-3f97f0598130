package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 【请填写功能名称】对象 st_rotation_picture
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_rotation_picture")
public class StRotationPicture extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 停留时间
     */
    private Long residenceTime;
    /**
     * 自动适配
     */
    private Long autoAdapt;
    /**
     * 图片地址（多个用，分割）
     */
    private String picPaths;
    /**
     * 区分（web,1;  APP,2）
     */
    private Long differentiate;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 所属项目
     */
    private Long projectId;

}
