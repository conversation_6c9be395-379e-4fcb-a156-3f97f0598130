package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StClasshourBo;
import com.exam.domain.vo.StClasshourVo;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.service.IStClasshourService;
import com.exam.common.core.page.TableDataInfo;

/**
 * 课程
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/classhour")
public class StClasshourController extends BaseController {

    private final IStClasshourService iStClasshourService;

    /**
     * 查询课程列表
     */
    @SaCheckPermission("exam:classhour:list")
    @GetMapping("/list")
    public TableDataInfo<StClasshourVo> list(StClasshourBo bo, PageQuery pageQuery) {
        return iStClasshourService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出课程列表
     */
    @SaCheckPermission("exam:classhour:export")
    @Log(title = "课程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StClasshourBo bo, HttpServletResponse response) {
        List<StClasshourVo> list = iStClasshourService.queryList(bo);
        ExcelUtil.exportExcel(list, "课程", StClasshourVo.class, response);
    }

    /**
     * 获取课程详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:classhour:query")
    @GetMapping("/{id}")
    public R<StClasshourVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(iStClasshourService.queryById(id));
    }

    /**
     * 新增课程
     */
    @SaCheckPermission("exam:classhour:add")
    @Log(title = "课程", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StClasshourBo bo) {
        return toAjax(iStClasshourService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改课程
     */
    @SaCheckPermission("exam:classhour:edit")
    @Log(title = "课程", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StClasshourBo bo) {
        return toAjax(iStClasshourService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除课程
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:classhour:remove")
    @Log(title = "课程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStClasshourService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
