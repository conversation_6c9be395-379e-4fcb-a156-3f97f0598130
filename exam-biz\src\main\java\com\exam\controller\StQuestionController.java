package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.annotation.Anonymous;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.StringUtils;
import com.exam.config.ExcelFieldsProperties;
import com.exam.constant.CommonConsts;
import com.exam.domain.StQuestion;
import com.exam.domain.StQuestionExcel;
import com.exam.domain.bo.StQuestionBo;
import com.exam.domain.vo.ImportQuestionVO;
import com.exam.domain.vo.SaveQuestionVO;
import com.exam.domain.vo.StQuestionVo;
import com.exam.exception.BizException;
import com.exam.service.IStQuestionService;
import com.exam.utils.ExcelUtil;
import com.exam.utils.ExcelUtils;
import com.exam.utils.GyUtils;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 试题
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/question")
public class StQuestionController extends BaseController {

    @Resource
    private final IStQuestionService iStQuestionService;

    @Autowired
    ExcelFieldsProperties excelFieldsProperties;

    /**
     * 查询试题列表
     *
     * @param questionGenre 试题类型
     * @param questionContent 试题内容
     * @param questionType 试题类别
     * @param size 每页条数
     * @param current 当前页数
     * @return 满足条件的试题列表
     */
    @GetMapping("/list")
    @ResponseBody
    public R<IPage<StQuestionVo>> selectQuestionList(@RequestParam(required = false) String questionGenre,
                                                     @RequestParam(required = false) String questionContent,
                                                     @RequestParam(required = false) String startTime,
                                                     @RequestParam(required = false) String questionType,
                                                     Integer size,
                                                     Integer current) {
        Map conditionParam = new HashMap<String, String>();
        conditionParam.put("questionGenre", questionGenre);
        conditionParam.put("questionContent", questionContent);
        conditionParam.put("startTime", startTime);
        conditionParam.put("questionType", questionType);

        IPage<StQuestionVo> stQuestionVOs = iStQuestionService.selectQuestionList(new Page<>(current, size), conditionParam);
        return R.ok(stQuestionVOs);
    }

    /**
     * 导出试题列表
     */
//    @SaCheckPermission("exam:question:export")
//    @Log(title = "试题", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(@RequestParam(required = false) String questionGenre,
//                       @RequestParam(required = false) String questionContent,
//                       @RequestParam(required = false) String createTime,
//                       @RequestParam(required = false) String questionType,
//                       HttpServletResponse response) {
//        Map conditionParam = new HashMap<String, String>();
//        conditionParam.put("questionGenre", questionGenre);
//        conditionParam.put("questionContent", questionContent);
//        conditionParam.put("createTime", createTime);
//        conditionParam.put("questionType", questionType);
//
//        List<StQuestionVo> stQuestionVOs = iStQuestionService.queryList(conditionParam);
//        try {
//            ExcelUtil.exportExcel(stQuestionVOs, response.getOutputStream());
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }

    /**
     * 获取试题详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    @ResponseBody
    public R<StQuestionVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStQuestionService.queryById(id));
    }

    /**
     * 保存试题
     */
    @Log(title = "试题", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    @ResponseBody
    public R<String> add(@Validated(AddGroup.class) @RequestBody StQuestion stQuestion) {
        StQuestionVo stQuestionVo = BeanUtil.toBean(stQuestion, StQuestionVo.class);
        long id = iStQuestionService.saveQuestion(stQuestionVo);
        return R.ok(String.valueOf(id));
    }

//    /**
//     * 修改试题
//     */
//    @SaCheckPermission("exam:question:edit")
//    @Log(title = "试题", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StQuestionBo bo) {
//        return toAjax(iStQuestionService.updateByBo(bo) ? 1 : 0);
//    }

    /**
     * 删除试题
     *
     * @param id String questionId
     */
    @Log(title = "试题", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteQuestion/{id}")
    public R remove(@NotEmpty(message = "主键不能为空")
    @PathVariable String id) {

        int ret = iStQuestionService.deleteQuestion(id);

        if (ret < 0) {
            return R.fail("对应考试试题不足，试题不能删除");
        }
        return R.ok();
    }

    /**
     * 批量上传试题
     *
     */
    @Log(title = "试题", businessType = BusinessType.IMPORT)
    @PostMapping(value = "/importQuestion")
    public R<String> importQuestion(@RequestPart("file") MultipartFile file,String questionType) {
        StQuestionVo stQuestionVo = new StQuestionVo();
        stQuestionVo.setQuestionType(questionType);
//        stQuestionVo.setCourseId(importQuestionVO.getCourseId());
//        stQuestionVo.setIsOpen(Objects.nonNull(importQuestionVO.getIsOpen()) ? (long)importQuestionVO.getIsOpen(): null);
        SaveQuestionVO saveQuestionVO;
        try {
            saveQuestionVO = iStQuestionService.importQuestion(file, stQuestionVo);
        } catch (Exception e) {
            throw new BizException(CommonConsts.QUESTION_ERROR, e.getMessage());
        }
        return R.ok(saveQuestionVO.getMsg());
    }

    /**
     * 下载试题模板
     *
     * @param request HttpServletRequest 对象
     * @param response HttpServletResponse 对象
     */
    @Log(title = "试题", businessType = BusinessType.OTHER)
    @GetMapping(value = "/downloadExceltemplate")
    public void downloadExceltemplate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        new ExcelUtils().downloadExceltemplate(response, request, "上传试题模板");
    }

    /**
     * 根据试题类别查看试题
     *
     * @param questionTypeId 试题类别id(多条逗号分割)
     * @param questionGenreId 试题类型id(多条逗号分割)
     * @return 满足条件的试题信息
     */
    @GetMapping(value = "/selectQuestionByType")
    @ResponseBody
    public R<List<StQuestion>> selectQuestionByType(String questionTypeId, String questionGenreId) {
        Map<String, String> conditionParam = new HashMap<>();
        conditionParam.put("questionTypeId", questionTypeId);
        conditionParam.put("questionGenreId", questionGenreId);
        List<StQuestion> stQuestionList = iStQuestionService.selectQuestionByType(conditionParam);
        return R.ok(stQuestionList);
    }


    /**
     * 批量删除试题
     *
     * @param questionIds 试题id集合，多ID 用英文逗号分隔，如：1724980104873119745,1724980104298500097
     */
    @Log(title = "试题", businessType = BusinessType.DELETE)
    @GetMapping(value = "/deleteQuestionBatch")
    @ResponseBody
    public R<String> deleteQuestionBatch(String questionIds) {
        Map conditionParam = new HashMap<String, String>();
        conditionParam.put("questionIds", Arrays.asList(questionIds.split(",")));
        int ret = iStQuestionService.deleteQuestionBatch(conditionParam);

        if (ret < 0) {
            return R.fail("对应考试试题不足，试题不能删除");
        }
        return R.ok();
    }

    /**
     * 查询试题个数
     *
     * @param questionTypeId 试题类别 ID
     */
    @GetMapping(value = "/selectQuestionCntByQuestionType")
    @ResponseBody
    public R<Integer> selectQuestionCntByQuestionType(@RequestParam Long questionTypeId) {
        Map<String, Object> conditionParam = new HashMap<>();
        //试题不需要权限 2021.9.10修改
        conditionParam.put("questionTypeId", questionTypeId);
        Integer cnt = iStQuestionService.selectQuestionCntByQuestionType(conditionParam);
        return R.ok(cnt);
    }


    /**
     * 批量上传试题(Word)
     *
     * @param request HttpServletRequest 对象
     * @param importQuestionVO 上传试题VO
     */
    @PostMapping(value = "/importQuestionWord")
    @ResponseBody
    public R<String> importQuestionWord(HttpServletRequest request, ImportQuestionVO importQuestionVO) throws Exception {
        StQuestionVo stQuestionVO = new StQuestionVo();
        stQuestionVO.setQuestionType(importQuestionVO.getQuestionType());
        stQuestionVO.setCourseId(importQuestionVO.getCourseId());
        stQuestionVO.setIsOpen(Objects.nonNull(importQuestionVO.getIsOpen()) ? (long)importQuestionVO.getIsOpen() : null);
        Map retMap = iStQuestionService.importQuestionWord(request, importQuestionVO.getFile(), stQuestionVO, true);
        String errMsg = (String) retMap.get("errMsg");
        if (StringUtils.isNotEmpty(errMsg)) {
            return R.fail(CommonConsts.QUESTION_ERROR, errMsg);
        }
        long count = Long.valueOf(retMap.get("count").toString());
        if (count <= 0) {
            R.fail(CommonConsts.QUESTION_ERROR, String.valueOf(count));
        }
        return R.ok("成功" + count + "道");
    }


    /**
     * 批量上传试题(Word)试验
     *
     * @param request HttpServletRequest 对象
     * @param questionType 试题类别
     * @param courseId 课程id
     * @param isOpen 1:公有,0:私有
     * @param file 上传的文件
     */
    @PostMapping(value = "/importQuestionWordTest")
    @ResponseBody
    public R<String> importQuestionWordTest(HttpServletRequest request, String questionType, String courseId, Integer isOpen,
        MultipartFile file) throws Exception {
        StQuestionVo stQuestionVO = new StQuestionVo();
        stQuestionVO.setQuestionType(questionType);
        stQuestionVO.setCourseId(courseId);
        stQuestionVO.setIsOpen(Objects.nonNull(isOpen) ? (long) isOpen : null);
        Map retMap = iStQuestionService.importQuestionWord(request, file, stQuestionVO, false);
        String buffer = (String) retMap.get("buffer");
        return R.ok(buffer);
    }


    /**
     * 批量上传试题(Excel)试验
     *
     * @param request HttpServletRequest 对象
     * @param file 文件
     */
    @PostMapping(value = "/importQuestionExcelTest")
    @ResponseBody
    public R<String> importQuestionExcelTest(HttpServletRequest request, MultipartFile file) throws Exception {
        SaveQuestionVO saveQuestionVO = iStQuestionService.importQuestion(file, null);
        return R.ok(saveQuestionVO.getMsg());
    }

    private String replaceOption(String str){
        String option = str.replace("1", "A").replace("2", "B")
            .replace("3", "C").replace("4", "D").replace("5", "E")
            .replace("6", "F").replace("7", "G").replace("8", "H");
        return option;
    }

    private String replaceJudgeRightKey(String str){
        String judgeRightKey = str.replace("1", "正确").replace("0", "错误");
        return judgeRightKey;
    }

    /**
     * 导出试题列表
     *
     * @param startTime 开始日期
     * @param endTime 结束日期
     * @param isLocal 是否本地
     */
    @Log(title = "试题", businessType = BusinessType.EXPORT)
    @GetMapping(value = "/exportQuestionList")
    @ResponseBody
    public void exportQuestionList(HttpServletRequest request, HttpServletResponse response, String startTime, String endTime,
        String isLocal) {
        Map conditionParam = new HashMap();
        conditionParam.put("startTime", startTime);
        conditionParam.put("endTime", endTime);
        List questionExcelList = iStQuestionService.selectQuestionListExport(conditionParam);
        int no = 0;
        for (Object obj : questionExcelList) {
            StQuestionExcel questionExcel = (StQuestionExcel) obj;
            Integer questionGenre = questionExcel.getQuestionGenre();
            String rightKey = questionExcel.getRightKey();
            if (3==questionGenre){
                questionExcel.setRightKey(replaceJudgeRightKey(rightKey));
            }else if (1==questionGenre||2==questionGenre){
                questionExcel.setRightKey(replaceOption(rightKey));
            }
            no++;
            questionExcel.setNo(String.valueOf(no));
            String optionContent = questionExcel.getOptionContent();
            JSONArray jSONArray = JSONArray.parseArray(optionContent);
            if (GyUtils.isNotNull(jSONArray) && jSONArray.size() >0) {
                for (int i = 0; i < jSONArray.size(); i++) {

                    JSONObject jSONObject = (JSONObject) jSONArray.get(i);

                    log.debug(jSONObject.toJSONString());

                    if (i == 0) {
                        questionExcel.setOptionContent_a((String) jSONObject.get("value"));
                    } else if (i == 1) {
                        questionExcel.setOptionContent_b((String) jSONObject.get("value"));
                    } else if (i == 2) {
                        questionExcel.setOptionContent_c((String) jSONObject.get("value"));
                    } else if (i == 3) {
                        questionExcel.setOptionContent_d((String) jSONObject.get("value"));
                    } else if (i == 4) {
                        questionExcel.setOptionContent_e((String) jSONObject.get("value"));
                    } else if (i == 5) {
                        questionExcel.setOptionContent_f((String) jSONObject.get("value"));
                    } else if (i == 6) {
                        questionExcel.setOptionContent_g((String) jSONObject.get("value"));
                    } else if (i == 7) {
                        questionExcel.setOptionContent_h((String) jSONObject.get("value"));
                    }

                }
            }
        }
        String fields = excelFieldsProperties.getQuestionExport();
        List<String> fieldList = Arrays.asList(fields.split(","));
        String path = "D:/考试系统试题.xlsx";
        if (isLocal != null && isLocal.equals("1")) {
            new ExcelUtils().excel2007Export(path, fieldList, questionExcelList, "导出试题", "导出试题模板");
        } else {
            new ExcelUtils().excel2007Export(response, request, fieldList, questionExcelList, "导出试题", "导出试题模板");
        }
    }
}
