package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 答题卡业务对象 st_answer_card_history
 *
 * <AUTHOR>
 * @date 2023-11-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StAnswerCardHistoryBo extends BaseEntity {

    /**
     * id
     */
    @NotBlank(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 考试id
     */
    @NotNull(message = "考试id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long examId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 答题卡id
     */
    @NotNull(message = "答题卡id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long answerId;

    /**
     * 答题卡状态
     */
    @NotNull(message = "答题卡状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long answerCardStatus;

    /**
     * 分数
     */
    @NotNull(message = "分数不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal score;

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long disputeStatus;

    /**
     * 补考次数
     */
    @NotNull(message = "补考次数不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long retestTimes;

    /**
     * 是否强制交卷(1.是；0.否)
     */
    @NotNull(message = "是否强制交卷(1.是；0.否)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long isForced;

    /**
     * 是否为最新
     */
    @NotBlank(message = "是否为最新不能为空", groups = {AddGroup.class, EditGroup.class})
    private String isLatest;

    /**
     * 是否通过(1.是；0.否)
     */
    @NotBlank(message = "是否通过(1.是；0.否)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String isPass;

    /**
     * (1:PC,2:APP)
     */
    @NotBlank(message = "(1:PC,2:APP)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String terminal;

    /**
     * 提交原因
     */
    @NotBlank(message = "提交原因不能为空", groups = {AddGroup.class, EditGroup.class})
    private String submitReason;

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long historyRank;

    /**
     * 租户 ID
     */
    @NotNull(message = "租户 ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;

    /**
     * 所属项目
     */
    @NotNull(message = "所属项目不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;


}
