package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.helper.LoginHelper;
import com.exam.domain.bo.StExamBo;
import com.exam.domain.vo.ExamStatedVO;
import com.exam.domain.vo.StExamVo;
import com.exam.domain.vo.StQuestionVo;
import com.exam.service.IStExamService;
import com.exam.utils.GyUtils;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 考试
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/exam")
public class StExamController extends BaseController {

    private final IStExamService iStExamService;

    /**
     * 查询考试列表
     */
    @GetMapping("/list")
    public R<IPage<StExamVo>> selectExamList(@RequestParam(required = false) String examName,
                                             @RequestParam(required = false) String startTime,
                                             @RequestParam(required = false) String endTime,
                                             @RequestParam(required = false) String examStatus,
                                             Integer size, Integer current) {
        Map conditionParam = new HashMap<String, String>();
        conditionParam.put("examName", examName);
        conditionParam.put("examStatus", examStatus);
        conditionParam.put("startTime", startTime);
        IPage<StExamVo> stExamVOs = iStExamService.selectExamList(new Page<>(current, size), conditionParam);
        return R.ok(stExamVOs);
    }

//    /**
//     * 导出考试列表
//     */
//    @SaCheckPermission("exam:exam:export")
//    @Log(title = "考试", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(StExamBo bo, HttpServletResponse response) {
//        List<StExamVo> list = iStExamService.queryList(bo);
//        ExcelUtil.exportExcel(list, "考试", StExamVo.class, response);
//    }

    /**
     * 获取考试详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<StExamVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStExamService.getExamInfo(id));
    }

    /**
     * 保存考试
     */
    @Log(title = "考试", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<String> add(@Validated(AddGroup.class) @RequestBody StExamVo stExamVo) {
        long id = iStExamService.saveExam(stExamVo);
        if (id < 0) {
            return R.fail(String.valueOf(id));
        }
        return R.ok(String.valueOf(id));
    }

//    /**
//     * 修改考试
//     */
//    @SaCheckPermission("exam:exam:edit")
//    @Log(title = "考试", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StExamBo bo) {
//        return toAjax(iStExamService.updateByBo(bo) ? 1 : 0);
//    }

    /**
     * 删除考试
     *
     * @param id 主键
     */
    @Log(title = "考试", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<String> remove(@PathVariable Long id) {
        int ret = iStExamService.deleteExam(id);
        if (ret < 0) {
            return R.fail("不能删除，该考试已开始");
        }
        return R.ok();
    }

    /**
     * 复制考试
     *
     * @param examId 考试ID
     * @return 考试信息
     */
    @Log(title = "考试", businessType = BusinessType.OTHER)
    @PostMapping(value = "/copyExam")
    @ResponseBody
    public R<StExamVo> copyExam(@RequestParam String examId) {
        Map conditionParam = new HashMap<String, String>();
        conditionParam.put("examId", examId);
        StExamVo stExamVo = iStExamService.getExamInfo(conditionParam);
        stExamVo.setId(null);
        stExamVo.setExamName(null);
        stExamVo.setStartTime(null);
        stExamVo.setEndTime(null);
        stExamVo.setResitStartTime(null);
        stExamVo.setResitEndTime(null);
        return R.ok(stExamVo);
    }

    /**
     * 查询考试试题
     *
     * @param examId 考试ID
     * @return 考试及试题信息
     */
    @GetMapping(value = "/selectExamQuestion")
    @ResponseBody
    public R<Map<Integer, List<StQuestionVo>>> selectExamQuestion(String examId) {
        Map<String, String> conditionParam = new HashMap<>();
        conditionParam.put("examId", examId);
        conditionParam.put("userId", String.valueOf(LoginHelper.getUserId()));
        conditionParam.put("callSource", "selectExamQuestion");
        Map<Integer, List<StQuestionVo>> map = iStExamService.selectExamQuestion(conditionParam);
        return R.ok(map);
    }


    /**
     * 查询考试说明
     *
     * @param examId 考试ID
     * @return 考试说明
     */
    @GetMapping(value = "/selectExamStated")
    @ResponseBody
    public R<ExamStatedVO> selectExamStated(String examId) {
        Map conditionParam = new HashMap<String, String>();
        conditionParam.put("examId", examId);
        return R.ok(iStExamService.selectExamStated(conditionParam));
    }


    /**
     * 查询考试试题(app)
     *
     * @param examId 考试ID
     */
    @GetMapping(value = "/selectExamQuestionApp")
    @ResponseBody
    public R<Map<String, List<StQuestionVo>>> selectExamQuestionApp(String examId) {
        Map<String, String> conditionParam = new HashMap<>();
        conditionParam.put("examId", examId);
        return R.ok(iStExamService.selectExamQuestionApp(conditionParam));
    }


    /**
     * 查询考试下拉列表
     */
    @GetMapping(value = "/selectExamOption")
    @ResponseBody
    public R<List<StExamVo>> selectExamOption() {
        Map<String, Long> conditionParam = new HashMap<>();
        conditionParam.put("projectId", LoginHelper.getProjectId());
        conditionParam.put("tenantId", LoginHelper.getTenantId());
        List<StExamVo> stExamVOs = iStExamService.selectExamOption(conditionParam);
        return R.ok(stExamVOs);
    }


    /**
     * 重置用户某一次考试
     *
     * @param examId 考试 ID
     * @param userId 用户 ID
     * @param historyRank 成绩排名
     */
    @GetMapping(value = "/resetUserExam")
    public R resetUserExam(String examId, String userId, Integer historyRank) {
        Map conditionParam = new HashMap<String, String>();
        conditionParam.put("examId", examId);
        conditionParam.put("userId", userId);
        conditionParam.put("historyRank", historyRank);
        iStExamService.resetUserExam(conditionParam);
        return R.ok();
    }


    /**
     * 删除创建一小时之内开始的考试用户试卷缓存
     *
     * @param examId 考试ID
     */
    @GetMapping(value = "/deleteUserExamQuestionInside")
    public R deleteUserExamQuestionInside(String examId) {
        iStExamService.deleteUserExamQuestionInside(examId);
        return R.ok();
    }


    /**
     * 开始考试测试
     *
     * @param examId 考试 ID
     */
    @GetMapping(value = "/startExamTest")
    @ResponseBody
    public R<Map<String, String>> startExamTest(String examId) {
        //因为测试考试需要先把用户分到组里再进行下面逻辑
        Map<String, String> conditionParam = new HashMap<String, String>();
        conditionParam.put("examId", examId);
        conditionParam.put("userId", String.valueOf(LoginHelper.getUserId()));
        Map<String, String> map = iStExamService.startExamTest(conditionParam);
        return R.ok(map);
    }


    /**
     * 删除考试测试记录
     *
     * @param answerId 答题卡ID
     * @param examId 考试ID
     * @param userGroupId 是否需要删除组关系
     */
    @GetMapping(value = "/delExamTest")
    @ResponseBody
    public R<Map<String, String>> delExamTest(@RequestParam(value = "answerId") String answerId,
        @RequestParam(value = "examId") String examId, @RequestParam(value = "userGroupId") String userGroupId) {
        //因为测试考试需要先把用户分到组里再进行下面逻辑
        Map<String, String> conditionParam = new HashMap<String, String>();
        conditionParam.put("examId", examId);
        conditionParam.put("answerId", answerId);
        conditionParam.put("userGroupId", userGroupId);
        conditionParam.put("userId", String.valueOf(LoginHelper.getUserId()));
        Map<String, String> map = iStExamService.delExamTest(conditionParam);
        return R.ok(map);
    }


    /**
     * 查询手动阅卷人员列表
     *
     * @param groupIds 组群名称
     * @param userName 用户名
     * @param examNames 考试名称
     * @param projectId 所属项目
     * @param chooseDateStart 选择日期开始
     * @param chooseDateEnd 选择日期结束
     * @param size 当前页
     * @param current 每页数量
     */
    @GetMapping(value = "/selectManualReviewList")
    @ResponseBody
    public R<IPage<Map<String, Object>>> selectManualReviewList(
        @RequestParam(value = "groupIds", required = false) String groupIds,
        @RequestParam(value = "userName", required = false) String userName,
        @RequestParam(value = "examNames", required = false) String examNames,
        @RequestParam(value = "projectId", required = false) String projectId,
        @RequestParam(value = "chooseDateStart", required = false) String chooseDateStart,
        @RequestParam(value = "chooseDateEnd", required = false) String chooseDateEnd,
        @RequestParam(value = "size", required = false) Integer size,
        @RequestParam(value = "current", required = false) Integer current) {

        Map<String, Object> map = new HashMap<>();
        if (GyUtils.isNotNull(groupIds)) {
            List<String> groupIdList = Arrays.asList(groupIds.split(",")).stream().distinct().collect(Collectors.toList());
            map.put("groupIdList", groupIdList);
        }
        if (GyUtils.isNotNull(examNames)) {
            List<String> examNameList = Arrays.asList(examNames.split(",")).stream().distinct().collect(Collectors.toList());
            map.put("examNameList", examNameList);
        }
        map.put("groupIds", groupIds);
        map.put("userName", userName);
        map.put("projectId", projectId);
        map.put("chooseDateStart", chooseDateStart);
        map.put("chooseDateEnd", chooseDateEnd);
        map.put("examNames", examNames);
        return R.ok(iStExamService.selectManualReviewList(new Page<>(current, size), map));
    }
}
