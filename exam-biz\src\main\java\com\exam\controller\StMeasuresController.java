package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StMeasuresBo;
import com.exam.domain.vo.StMeasuresVo;
import com.exam.service.IStMeasuresService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 重点措施
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/measures")
public class StMeasuresController extends BaseController {

    private final IStMeasuresService iStMeasuresService;

    /**
     * 查询重点措施列表
     */
    @SaCheckPermission("exam:measures:list")
    @GetMapping("/list")
    public TableDataInfo<StMeasuresVo> list(StMeasuresBo bo, PageQuery pageQuery) {
        return iStMeasuresService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出重点措施列表
     */
    @SaCheckPermission("exam:measures:export")
    @Log(title = "重点措施", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StMeasuresBo bo, HttpServletResponse response) {
        List<StMeasuresVo> list = iStMeasuresService.queryList(bo);
        ExcelUtil.exportExcel(list, "重点措施", StMeasuresVo.class, response);
    }

    /**
     * 获取重点措施详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:measures:query")
    @GetMapping("/{id}")
    public R<StMeasuresVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStMeasuresService.queryById(id));
    }

    /**
     * 新增重点措施
     */
    @SaCheckPermission("exam:measures:add")
    @Log(title = "重点措施", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StMeasuresBo bo) {
        return toAjax(iStMeasuresService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改重点措施
     */
    @SaCheckPermission("exam:measures:edit")
    @Log(title = "重点措施", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StMeasuresBo bo) {
        return toAjax(iStMeasuresService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除重点措施
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:measures:remove")
    @Log(title = "重点措施", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStMeasuresService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
