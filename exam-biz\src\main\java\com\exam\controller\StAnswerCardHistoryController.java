package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.page.TableDataInfo;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.bo.StAnswerCardHistoryBo;
import com.exam.domain.vo.StAnswerCardHistoryVo;
import com.exam.service.IStAnswerCardHistoryService;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 答题卡
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/answerCardHistory")
public class StAnswerCardHistoryController extends BaseController {

    private final IStAnswerCardHistoryService iStAnswerCardHistoryService;

    /**
     * 查询答题卡列表
     */
    @SaCheckPermission("exam:answerCardHistory:list")
    @GetMapping("/list")
    public TableDataInfo<StAnswerCardHistoryVo> list(StAnswerCardHistoryBo bo, PageQuery pageQuery) {
        return iStAnswerCardHistoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出答题卡列表
     */
    @SaCheckPermission("exam:answerCardHistory:export")
    @Log(title = "答题卡", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StAnswerCardHistoryBo bo, HttpServletResponse response) {
        List<StAnswerCardHistoryVo> list = iStAnswerCardHistoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "答题卡", StAnswerCardHistoryVo.class, response);
    }

    /**
     * 获取答题卡详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:answerCardHistory:query")
    @GetMapping("/{id}")
    public R<StAnswerCardHistoryVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable String id) {
        return R.ok(iStAnswerCardHistoryService.queryById(id));
    }

    /**
     * 新增答题卡
     */
    @SaCheckPermission("exam:answerCardHistory:add")
    @Log(title = "答题卡", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StAnswerCardHistoryBo bo) {
        return toAjax(iStAnswerCardHistoryService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改答题卡
     */
    @SaCheckPermission("exam:answerCardHistory:edit")
    @Log(title = "答题卡", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StAnswerCardHistoryBo bo) {
        return toAjax(iStAnswerCardHistoryService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除答题卡
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:answerCardHistory:remove")
    @Log(title = "答题卡", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable String[] ids) {
        return toAjax(iStAnswerCardHistoryService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
