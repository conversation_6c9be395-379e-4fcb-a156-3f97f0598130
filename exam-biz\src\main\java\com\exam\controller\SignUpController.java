package com.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.annotation.Log;
import com.exam.common.core.domain.R;
import com.exam.common.enums.BusinessType;
import com.exam.domain.SignUpDTO;
import com.exam.domain.vo.SignUpDetailVo;
import com.exam.domain.vo.SignUpVo;
import com.exam.service.SignUpService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 报名
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@RestController
@RequestMapping("/exam/signUp")
@RequiredArgsConstructor
public class SignUpController {

    private final SignUpService signUpService;

    @ApiOperation(value = "报名列表")
    @GetMapping("/getSignUpList" )
    public R<IPage<SignUpVo>> getSignUpList(Page page, @RequestParam(required = false,value = "examName")String examName,
                                            @RequestParam(required = false,value = "status")Integer status) {
        return R.ok(signUpService.getSignUpList(page,examName,status));
    }

    @ApiOperation(value = "报名")
    @Log(title = "报名", businessType = BusinessType.INSERT)
    @PostMapping("/signUp")
    public R signUp(@RequestBody SignUpDTO signUpDTO) {
        signUpService.signUp(signUpDTO);
        return R.ok();
    }

    @ApiOperation(value = "报名详情列表")
    @GetMapping("/getSignUpDetailList" )
    public R<IPage<SignUpDetailVo>> getSignUpDetailList(Page page,@RequestParam(required = true,value = "examId")Long examId,
                                                        @RequestParam(required = false,value = "examRoomName")String examRoomName,
                                                        @RequestParam(required = false,value = "nameOrPhone")String nameOrPhone,
                                                        @RequestParam(required = false,value = "idNumber")String idNumber,
                                                        @RequestParam(required = false,value = "joinFlag")Integer joinFlag) {
        return R.ok(signUpService.getSignUpDetailList(page,examId,examRoomName,nameOrPhone,idNumber,joinFlag));
    }


    @ApiOperation(value = "结束报名")
    @Log(title = "结束报名", businessType = BusinessType.OTHER)
    @PostMapping("/finishSignUp")
    public R finishSignUp(@RequestBody SignUpDTO signUpDTO) {
        signUpService.finishSignUp(signUpDTO);
        return R.ok();
    }

    @ApiOperation(value = "分配考场")
    @Log(title = "分配考场", businessType = BusinessType.OTHER)
    @PostMapping("/distributedExamRoom")
    public R distributedExamRoom(@RequestBody SignUpDTO signUpDTO){
        signUpService.distributedExamRoom(signUpDTO);
        return R.ok();
    }

}
