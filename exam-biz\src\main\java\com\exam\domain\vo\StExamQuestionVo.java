package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 考试与试题关系视图对象 st_exam_question
 *
 * <AUTHOR>
 * @date 2023-11-06
 */
@Data
@ExcelIgnoreUnannotated
public class StExamQuestionVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @ExcelProperty(value = "主键 ID")
    private Long id;

    /**
     * 考试 ID
     */
    @ExcelProperty(value = "考试 ID")
    private Long examId;

    /**
     * 试题 ID
     */
    @ExcelProperty(value = "试题 ID")
    private Long questionId;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;


}
