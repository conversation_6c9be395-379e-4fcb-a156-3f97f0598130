package com.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.exam.common.core.domain.entity.SysUser;
import com.exam.domain.RailwayUserInfo;
import com.exam.domain.RailwayUserPageDto;
import com.exam.common.helper.LoginHelper;
import com.exam.domain.StUserExam;
import com.exam.domain.StUserExamVo;
import com.exam.domain.bo.ConCityBo;
import com.exam.domain.bo.ConCountyBo;
import com.exam.domain.bo.ConProvinceBo;
import com.exam.domain.bo.RegionBo;
import com.exam.domain.vo.RailwayUserInfoVo;
import com.exam.domain.vo.UserInfoVO;
import com.exam.mapper.RailwayUserInfoMapper;
import com.exam.mapper.StUserExamMapper;
import com.exam.service.IConCityService;
import com.exam.service.IConCountyService;
import com.exam.service.IConProvinceService;
import com.exam.service.RailwayUserInfoService;
import com.exam.system.mapper.SysUserMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class RailwayUserInfoServiceImpl extends ServiceImpl<RailwayUserInfoMapper, RailwayUserInfo> implements RailwayUserInfoService {


    @Autowired
    private RailwayUserInfoMapper railwayUserInfoMapper;


    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private IConProvinceService iConProvinceService;


    @Autowired
    private IConCityService iConCityService;


    @Autowired
    private IConCountyService iConCountyService;

    @Autowired
    private StUserExamMapper stUserExamMapper;


    @Override
    public RailwayUserInfoVo selectInfo(Long userId) {
        LambdaQueryWrapper<RailwayUserInfo> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(RailwayUserInfo::getUserId, userId);
        RailwayUserInfo railwayUserInfo = railwayUserInfoMapper.selectOne(queryWrapper);
        RailwayUserInfoVo result = new RailwayUserInfoVo();
        if (railwayUserInfo!=null){
            BeanUtils.copyProperties(railwayUserInfo, result);
        }

        SysUser sysUser = sysUserMapper.selectUserById(userId);
        result.setSysUser(sysUser);
        return result;
    }

    @Override
    public List<Object> getRegion(RegionBo bo) {
        Integer level = bo.getLevel();
        String parentCode = bo.getParentCode();
        switch (level) {
            case 1:
                return Collections.singletonList(iConProvinceService.queryList(new ConProvinceBo()));
            case 2:
                ConCityBo conCityBo = new ConCityBo();
                conCityBo.setProvinceCode(parentCode);
                return Collections.singletonList(iConCityService.queryList(conCityBo));
            case 3:
                ConCountyBo conCountyBo = new ConCountyBo();
                conCountyBo.setCityCode(parentCode);
                return Collections.singletonList(iConCountyService.queryList(conCountyBo));
            default:
                return null;
        }

    }

    @Override
    public RailwayUserInfoVo getExamList(RailwayUserInfo railwayUserInfo) {
        List<StUserExamVo> stUserExams = stUserExamMapper.getList(LoginHelper.getUserId());
        RailwayUserInfoVo result=new RailwayUserInfoVo();
        BeanUtils.copyProperties(railwayUserInfo,result);
        result.setExamList(stUserExams);
        return result;
    }

    @Override
    public StUserExam getExam(Long examId) {
        return stUserExamMapper.getOneById(examId);
    }

    @Override
    public IPage<UserInfoVO> queryUserPage(RailwayUserPageDto railwayUserPageDto) {
        Page page = new Page<>(railwayUserPageDto.getCurrent(), railwayUserPageDto.getSize());
        return railwayUserInfoMapper.queryUserPage(page, railwayUserPageDto);
    }
}
