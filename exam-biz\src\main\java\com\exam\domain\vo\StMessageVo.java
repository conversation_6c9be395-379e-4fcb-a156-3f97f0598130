package com.exam.domain.vo;

import java.io.Serializable;
import java.util.Date;

import com.exam.constant.CommonDataBaseConst;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 纠纷申诉视图对象 st_message
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="StMessageVo对象", description="消息通知VO")
public class StMessageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty(value = "消息名称")
    private String title;

    @ApiModelProperty(value = "触发条件")
    private String msgType;

    @ApiModelProperty(value = "累计推送人数")
    private String pushPeople;

    @ApiModelProperty(value = "累计推送次数")
    private String pushNumber;

    @ApiModelProperty(value = "触发条件名称")
    private String msgTypeName;

    @ApiModelProperty(value = "消息详情类型")
    private String detailType;

    @ApiModelProperty(value = "消息详情类型名称")
    private String detailTypeName;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "终端")
    private String terminal;

    @ApiModelProperty(value = "状态")
    private String msgStatus;

    @ApiModelProperty(value = "状态名称")
    private String msgStatusName;

    public String getTitle() {
        String msgTitle = CommonDataBaseConst.MSG_TITLE.getMap().get(this.getMsgType());
        if(StringUtil.isEmpty(msgTitle)) {
            return this.title;
        }else {
            return msgTitle;
        }
    }

    public String getMsgStatusName() {
        return CommonDataBaseConst.MSG_STATUS.getMap().get(this.getMsgStatus());
    }

    public String getMsgTypeName() {
        return CommonDataBaseConst.MSG_TYPE.getMap().get(this.getMsgType());
    }

    public String getDetailTypeName() {
        return CommonDataBaseConst.MSG_DETAIL_TYPE.getMap().get(this.getDetailType());
    }

}
