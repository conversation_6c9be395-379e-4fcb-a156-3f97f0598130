package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.page.TableDataInfo;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.bo.StUserGroupBo;
import com.exam.domain.vo.StUserGroupVo;
import com.exam.service.IStUserGroupService;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户组群关系
 *
 * <AUTHOR>
 * @date 2023-11-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/userGroup")
public class StUserGroupController extends BaseController {

    private final IStUserGroupService iStUserGroupService;

    /**
     * 查询用户组群关系列表
     */
    @SaCheckPermission("exam:userGroup:list")
    @GetMapping("/list")
    public TableDataInfo<StUserGroupVo> list(StUserGroupBo bo, PageQuery pageQuery) {
        return iStUserGroupService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户组群关系列表
     */
    @SaCheckPermission("exam:userGroup:export")
    @Log(title = "用户组群关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StUserGroupBo bo, HttpServletResponse response) {
        List<StUserGroupVo> list = iStUserGroupService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户组群关系", StUserGroupVo.class, response);
    }

    /**
     * 获取用户组群关系详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:userGroup:query")
    @GetMapping("/{id}")
    public R<StUserGroupVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStUserGroupService.queryById(id));
    }

    /**
     * 新增用户组群关系
     */
    @SaCheckPermission("exam:userGroup:add")
    @Log(title = "用户组群关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StUserGroupBo bo) {
        return toAjax(iStUserGroupService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改用户组群关系
     */
    @SaCheckPermission("exam:userGroup:edit")
    @Log(title = "用户组群关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StUserGroupBo bo) {
        return toAjax(iStUserGroupService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户组群关系
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:userGroup:remove")
    @Log(title = "用户组群关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable Long[] ids) {
        return toAjax(iStUserGroupService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
