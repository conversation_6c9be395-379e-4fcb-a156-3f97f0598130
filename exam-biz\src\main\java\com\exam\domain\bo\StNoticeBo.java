package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 公告业务对象 st_notice
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StNoticeBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String content;

    /**
     * 是否置顶
     */
    @NotNull(message = "是否置顶不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isTop;

    /**
     * 置顶时间
     */
    @NotNull(message = "置顶时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date topTime;

    /**
     * 领域
     */
    @NotBlank(message = "领域不能为空", groups = { AddGroup.class, EditGroup.class })
    private String domainCode;

    /**
     * 权限
     */
    @NotBlank(message = "权限不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date createTime;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createBy;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateBy;


}
