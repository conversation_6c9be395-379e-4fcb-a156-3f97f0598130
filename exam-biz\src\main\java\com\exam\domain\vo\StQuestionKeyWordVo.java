package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 试题关键词视图对象 st_question_key_word
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Data
@ExcelIgnoreUnannotated
public class StQuestionKeyWordVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 试题id
     */
    @ExcelProperty(value = "试题id")
    private Long questionId;

    /**
     * 关键词内容
     */
    @ExcelProperty(value = "关键词内容")
    private String keyWordContent;

    /**
     * 分值占比
     */
    @ExcelProperty(value = "分值占比")
    private Long scoreProportion;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;


}
