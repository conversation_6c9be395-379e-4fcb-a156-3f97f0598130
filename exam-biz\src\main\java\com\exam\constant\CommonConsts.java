package com.exam.constant;

/**
 * <AUTHOR>
 * @Description:全局变量
 * @create 2021-03-15
 */
public class CommonConsts {

    /**
     * 操作成功！MyException中的1000
     */
    public static final String SUCCESS = "1000";
    /**
     * 统一错误标识码  1001
     */
    public static final String ERROR = "1001";


    public static final String QUESTION_ERROR = "10011";
    /**
     * 校验返回code
     */
    public static final String VERIFT = "-1";

    /**
     * token不存在 请重新登陆！
     */
    public static final String TOKEN_NOT_EXIT = "405";

    /**
     * 无效token 请重新登陆！
     */
    public static final String TOKEN_INVALID = "406";
    /**
     * 请求的数据错误
     */
    public static final String BODY_NOT_MATCH = "400";

    /**
     * 服务器内部错误
     */
    public static final String INTERNAL_SERVER_ERROR = "500";


    /**
     * 签名校验失败
     */
    public static final String SIGN_CHECK_ERROR = "999";

    /**
     * 局级公司编号
     */
    public static final String PARENT_COMPANY = "CC19";

    /**
     * 局级公司名称
     */
    public static final String PARENT_COMPANY_NAME = "中铁十九局集团有限公司";


    /**
     * "-"字符
     */
    public static final String HENG_XIAN = "-";



    /**
     * 是
     */
    public static final String YES = "1";
    /**
     * 否
     */
    public static final String NO = "0";

    /**
     * 成功
     */
    public static final String SUCCEED = "1";
    /**
     * 失败
     */
    public static final String FAIL = "0";


    //请求考试状态
    /**
     * 考试未开始
     */
    public static final String EXAM_NOT_STARTED = "0";
    /**
     * 培训未完成
     */
    public static final String NO_COMPLETED = "-1";
    /**
     * 已完成
     */
    public static final String EXAM_COMPLETED  = "-2";
    /**
     * 已结束
     */
    public static final String EXAM_OVERTIME = "-3";
    /**
     * 补考未开始
     */
    public static final String RETEST_EXAM_NOT_STARTED = "-4";
    /**
     * 培训未完成
     */
    public static final String TRAIN_NOT_COMPLETED = "-5";

    /**
     * 您的培训课程不达标，导致可选试题数量不足，请继续培训
     */
    public static final String EXAM_QUESTION_INSUFFICIENT = "-6";
    /**
     * 可以开始
     */
    public static final String EXAM_STARTE = "1";




    /**
     * 普通用户自动创建考试分数
     */
    public static final int AUTO_EXAM_SCORE = 100;

    /**
     * 普通用户自动创建考试分数
     */
    public static final int AUTO_EXAM_LENGTH = 60;

    /**
     * 培训时长不满足考试最小所需时长
     */
    public static final int CHECK_QUESTION_EXAM_NE1 = -1;

    /**
     * 题数小于设置的题数
     */
    public static final int CHECK_QUESTION_EXAM_NE2 = -2;

    /**
     * 当前组没有对应课程
     */
    public static final int CHECK_QUESTION_EXAM_NE3 = -3;

    /**
     * 试题数满足
     */
    public static final int CHECK_QUESTION_EXAM_1 = 1;

    /**
     * 没有对应试题类别
     */
    public static final int CHECK_QUESTION_EXAM_NE4 = -4;


    //判断答案是否正确
    /**
     *正确
     */
    public static final int REVIEW_RESULT_RIGHT= 1;
    /**
     *错误
     */
    public static final int REVIEW_RESULT_ERROR= 0;


    //删除试题类别返回值
    /**
     * 正常
     */
    public static final int DELETE_QUESTIONTYPE_RESULT_1= 1;
    /**
     * 试题类别下有试题不能删除
     */
    public static final int DELETE_QUESTIONTYPE_RESULT_NE1= -1;
    /**
     * 试题类别下有子试题类别不能删除
     */
    public static final int DELETE_QUESTIONTYPE_RESULT_NE2= -2;


    //批量导入试题返回值
    /**
     * 上传模板错误的情况
     */
    public static final int IMPORT_QUESTION_RESULT_NE1=-1;


    //保存试题类别返回值
    /**
     * 保存二级类别,其父类别下面有课程
     */
    public static final int SAVE_STQUESTION_TYPE_NE1=-1;
    /**
     * 重复的类别名称
     */
    public static final int SAVE_STQUESTION_TYPE_NE2=-2;

    /**
     * 如果已经开始考试则不能删除
     */
    public static final int DELETE_EXAM_RESULT_NE1=-1;
    /**
     * 正常删除
     */
    public static final int DELETE_EXAM_RESULT_1=1;

    /**
     * 已经开始的考试不能修改
     */
    public static final int SAVE_EXAM_RESULT_NE1=-1;

    //保存试题返回值
    /**
     * 重复试题
     */
    public static final int SAVE_QUESTION_RESULT_NE1=-1;
    /**
     * 正常
     */
    public static final int SAVE_QUESTION_RESULT_1=1;

    //保存课程类别返回值
    /**
     * 其父类别下面有课程
     */
    public static final int SAVE_CERTIFICATE_NE1=-1;

    //删除课程类别返回值
    /**
     * 此类别下有课程
     */
    public static final int DELETE_COURSECATEGORY_NE1=-1;

    /**
     * 此类别下有子课程类别
     */
    public static final int DELETE_COURSECATEGORY_NE2=-2;

    /**
     * 可以删除
     */
    public static final int DELETE_COURSECATEGORY_1=1;

    //删除课程返回值
    /**
     * 此课程下有课时不能删除
     */
    public static final int DELETE_COURSE_NE1=-1;

    /**
     * 此课程下有试题不能删除
     */
    public static final int DELETE_COURSE_NE2=-2;

    /**
     * 可以删除
     */
    public static final int DELETE_COURSE_1=1;

    /**
     * 成绩证书及格
     */
    public static final String SCORE_LEVEL_PASS="及格";

    /**
     * 成绩证书不及格
     */
    public static final String SCORE_LEVEL_FAIL="不及格";

    /**
     * 保存试题来源：新增试题
     */
    public static final String SAVE_QUESTION_DATASOURCE_1="1";

    /**
     * 保存试题来源：新增课时间提醒
     */
    public static final String SAVE_QUESTION_DATASOURCE_2="2";

    /**
     * 数据权限：只读
     */
    public static final String DATA_POWER_0="0";

    /**
     * 数据权限：可编辑
     */
    public static final String DATA_POWER_1="1";

    /**
     * 视频
     */
    public static final int FILE_TYPE_1 = 1;

    /**
     * 音频
     */
    public static final int FILE_TYPE_2 = 2;
    /**
     * 文档
     */
    public static final int FILE_TYPE_3 = 3;

    /**
     * 培训状态：培训未完成
     */
    public static final int PLAY_STATUS_0 = 0;
    /**
     * 培训状态：培训完成
     */
    public static final int PLAY_STATUS_1 = 1;
    /**
     * 答题卡状态：考试中
     */
    public static final int ANSWER_CARD_STATUS_0 = 0;

    /**
     * 答题卡状态：已交卷
     */
    public static final int ANSWER_CARD_STATUS_1 = 1;

    /**
     * 成功
     */
    public static final int ALIFACE_SUCCESS = 200;
    /**
     * 等待结果
     */
    public static final int ALIFACE_WAIT = 0;
    /**
     * 非本人
     */
    public static final int ALIFACE_ORDERS = -1;
    /**
     * 多个人脸
     */
    public static final int ALIFACE_MORE_PEOPLE = -2;
    /**
     * 无人脸
     */
    public static final int ALIFACE_NO_ONE = -3;
    /**
     * 作弊动作
     */
    public static final int ALIFACE_CHEAT = -4;

    /**
     * 无消息
     */
    public static final int noMessage = 0;
    /**
     * 有消息
     */
    public static final int newMessage = 1;


    /**
     * 是否从redis里取数据：是
     */
    public static final boolean GET_REDIS_TRUE = true;

    /**
     * 是否从redis里取数据：否
     */
    public static final boolean GET_REDIS_FALSE = false;
    /**
     * 图片地址
     */
//	public static final String IMAGE_PATH = "https://www.idcgj.com/";
    /**
     * 视频地址
     */
//	public static final String VIDEO_PATH = "https://www.idcgj.com/";

    public static final Integer TIMEOUT = -1;
    public static final Integer NORMAL = 1;
}
