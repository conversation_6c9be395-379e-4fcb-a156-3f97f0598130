package com.exam.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.domain.SignUpSmsInfoVo;
import com.exam.domain.vo.SignUpDetailVo;
import com.exam.domain.vo.SignUpVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SignUpMapper {
    IPage<SignUpVo> getSignUpList(Page page, @Param("tenantId") Long tenantId, @Param("examName") String examName,  @Param("status")Integer status);

    IPage<SignUpDetailVo> getSignUpDetailList(Page page, @Param("tenantId") Long tenantId,@Param("examId") Long examId, @Param("examRoomName") String examRoomName, @Param("nameOrPhone") String nameOrPhone, @Param("idNumber")String idNumber, @Param("joinFlag")Integer joinFlag);

    void deleteByExamId(@Param("examId") Long examId, @Param("tenantId") Long tenantId);

    List<SignUpSmsInfoVo> getSignUpSmsInfoList(String ids);
}
