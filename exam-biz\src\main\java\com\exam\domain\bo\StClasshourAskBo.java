package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 【请填写功能名称】业务对象 st_classhour_ask
 *
 * <AUTHOR>
 * @date 2023-10-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StClasshourAskBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 课时id
     */
    @NotNull(message = "课时id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long classhourId;

    /**
     * 提问位置（百分比）
     */
    @NotNull(message = "提问位置（百分比）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long askPort;

    /**
     * 回退位置（秒）
     */
    @NotNull(message = "回退位置（秒）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long backPort;

    /**
     * 题干
     */
    @NotBlank(message = "题干不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionContent;

    /**
     * 选项内容
     */
    @NotBlank(message = "选项内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String optionContent;

    /**
     * 正确答案
     */
    @NotBlank(message = "正确答案不能为空", groups = { AddGroup.class, EditGroup.class })
    private String rightKey;

    /**
     * 图片路径
     */
    @NotBlank(message = "图片路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String picPath;

    /**
     * 所属公司
     */
    @NotBlank(message = "所属公司不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;

    /**
     * 领域
     */
    @NotBlank(message = "领域不能为空", groups = { AddGroup.class, EditGroup.class })
    private String domainCode;

    /**
     * 试题类型
     */
    @NotNull(message = "试题类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long questionGenre;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;


}
