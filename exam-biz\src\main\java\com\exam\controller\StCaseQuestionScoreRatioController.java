package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StCaseQuestionScoreRatioBo;
import com.exam.domain.vo.StCaseQuestionScoreRatioVo;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.service.IStCaseQuestionScoreRatioService;
import com.exam.common.core.page.TableDataInfo;

/**
 * 案例题分数比例
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/caseQuestionScoreRatio")
public class StCaseQuestionScoreRatioController extends BaseController {

    private final IStCaseQuestionScoreRatioService iStCaseQuestionScoreRatioService;

    /**
     * 查询案例题分数比例列表
     */
    @SaCheckPermission("exam:caseQuestionScoreRatio:list")
    @GetMapping("/list")
    public TableDataInfo<StCaseQuestionScoreRatioVo> list(StCaseQuestionScoreRatioBo bo, PageQuery pageQuery) {
        return iStCaseQuestionScoreRatioService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出案例题分数比例列表
     */
    @SaCheckPermission("exam:caseQuestionScoreRatio:export")
    @Log(title = "案例题分数比例", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StCaseQuestionScoreRatioBo bo, HttpServletResponse response) {
        List<StCaseQuestionScoreRatioVo> list = iStCaseQuestionScoreRatioService.queryList(bo);
        ExcelUtil.exportExcel(list, "案例题分数比例", StCaseQuestionScoreRatioVo.class, response);
    }

    /**
     * 获取案例题分数比例详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:caseQuestionScoreRatio:query")
    @GetMapping("/{id}")
    public R<StCaseQuestionScoreRatioVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStCaseQuestionScoreRatioService.queryById(id));
    }

    /**
     * 新增案例题分数比例
     */
    @SaCheckPermission("exam:caseQuestionScoreRatio:add")
    @Log(title = "案例题分数比例", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StCaseQuestionScoreRatioBo bo) {
        return toAjax(iStCaseQuestionScoreRatioService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改案例题分数比例
     */
    @SaCheckPermission("exam:caseQuestionScoreRatio:edit")
    @Log(title = "案例题分数比例", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StCaseQuestionScoreRatioBo bo) {
        return toAjax(iStCaseQuestionScoreRatioService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除案例题分数比例
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:caseQuestionScoreRatio:remove")
    @Log(title = "案例题分数比例", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStCaseQuestionScoreRatioService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
