package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.domain.bo.StMockQuestionBo;
import com.exam.domain.qo.StMockQuestionQO;
import com.exam.domain.vo.SaveStQuestionProportionDTO;
import com.exam.domain.vo.StMockQuestionVo;
import com.exam.service.IStMockQuestionService;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 模式考试题库
 *
 * <AUTHOR>
 * @date 2023-11-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/mockQuestion")
public class StMockQuestionController extends BaseController {

    @Resource
    private final IStMockQuestionService iStMockQuestionService;

    /**
     * 分页查询模拟题库
     */
    @SaCheckPermission("exam:mockQuestion:list")
    @PostMapping("/list")
    @ResponseBody
    public R<Page<StMockQuestionVo>> page(@RequestBody StMockQuestionQO stMockQuestionQO) {
        return R.ok(iStMockQuestionService.page(stMockQuestionQO));
    }
//
//    /**
//     * 导出模式考试题库列表
//     */
//    @SaCheckPermission("exam:mockQuestion:export")
//    @Log(title = "模式考试题库", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(StMockQuestionBo bo, HttpServletResponse response) {
//        List<StMockQuestionVo> list = iStMockQuestionService.queryList(bo);
//        ExcelUtil.exportExcel(list, "模式考试题库", StMockQuestionVo.class, response);
//    }
//
//    /**
//     * 获取模式考试题库详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("exam:mockQuestion:query")
//    @GetMapping("/{id}")
//    public R<StMockQuestionVo> getInfo(@NotNull(message = "主键不能为空")
//    @PathVariable Long id) {
//        return R.ok(iStMockQuestionService.queryById(id));
//    }
//
//    /**
//     * 新增模式考试题库
//     */
//    @SaCheckPermission("exam:mockQuestion:add")
//    @Log(title = "模式考试题库", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody StMockQuestionBo bo) {
//        return toAjax(iStMockQuestionService.insertByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 修改模式考试题库
//     */
//    @SaCheckPermission("exam:mockQuestion:edit")
//    @Log(title = "模式考试题库", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StMockQuestionBo bo) {
//        return toAjax(iStMockQuestionService.updateByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 删除模式考试题库
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("exam:mockQuestion:remove")
//    @Log(title = "模式考试题库", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//    @PathVariable Long[] ids) {
//        return toAjax(iStMockQuestionService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
//    }


    /**
     * 设置比例
     *
     * @param saveStQuestionProportionDTO 保存试题类别比例DTO
     */
    @PostMapping(value = "/saveStQuestionProportion")
    @ResponseBody
    public R<Void> saveStQuestionProportion(@Valid @RequestBody SaveStQuestionProportionDTO saveStQuestionProportionDTO) {
        iStMockQuestionService.saveStQuestionProportion(saveStQuestionProportionDTO);
        return R.ok();
    }

    /**
     * 全部更新
     */
    @GetMapping(value = "/refreshAll")
    @ResponseBody
    public R<Void> refreshAll() {
        iStMockQuestionService.refreshAll();
        return R.ok();
    }


    /**
     * 分类更新
     */
    @GetMapping(value = "/refreshOne")
    @ResponseBody
    public R<Void> refreshOne(Long questionTypeId) {
        iStMockQuestionService.refreshOne(questionTypeId);
        return R.ok();
    }
}
