package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 区业务对象 con_county
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ConCountyBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     *
     */
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String code;

    /**
     *
     */
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     *
     */
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cityCode;


}
