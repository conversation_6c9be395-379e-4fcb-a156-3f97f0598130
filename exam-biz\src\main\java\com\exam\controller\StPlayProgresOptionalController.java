package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StPlayProgresOptionalBo;
import com.exam.domain.vo.StPlayProgresOptionalVo;
import com.exam.service.IStPlayProgresOptionalService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 播放进度
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/playProgresOptional")
public class StPlayProgresOptionalController extends BaseController {

    private final IStPlayProgresOptionalService iStPlayProgresOptionalService;

    /**
     * 查询播放进度列表
     */
    @SaCheckPermission("exam:playProgresOptional:list")
    @GetMapping("/list")
    public TableDataInfo<StPlayProgresOptionalVo> list(StPlayProgresOptionalBo bo, PageQuery pageQuery) {
        return iStPlayProgresOptionalService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出播放进度列表
     */
    @SaCheckPermission("exam:playProgresOptional:export")
    @Log(title = "播放进度", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StPlayProgresOptionalBo bo, HttpServletResponse response) {
        List<StPlayProgresOptionalVo> list = iStPlayProgresOptionalService.queryList(bo);
        ExcelUtil.exportExcel(list, "播放进度", StPlayProgresOptionalVo.class, response);
    }

    /**
     * 获取播放进度详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:playProgresOptional:query")
    @GetMapping("/{id}")
    public R<StPlayProgresOptionalVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStPlayProgresOptionalService.queryById(id));
    }

    /**
     * 新增播放进度
     */
    @SaCheckPermission("exam:playProgresOptional:add")
    @Log(title = "播放进度", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StPlayProgresOptionalBo bo) {
        return toAjax(iStPlayProgresOptionalService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改播放进度
     */
    @SaCheckPermission("exam:playProgresOptional:edit")
    @Log(title = "播放进度", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StPlayProgresOptionalBo bo) {
        return toAjax(iStPlayProgresOptionalService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除播放进度
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:playProgresOptional:remove")
    @Log(title = "播放进度", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStPlayProgresOptionalService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
