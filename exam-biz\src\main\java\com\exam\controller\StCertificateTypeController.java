package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StCertificateTypeBo;
import com.exam.domain.vo.StCertificateTypeVo;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.service.IStCertificateTypeService;
import com.exam.common.core.page.TableDataInfo;

/**
 * 证件类别
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/certificateType")
public class StCertificateTypeController extends BaseController {

    private final IStCertificateTypeService iStCertificateTypeService;

    /**
     * 查询证件类别列表
     */
    @SaCheckPermission("exam:certificateType:list")
    @GetMapping("/list")
    public TableDataInfo<StCertificateTypeVo> list(StCertificateTypeBo bo, PageQuery pageQuery) {
        return iStCertificateTypeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出证件类别列表
     */
    @SaCheckPermission("exam:certificateType:export")
    @Log(title = "证件类别", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StCertificateTypeBo bo, HttpServletResponse response) {
        List<StCertificateTypeVo> list = iStCertificateTypeService.queryList(bo);
        ExcelUtil.exportExcel(list, "证件类别", StCertificateTypeVo.class, response);
    }

    /**
     * 获取证件类别详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:certificateType:query")
    @GetMapping("/{id}")
    public R<StCertificateTypeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStCertificateTypeService.queryById(id));
    }

    /**
     * 新增证件类别
     */
    @SaCheckPermission("exam:certificateType:add")
    @Log(title = "证件类别", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StCertificateTypeBo bo) {
        return toAjax(iStCertificateTypeService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改证件类别
     */
    @SaCheckPermission("exam:certificateType:edit")
    @Log(title = "证件类别", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StCertificateTypeBo bo) {
        return toAjax(iStCertificateTypeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除证件类别
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:certificateType:remove")
    @Log(title = "证件类别", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStCertificateTypeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
