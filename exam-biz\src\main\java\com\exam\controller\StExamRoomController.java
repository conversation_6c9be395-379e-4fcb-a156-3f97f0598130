package com.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.annotation.Log;
import com.exam.common.core.domain.R;
import com.exam.common.enums.BusinessType;
import com.exam.domain.StExamRoom;
import com.exam.domain.vo.ExamRoomVo;
import com.exam.service.IStExamRoomService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 考场表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@RestController
@RequestMapping("/exam/examRoom")
@RequiredArgsConstructor
public class StExamRoomController {

    private final IStExamRoomService iStExamRoomService;

    @ApiOperation(value = "考场列表")
    @GetMapping("/getExamRoomList" )
    public R<IPage<ExamRoomVo>> getExamRoomList(Page page, @RequestParam(required = false,value = "name")String name,
                                                @RequestParam(required = false,value = "provinceId")Long provinceId,
                                                @RequestParam(required = false,value = "cityId")Long cityId,
                                                @RequestParam(required = false,value = "countyId")Long countyId,
                                                @RequestParam(required = false,value = "status")Long status) {
        return R.ok(iStExamRoomService.getExamRoomList(page,name,provinceId,cityId,countyId,status));
    }

    @ApiOperation(value = "新增考场")
    @Log(title = "新增考场", businessType = BusinessType.INSERT)
    @PostMapping("/saveExamRoom")
    public R saveExamRoom(@RequestBody StExamRoom stExamRoom) {
        iStExamRoomService.saveExamRoom(stExamRoom);
        return R.ok();
    }


    @ApiOperation(value = "修改考场")
    @Log(title = "修改考场", businessType = BusinessType.UPDATE)
    @PostMapping("/updateExamRoom")
    public R updateExamRoom(@RequestBody StExamRoom stExamRoom) {
        iStExamRoomService.updateExamRoom(stExamRoom);
        return R.ok();
    }

    @ApiOperation(value = "停用/启用考场")
    @Log(title = "停用/启用考场", businessType = BusinessType.UPDATE)
    @GetMapping("/enableExamRoom" )
    public R enableExamRoom(@RequestParam("id")Long id,@RequestParam("status")Long status) {
        iStExamRoomService.enableExamRoom(id,status);
        return R.ok();
    }


    @ApiOperation(value = "企业下未被占用的考场")
    @GetMapping("/getNotOccupiedExamRoomList" )
    public R<List<ExamRoomVo>> getNotOccupiedExamRoomList() {
        return R.ok(iStExamRoomService.getNotOccupiedExamRoomList());
    }

}
