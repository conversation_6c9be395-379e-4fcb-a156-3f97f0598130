--- # 数据源配置
spring:
  datasource:
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: false
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: com.zaxxer.hikari.HikariDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************************************************************************************************************************************************************************************************
          username: root
          password: 1qaz!QAZ

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    cluster:
      nodes:
        - **************:37000
        - **************:37001
        - **************:37002
        - **************:37003
        - **************:37004
        - **************:37005
#    # 地址
#    host: **************
#    # 端口，默认为6379
#    port: 6379
#    # 数据库索引
    database: 0
#    # 密码(如没有密码请注释掉)
#    password: dc3
    # 连接超时时间
    timeout: 20s
    # 是否开启ssl
    ssl: false

redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 32
  # Netty线程池数量
  nettyThreads: 64
#  singleServerConfig:
#    # 客户端名称
#    clientName: ${exam.name}
#    # 最小空闲连接数
#    connectionMinimumIdleSize: 32
#    # 连接池大小
#    connectionPoolSize: 128
#    # 连接空闲超时，单位：毫秒
#    idleConnectionTimeout: 10000
#    # 命令等待超时，单位：毫秒
#    timeout: 3000
#    # 发布和订阅连接池大小
#    subscriptionConnectionPoolSize: 100

  # 单节点配置
  clusterServersConfig  :
    # 客户端名称
    clientName: ${exam.name}
    masterConnectionMinimumIdleSize: 32
      # master连接池大小
    masterConnectionPoolSize: 64
      # slave最小空闲连接数
    slaveConnectionMinimumIdleSize: 32
      # slave连接池大小
    slaveConnectionPoolSize: 64
      # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
      # 命令等待超时，单位：毫秒
    timeout: 3000
      # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50
      # 读取模式
    readMode: "MASTER_SLAVE"
      # 订阅模式
    subscriptionMode: "MASTER"

--- # sms 短信
sms:
  enabled: false
  # 阿里云 dysmsapi.aliyuncs.com
  # 腾讯云 sms.tencentcloudapi.com
  endpoint: "dysmsapi.aliyuncs.com"
  accessKeyId: xxxxxxx
  accessKeySecret: xxxxxx
  signName: 测试
  # 腾讯专用
  sdkAppId:

train:
  image:
    path: /train/
    upload:
      path: /storage/static/images/train/img/
    domain: img/
    server: http://**************:9000/
  file:
    zip:
      path: /usr/local/train/certpic/
    path: /train/file/
    upload:
      path: /storage/static/files/train/file/
    domain: file/
    client: /train/file/aaa.zip
    server: http://**************:9000/files
  video:
    path: /train/video/
    upload:
      path: /storage/static/videos/train/video/
    domain: video/
    server: http://**************:9000/videos

ansj:
  default:
    dic:
      path: d:/default.dic

invigilator_sms:
  file:
    server: http://**************:9000/
#sms.file.server = http://**************:9000/
  aiplatform:
    api-url: https://ai.techin.top:10020/ai/user/match
#sms.aiplatform.api-url = http://**************/ai/user/match
    group-id: UgvQPiQed6

spring:
  face:
    url: https://api-cn.faceplusplus.com/facepp/v3/compare
#spring.face.appkey=p8JhvC72Y-jZGmFpOWV3LQEFHHLeGu1j
#spring.face.appSecret=4em2j6MB780ovpu7gi_LBQ_KC2sPxcMM
    appkey: Qzj8J3TAdgtvMSRNb1H-VmoOaqoouiaG
    appSecret: 2nwUIxpZiX8_oojAW98kuvt0wRNXkZ4U

