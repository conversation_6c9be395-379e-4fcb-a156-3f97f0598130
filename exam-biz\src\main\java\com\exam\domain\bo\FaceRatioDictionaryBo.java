package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 人脸对比成功比例业务对象 face_ratio_dictionary
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class FaceRatioDictionaryBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 人脸对比成功比例
     */
    @NotNull(message = "人脸对比成功比例不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long radio;

    /**
     * 系统名称
     */
    @NotBlank(message = "系统名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String app;

    /**
     * 系统描述
     */
    @NotBlank(message = "系统描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appDesc;


}
