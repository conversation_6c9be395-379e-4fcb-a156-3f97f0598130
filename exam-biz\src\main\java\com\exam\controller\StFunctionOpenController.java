package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.page.TableDataInfo;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.bo.StFunctionOpenBo;
import com.exam.domain.vo.StFunctionOpenVo;
import com.exam.service.IStFunctionOpenService;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 岗位属性
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/functionOpen")
public class StFunctionOpenController extends BaseController {

    private final IStFunctionOpenService iStFunctionOpenService;

    /**
     * 查询岗位属性列表
     */
    @SaCheckPermission("exam:functionOpen:list")
    @GetMapping("/list")
    public TableDataInfo<StFunctionOpenVo> list(StFunctionOpenBo bo, PageQuery pageQuery) {
        return iStFunctionOpenService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出岗位属性列表
     */
    @SaCheckPermission("exam:functionOpen:export")
    @Log(title = "岗位属性", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StFunctionOpenBo bo, HttpServletResponse response) {
        List<StFunctionOpenVo> list = iStFunctionOpenService.queryList(bo);
        ExcelUtil.exportExcel(list, "岗位属性", StFunctionOpenVo.class, response);
    }

    /**
     * 获取岗位属性详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:functionOpen:query")
    @GetMapping("/{id}")
    public R<StFunctionOpenVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStFunctionOpenService.queryById(id));
    }

    /**
     * 新增岗位属性
     */
    @SaCheckPermission("exam:functionOpen:add")
    @Log(title = "岗位属性", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StFunctionOpenBo bo) {
        return toAjax(iStFunctionOpenService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改岗位属性
     */
    @SaCheckPermission("exam:functionOpen:edit")
    @Log(title = "岗位属性", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StFunctionOpenBo bo) {
        return toAjax(iStFunctionOpenService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除岗位属性
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:functionOpen:remove")
    @Log(title = "岗位属性", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable Long[] ids) {
        return toAjax(iStFunctionOpenService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
