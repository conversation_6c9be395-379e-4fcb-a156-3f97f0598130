package com.exam.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 【请填写功能名称】视图对象 st_user
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StUserVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 照片
     */
    @ExcelProperty(value = "照片")
    private String profilePicture;

    /**
     * 出生日期
     */
    @ExcelProperty(value = "出生日期")
    private Date birthDate;

    /**
     * 籍贯
     */
    @ExcelProperty(value = "籍贯")
    private String nativePlace;

    /**
     * 职称
     */
    @ExcelProperty(value = "职称")
    private Long title;

    /**
     * 政治面貌
     */
    @ExcelProperty(value = "政治面貌")
    private Integer policitalStatus;

    /**
     * 毕业学校
     */
    @ExcelProperty(value = "毕业学校")
    private String graduationSchool;

    /**
     * 毕业时间
     */
    @ExcelProperty(value = "毕业时间")
    private Date graduationTime;

    /**
     * 专业
     */
    @ExcelProperty(value = "专业")
    private String major;

    /**
     * 工作经历
     */
    @ExcelProperty(value = "工作经历")
    private String workExperience;

    /**
     * 培训经历
     */
    @ExcelProperty(value = "培训经历")
    private String trainExperience;

    /**
     * 个人业绩
     */
    @ExcelProperty(value = "个人业绩")
    private String achievement;

    /**
     * 获奖情况
     */
    @ExcelProperty(value = "获奖情况")
    private String award;

    /**
     * 是否删除(1.是；0.否)
     */
    @ExcelProperty(value = "是否删除(1.是；0.否)")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人id
     */
    @ExcelProperty(value = "创建人id")
    private Long createBy;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long updateBy;


}
