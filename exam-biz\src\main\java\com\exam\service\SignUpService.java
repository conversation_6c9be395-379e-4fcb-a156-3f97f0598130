package com.exam.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.domain.SignUpDTO;
import com.exam.domain.vo.SignUpDetailVo;
import com.exam.domain.vo.SignUpVo;

public interface SignUpService {
    IPage<SignUpVo> getSignUpList(Page page, String examName, Integer status);

    void signUp(SignUpDTO signUpDTO);

    IPage<SignUpDetailVo> getSignUpDetailList(Page page,Long examId, String examRoomName, String nameOrPhone, String idNumber, Integer joinFlag);

    void finishSignUp(SignUpDTO signUpDTO);
    void autoFinishSignUp();

    void distributedExamRoom(SignUpDTO signUpDTO);
}
