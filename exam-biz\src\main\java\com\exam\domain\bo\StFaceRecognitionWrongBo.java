package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户组群关系业务对象 st_face_recognition_wrong
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StFaceRecognitionWrongBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 人脸照片
     */
    @NotBlank(message = "人脸照片不能为空", groups = { AddGroup.class, EditGroup.class })
    private String faceFilePath;

    /**
     * 人脸照片路径
     */
    @NotBlank(message = "人脸照片路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String faceFileUrl;

    /**
     * 身份证照片
     */
    @NotBlank(message = "身份证照片不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idCardFilePath;

    /**
     * 身份证路径
     */
    @NotBlank(message = "身份证路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idCardFileUrl;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date createTime;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createBy;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateBy;


}
