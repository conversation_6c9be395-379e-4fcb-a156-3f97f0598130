package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.page.TableDataInfo;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.bo.StExamQuestionBo;
import com.exam.domain.vo.StExamQuestionVo;
import com.exam.service.IStExamQuestionService;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 考试与试题关系
 *
 * <AUTHOR>
 * @date 2023-11-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/examQuestion")
public class StExamQuestionController extends BaseController {

    private final IStExamQuestionService iStExamQuestionService;

    /**
     * 查询考试与试题关系列表
     */
    @SaCheckPermission("exam:examQuestion:list")
    @GetMapping("/list")
    public TableDataInfo<StExamQuestionVo> list(StExamQuestionBo bo, PageQuery pageQuery) {
        return iStExamQuestionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出考试与试题关系列表
     */
    @SaCheckPermission("exam:examQuestion:export")
    @Log(title = "考试与试题关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StExamQuestionBo bo, HttpServletResponse response) {
        List<StExamQuestionVo> list = iStExamQuestionService.queryList(bo);
        ExcelUtil.exportExcel(list, "考试与试题关系", StExamQuestionVo.class, response);
    }

    /**
     * 获取考试与试题关系详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:examQuestion:query")
    @GetMapping("/{id}")
    public R<StExamQuestionVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStExamQuestionService.queryById(id));
    }

    /**
     * 新增考试与试题关系
     */
    @SaCheckPermission("exam:examQuestion:add")
    @Log(title = "考试与试题关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StExamQuestionBo bo) {
        return toAjax(iStExamQuestionService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改考试与试题关系
     */
    @SaCheckPermission("exam:examQuestion:edit")
    @Log(title = "考试与试题关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StExamQuestionBo bo) {
        return toAjax(iStExamQuestionService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除考试与试题关系
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:examQuestion:remove")
    @Log(title = "考试与试题关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable Long[] ids) {
        return toAjax(iStExamQuestionService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
