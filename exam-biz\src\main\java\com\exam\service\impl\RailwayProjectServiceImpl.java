package com.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.exam.domain.RailwayProject;
import com.exam.mapper.RailwayProjectMapper;
import com.exam.service.RailwayProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RailwayProjectServiceImpl extends ServiceImpl<RailwayProjectMapper, RailwayProject> implements RailwayProjectService{

    @Autowired
    private RailwayProjectMapper projectMapper;
    @Override
    public void add(RailwayProject project) {
        save(project);
    }

    @Override
    public void editById(RailwayProject project) {
        updateById(project);
    }

    @Override
    public List<RailwayProject> queryByRfId(Long rfId) {

        return list(new LambdaQueryWrapper<RailwayProject>().eq(RailwayProject::getRfId, rfId));
    }


    @Override
    public RailwayProject selectByUserId(Long userId) {
        return projectMapper.selectByUserId(userId);
    }
}
