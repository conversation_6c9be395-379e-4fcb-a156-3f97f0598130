package com.exam.domain.qo;


import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "查询必须")
public class SearchQO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量", required = true)
    private Integer size;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", required = true)
    private Integer current;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String column;

    private final String defaultColumn = "create_time";
    /**
     * 是否正序
     */
    @ApiModelProperty(value = "是否正序")
    private Boolean isAsc;

    /**
     * 创建Page对象，默认排序创建时间，传排序字段，使用字段排序
     * @return
     */
    @SuppressWarnings("rawtypes")
    public Page buildPage() {
        return buildPage(defaultColumn);
    }

    /**
     * 默认字段添加
     * @param defaultColumn
     * @return
     */
    @SuppressWarnings("rawtypes")
    public Page buildPage(String defaultColumn) {
        Page page = new Page<>(current, size);
        OrderItem orderItem = new OrderItem();
        if(StringUtils.isEmpty(column)) {
            orderItem.setColumn(defaultColumn);
            orderItem.setAsc(false);
        }else {
            orderItem.setColumn(column);
            if(isAsc != null) {
                orderItem.setAsc(isAsc);
            }
        }
        page.addOrder(orderItem);
        return page;
    }

    public Page buildPageNoOrder() {
        Page page = new Page<>(current, size);
        return page;
    }

}
