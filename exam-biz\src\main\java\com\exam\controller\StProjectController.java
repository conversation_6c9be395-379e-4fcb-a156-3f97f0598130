package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StProjectBo;
import com.exam.domain.vo.StProjectVo;
import com.exam.service.IStProjectService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/project")
public class StProjectController extends BaseController {

    private final IStProjectService iStProjectService;

    /**
     * 查询【请填写功能名称】列表
     */
    @SaCheckPermission("exam:project:list")
    @GetMapping("/list")
    public TableDataInfo<StProjectVo> list(StProjectBo bo, PageQuery pageQuery) {
        return iStProjectService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @SaCheckPermission("exam:project:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StProjectBo bo, HttpServletResponse response) {
        List<StProjectVo> list = iStProjectService.queryList(bo);
        ExcelUtil.exportExcel(list, "【请填写功能名称】", StProjectVo.class, response);
    }

    /**
     * 获取【请填写功能名称】详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:project:query")
    @GetMapping("/{id}")
    public R<StProjectVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStProjectService.queryById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @SaCheckPermission("exam:project:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StProjectBo bo) {
        return toAjax(iStProjectService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改【请填写功能名称】
     */
    @SaCheckPermission("exam:project:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StProjectBo bo) {
        return toAjax(iStProjectService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除【请填写功能名称】
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:project:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStProjectService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
