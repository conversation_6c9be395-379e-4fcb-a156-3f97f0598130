package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 成绩等级 业务对象 st_score_level
 *
 * <AUTHOR>
 * @date 2023-11-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StScoreLevelBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 考试id
     */
    @NotNull(message = "考试id不能为空", groups = { EditGroup.class })
    private Long examId;

    /**
     * 成绩等级名称
     */
    @NotBlank(message = "成绩等级名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String scoreLevelName;

    /**
     * 区间最高分
     */
    @NotNull(message = "区间最高分不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double highestScore;

    /**
     * 区间最低分
     */
    @NotNull(message = "区间最低分不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double lowestScore;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;


}
