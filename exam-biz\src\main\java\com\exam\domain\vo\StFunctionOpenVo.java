package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import java.util.Date;

import com.exam.constant.CommonDataBaseConst;
import lombok.Data;


/**
 * 岗位属性视图对象 st_function_open
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@ExcelIgnoreUnannotated
public class StFunctionOpenVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 功能开放时间
     */
    @ExcelProperty(value = "功能开放时间")
    private Date startTime;

    /**
     * 功能结束时间
     */
    @ExcelProperty(value = "功能结束时间")
    private Date endTime;

    /**
     * 类型1：岗位属性，2：模拟考试
     */
    @ExcelProperty(value = "类型1：岗位属性，2：模拟考试")
    private Integer type;

    /**
     * 租户ID
     */
    @ExcelProperty(value = "租户ID")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;

    /**
     * 是否删除
     */
    private Integer delFlag;

    /**
     * 功能名称
     */
    private String name;

    public String getName() {
        return CommonDataBaseConst.FUNCTION_OPEN_TYPE.getMap().get(this.type);
    }
}
