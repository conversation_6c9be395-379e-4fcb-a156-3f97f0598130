package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.domain.bo.StMessageUserBo;
import com.exam.domain.qo.StMessageUserQO;
import com.exam.domain.vo.StMessageUserVo;
import com.exam.service.IStCompulsionComplaintService;
import com.exam.service.IStMessageUserService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 用户消息关联
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/messageUser")
public class StMessageUserController extends BaseController {

    private final IStMessageUserService iStMessageUserService;
    @Autowired
    IStCompulsionComplaintService iStCompulsionComplaintService;

    @ApiOperationSupport(order = 1)
    @PostMapping(value = "/pageMessgeUser")
    @ApiOperation(value = "1.17 分页查询消息")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<IPage<StMessageUserVo>> pageMessgeUser(@RequestBody StMessageUserQO stMessageUserQO) {
        return R.ok(iStMessageUserService.page(stMessageUserQO));
    }

    @ApiOperationSupport(order = 2)
    @GetMapping(value = "/read")
    @ApiOperation(value = "1.4 已读")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> read(Long id) {
        iStMessageUserService.read(id);
        return R.ok();
    }

    @ApiOperationSupport(order = 3)
    @GetMapping(value = "/readALL")
    @ApiOperation(value = "1.4 已读全部")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> readALL() {
        iStMessageUserService.readALL();
        return R.ok();
    }

    @ApiOperationSupport(order = 3)
    @GetMapping(value = "/newMessage")
    @ApiOperation(value = "1.4 获取新消息")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Integer> newMessage() {
        iStCompulsionComplaintService.isTimeOut();
        return R.ok(iStMessageUserService.newMeesage());
    }

    /**
     * 查询用户消息关联列表
     */
    @SaCheckPermission("exam:messageUser:list")
    @GetMapping("/list")
    public TableDataInfo<StMessageUserVo> list(StMessageUserBo bo, PageQuery pageQuery) {
        return iStMessageUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户消息关联列表
     */
    @SaCheckPermission("exam:messageUser:export")
    @Log(title = "用户消息关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StMessageUserBo bo, HttpServletResponse response) {
        List<StMessageUserVo> list = iStMessageUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户消息关联", StMessageUserVo.class, response);
    }

    /**
     * 获取用户消息关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:messageUser:query")
    @GetMapping("/{id}")
    public R<StMessageUserVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(iStMessageUserService.queryById(id));
    }

    /**
     * 新增用户消息关联
     */
    @SaCheckPermission("exam:messageUser:add")
    @Log(title = "用户消息关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StMessageUserBo bo) {
        return toAjax(iStMessageUserService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改用户消息关联
     */
    @SaCheckPermission("exam:messageUser:edit")
    @Log(title = "用户消息关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StMessageUserBo bo) {
        return toAjax(iStMessageUserService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户消息关联
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:messageUser:remove")
    @Log(title = "用户消息关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(iStMessageUserService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
