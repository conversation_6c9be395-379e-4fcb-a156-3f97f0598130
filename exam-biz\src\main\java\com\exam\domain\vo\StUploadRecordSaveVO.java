package com.exam.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 上传附件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "StUploadRecordSaveVO", description = "上传文件返回对象")
public class StUploadRecordSaveVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 文件资源来源应用
     */
    private String sourceApp;

    /**
     * 文件用途
     */
    private String  filePurpose ;

    /**
     * 文件类型
     *  "fileType": ".zip",
     */
    private String  fileType ;

    /**
     * 描述
     */
    private String  fileDesc ;

    /**
     * 文件名称
     * "fileName": "importCert(3).zip",
     */
    private String  fileName ;

    /**
     * 文件路径
     * "group1/M01/00/51/wKgT6WMpe3iAF-uzAAB42H09vIw787.zip"
     */
    private String  filePath ;
    private Long  createBy ;
    private Long  createTime ;

    /**
     * 文件服务器全路径
     * http://**************:3443/group1/M01/00/51/wKgT6WMpe3iAF-uzAAB42H09vIw787.zip
     */
    private String  fileUrl ;
    private String  file ;
    private Long  isScript ;
    private String  thumbnailUrl ;
    private String  thumbnailPath ;
    private String  videoDuration ;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tabTimeStr;

    private Long fileSize;

    private Long parentId;

    /**
     * 压缩文件解压后的文件集合
     */
    @ApiModelProperty(value = "压缩文件解压后的文件集合")
    private List<StUploadRecordSaveVO> fileZipVOList;

}
