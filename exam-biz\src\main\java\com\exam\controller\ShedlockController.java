package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.ShedlockBo;
import com.exam.domain.vo.ShedlockVo;
import com.exam.service.IShedlockService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/shedlock")
public class ShedlockController extends BaseController {

    private final IShedlockService iShedlockService;

    /**
     * 查询【请填写功能名称】列表
     */
    @SaCheckPermission("exam:shedlock:list")
    @GetMapping("/list")
    public TableDataInfo<ShedlockVo> list(ShedlockBo bo, PageQuery pageQuery) {
        return iShedlockService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @SaCheckPermission("exam:shedlock:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ShedlockBo bo, HttpServletResponse response) {
        List<ShedlockVo> list = iShedlockService.queryList(bo);
        ExcelUtil.exportExcel(list, "【请填写功能名称】", ShedlockVo.class, response);
    }

    /**
     * 获取【请填写功能名称】详细信息
     *
     * @param name 主键
     */
    @SaCheckPermission("exam:shedlock:query")
    @GetMapping("/{name}")
    public R<ShedlockVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String name) {
        return R.ok(iShedlockService.queryById(name));
    }

    /**
     * 新增【请填写功能名称】
     */
    @SaCheckPermission("exam:shedlock:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ShedlockBo bo) {
        return toAjax(iShedlockService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改【请填写功能名称】
     */
    @SaCheckPermission("exam:shedlock:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ShedlockBo bo) {
        return toAjax(iShedlockService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除【请填写功能名称】
     *
     * @param names 主键串
     */
    @SaCheckPermission("exam:shedlock:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{names}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] names) {
        return toAjax(iShedlockService.deleteWithValidByIds(Arrays.asList(names), true) ? 1 : 0);
    }
}
