package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 用户组群关系业务对象 st_user_wrong_act
 *
 * <AUTHOR>
 * @date 2023-11-27
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StUserWrongActBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 答题卡id
     */
    @NotNull(message = "答题卡id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long answerCardId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 用户姓名
     */
    @NotBlank(message = "用户姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String userName;

    /**
     * 违规行为
     */
    @NotBlank(message = "违规行为不能为空", groups = {AddGroup.class, EditGroup.class})
    private String wrongAction;

    /**
     * 违规编号
     */
    @NotNull(message = "违规编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer code;

    /**
     * 是否提醒
     */
    @NotNull(message = "是否提醒不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long isRemind;

    /**
     * 是否强制提交
     */
    @NotNull(message = "是否强制提交不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long isForceSubmit;

    /**
     * 考试id
     */
    @NotNull(message = "考试id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long examId;

    /**
     * 身份证正面照片地址
     */
    @NotBlank(message = "身份证正面照片地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String idImageUrl;

    /**
     * 错误图片base64编码
     */
    @NotBlank(message = "错误图片base64编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String errorPicBase;

    /**
     * 人脸识别错误编码
     */
    @NotBlank(message = "人脸识别错误编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String errorCode;

    /**
     * 比对结果置信度
     */
    @NotBlank(message = "比对结果置信度不能为空", groups = {AddGroup.class, EditGroup.class})
    private String confidence;

    /**
     * 补考次数
     */
    @NotBlank(message = "补考次数不能为空", groups = {AddGroup.class, EditGroup.class})
    private String retestTimes;

    /**
     * 提醒内容
     */
    @NotBlank(message = "提醒内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remindContent;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;

    /**
     * 考试名称
     */
    @NotBlank(message = "考试名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String examName;


}
