package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 课程类别业务对象 st_course_category
 *
 * <AUTHOR>
 * @date 2023-11-02
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StCourseCategoryBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 类别名称
     */
    @NotBlank(message = "类别名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String categoryName;

    /**
     * 封面图片路径
     */
    @NotBlank(message = "封面图片路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String picPath;

    /**
     * 父id
     */
    @NotNull(message = "父id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fatherId;

    /**
     * 是否安管人员(1.是；0.否)
     */
    @NotNull(message = "是否安管人员(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isSafety;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;


}
