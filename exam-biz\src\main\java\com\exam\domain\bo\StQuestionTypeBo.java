package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 试题类型业务对象 st_question_type
 *
 * <AUTHOR>
 * @date 2023-11-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StQuestionTypeBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 试题类别名称
     */
    @NotBlank(message = "试题类别名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String questionTypeName;

    /**
     * 类别排序
     */
    @NotNull(message = "类别排序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long questionTypeSn;

    /**
     * 父id
     */
    @NotNull(message = "父id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long fatherId;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;

    /**
     * 所属项目
     */
    @NotNull(message = "所属项目不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;


}
