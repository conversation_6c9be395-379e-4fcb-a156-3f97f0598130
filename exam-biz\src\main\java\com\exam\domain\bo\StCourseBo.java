package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 课程业务对象 st_course
 *
 * <AUTHOR>
 * @date 2023-10-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StCourseBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 所属类别id
     */
    @NotNull(message = "所属类别id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long categoryId;

    /**
     * 课程类别名称
     */
    private String categoryName;

    /**
     * 课程名称
     */
    @NotBlank(message = "课程名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String courseName;

    /**
     * 课时数
     */
    @NotNull(message = "课时数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long classhourCount;

    /**
     * 课程时长
     */
    @NotNull(message = "课程时长不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseDuration;

    /**
     * 主讲老师
     */
    @NotBlank(message = "主讲老师不能为空", groups = { AddGroup.class, EditGroup.class })
    private String teacher;

    /**
     * 封面图片路径
     */
    @NotBlank(message = "封面图片路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String picPath;

    /**
     * 是否为精品课程(1.是；0.否)
     */
    @NotNull(message = "是否为精品课程(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isBoutique;

    /**
     * 是否安管人员(1.是；0.否)
     */
    @NotNull(message = "是否安管人员(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isSafety;

    /**
     * 安管是否分类(1.已分类；0.未分类)
     */
    @NotNull(message = "安管是否分类(1.已分类；0.未分类)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isSafetyCategory;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;

    @NotNull(message = "删除标识", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;


}
