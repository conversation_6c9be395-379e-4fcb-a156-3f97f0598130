package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 组群课程关系视图对象 st_group_course
 *
 * <AUTHOR>
 * @date 2023-11-06
 */
@Data
@ExcelIgnoreUnannotated
public class StGroupCourseVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 组群id
     */
    @ExcelProperty(value = "组群id")
    private Long groupId;

    /**
     * 课程id
     */
    @ExcelProperty(value = "课程id")
    private Long courseId;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long projectId;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;


}
