package com.exam.system.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.exam.common.core.domain.entity.SysUserEntity;

/**
 * 用户 业务层
 */
public interface ISysUserEntityService {

    SysUserEntity getUser(String username);

    SysUserEntity getUserCache(String username);

    /**
     * 缓存考试用户
     *
     * @param userIdList
     * @param deadline
     */
    void cacheExamUser(Long tenantId, List<Long> userIdList, LocalDateTime deadline);

    /**
     * key:明文，value:密文
     *
     * @param pwdMap
     */
    void cachePassword(Map<String, String> pwdMap);

    /**
     * @param pwd  明文
     * @param hash 密文
     * @return
     */
    Boolean checkPwd(String pwd, String hash);

}
