package com.exam.constant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

/**
 * 数据库对应的状态码常量类
 */
public class CommonDataBaseConst {

    /**
     * 通用所有基础数据表的数据状态 停用 状态暂时无
     *
     * <AUTHOR>
     */
    public enum DATA_STATUS {
        DELETED(0, "删除"),
        NORMAL(1, "正常"),
        FROZEN(2, "停用");
        private int code;
        private String mess;

        DATA_STATUS(int code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public int getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (DATA_STATUS as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 通用所有数据表中 是、否的数据状态
     *
     * <AUTHOR>
     */
    public enum YES_OR_NO {
        YES(1, "是"),
        NO(0, "否");
        private int code;
        private String mess;

        YES_OR_NO(int code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public int getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (YES_OR_NO as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 通用所有数据表中 是、否的数据状态
     *
     * <AUTHOR>
     */
    public enum YES_OR_NO_STR {
        YES("1", "是"),
        NO("0", "否");
        private String code;
        private String mess;

        YES_OR_NO_STR(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (YES_OR_NO_STR as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 试题类型名称字典
     *
     * <AUTHOR>
     */
    public enum QUESTION_GENRE_NAME {
        SINGLE_CHOICE(1, "单选题"),
        MULTIPLE_CHOICE(2, "多选题"),
        JUDGE(3, "判断题"),
        COMPLETION(4, "填空题"),
        SHORTANSWER(5, "简答题"),
        CASE(6, "案例题");
        private int code;
        private String mess;

        QUESTION_GENRE_NAME(int code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public int getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (QUESTION_GENRE_NAME as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    //单选
    public static final int CONST_SINGLE_CHOICE = 1;
    //多选
    public static final int CONST_MULTIPLE_CHOICE = 2;
    //判断
    public static final int CONST_JUDGE = 3;
    //填空
    public static final int CONST_COMPLETION = 4;
    //简答
    public static final int SHORT_ANSWER = 5;
    //案例题
    public static final int CASE = 6;

//    /**
//     * 试卷状态
//     *
//     * <AUTHOR>
//     */
//    public enum ANSWER_CARD_STATUS {
//        SUBMITTED(1, "已提交"),
//        CHECKED(2, "已阅卷");
//        private int code;
//        private String mess;
//
//        ANSWER_CARD_STATUS(int code, String mess) {
//            this.code = code;
//            this.mess = mess;
//        }
//
//        public int getCode() {
//            return code;
//        }
//
//        public String getMess() {
//            return mess;
//        }
//
//        public static LinkedHashMap<Integer, String> getMap() {
//            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
//            for (ANSWER_CARD_STATUS as : values()) {
//                map.put(as.getCode(), as.getMess());
//            }
//            return map;
//        }
//    }

    /**
     * 人员类型
     *
     * <AUTHOR>
     */
    public enum NATURE_PERSONNEL {
        REGULAR_EMPLOYEE(0, "正式员工"),
        PERSONNEL_AGENCY(1, "合同工"),
        TEMPS(2, "人事代理");
        private int code;
        private String mess;

        NATURE_PERSONNEL(int code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public int getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (NATURE_PERSONNEL as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 考试种类
     *
     * <AUTHOR>
     */
    public enum EXAM_TYPE {
        MOCK_EXAMINATION(0, "模拟考试"),
        FORMAL_EXAMINATION(1, "正式考试");
        private int code;
        private String mess;

        EXAM_TYPE(int code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public int getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (EXAM_TYPE as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 考试状态
     *
     * <AUTHOR>
     */
    public enum EXAM_STATUS {
        EXAM_NOT_STARTED("0", "未开始"),
        NO_COMPLETED("-1", "培训未完成"),
        EXAM_OVER("-2", "已完成"),
        EXAM_OVERTIME("-3", "已结束"),
        RETEST_EXAM_NOT_STARTED("-4", "补考未开始"),
        TRAIN_NOT_COMPLETED("-5", "培训未完成"),
        EXAM_QUESTION_INSUFFICIENT("-6", "可选试题数量不足，请继续培训"),
        EXAM_STARTE("1", "已开始");
        private String code;
        private String mess;

        EXAM_STATUS(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (EXAM_STATUS as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 课程来源
     *
     * <AUTHOR>
     */
    public enum COURSE_SOURCE {
        COURSE_SOURCE_1("1", "规定课程"),
        COURSE_SOURCE_2("2", "自选课程");
        private String code;
        private String mess;

        COURSE_SOURCE(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (COURSE_SOURCE as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 培训方式（0:不培训,1:规定课程,2:自选课程）
     *
     * <AUTHOR>
     */
    public enum TRAIN_METHOD {
        TRAIN_METHOD_1("0", "不培训"),
        TRAIN_METHOD_2("1", "规定课程"),
        TRAIN_METHOD_3("2", "自选课程");
        private String code;
        private String mess;

        TRAIN_METHOD(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (TRAIN_METHOD as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 申诉类型
     *
     * <AUTHOR>
     */
    public enum COMPLAINT_TYPE {
        DISPUTE(1, "纠分"),
        COMPULSORY(2, "强制交卷");
        private Integer code;
        private String mess;

        COMPLAINT_TYPE(Integer code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public Integer getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (COMPLAINT_TYPE as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 申诉状态
     *
     * <AUTHOR>
     */
    public enum DISPUTE_STATUS {
        UNCOMMITTED(-1, "未提交"),
        UNTREATED(0, "未审核"),
        PROCESSED(1, "已处理"),
        TIMEOUT(2, "已超时");
        private Integer code;
        private String mess;

        DISPUTE_STATUS(Integer code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public Integer getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (DISPUTE_STATUS as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 申诉详情类型
     *
     * <AUTHOR>
     */
    public enum COMPLAINT_STATUS {
        REFUSE(-1, "已拒绝"),
        UNTREATED(0, "未审核"),
        PASS(1, "已通过"),
        TIMEOUT(2, "已超时");
        private Integer code;
        private String mess;

        COMPLAINT_STATUS(Integer code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public Integer getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (COMPLAINT_STATUS as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 申诉详情类型
     *
     * <AUTHOR>
     */
    public enum MSG_STATUS {
        STOP("0", "停用"),
        START("1", "启用");
        private String code;
        private String mess;

        MSG_STATUS(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (MSG_STATUS as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 消息推送出发条件类型
     *
     * <AUTHOR>
     */
    public enum MSG_TITLE {
        COMPULSION("3", "强制交卷申诉"),
        DISPUTE("4", "纠分申诉");
        private String code;
        private String mess;

        MSG_TITLE(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (MSG_TITLE as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 消息推送出发条件类型
     *
     * <AUTHOR>
     */
    public enum MSG_TYPE {
        COURSE("1", "分配课程"),
        EXAM("2", "创建考试"),
        COMPULSION("3", "申诉发起"),
        DISPUTE("4", "申诉发起");
        private String code;
        private String mess;

        MSG_TYPE(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (MSG_TYPE as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 消息详情类型
     *
     * <AUTHOR>
     */
    public enum MSG_DETAIL_TYPE {
        NONE("0", "无分类"),
        COMPLAINT_UNTREATED("1", "强制交卷申诉待处理"),
        COMPLAINT_PASS("2", "强制交卷申诉已通过"),
        COMPLAINT_REFUSE("3", "强制交卷申诉已拒绝"),
        COMPLAINT_TIMEOUT("4", "强制交卷申诉已超时"),
        DISPUTE_UNTREATED("5", "纠分申诉待处理"),
        DISPUTE_PROCESSED("6", "纠分申诉已处理");
        private String code;
        private String mess;

        MSG_DETAIL_TYPE(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (MSG_DETAIL_TYPE as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 申诉详情类型
     *
     * <AUTHOR>
     */
    public enum MSG_READ_STATUS {
        UNREAD("0", "未读"),
        READED("1", "已读");
        private String code;
        private String mess;

        MSG_READ_STATUS(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (MSG_READ_STATUS as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    public enum PROFESSIONAL_TITLE {
        ONE("1", "正高级工程师"),
        TWO("2", "高级工程师"),
        THREE("3", "中级工程师"),
        FOUR("4", "助理工程师"),
        FIVE("5", "高级技师"),
        SIX("6", "技师");

        private String code;
        private String mess;

        PROFESSIONAL_TITLE(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static List<Map<String, String>> getSelectList() {
            List<Map<String, String>> list = new ArrayList<>();
            LinkedHashMap<String, String> map = null;
            for (PROFESSIONAL_TITLE as : values()) {
                map = new LinkedHashMap<>();
                map.put("professionalTitle", as.getCode());
                map.put("professionalTitleText", as.getMess());
                list.add(map);
            }
            return list;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (PROFESSIONAL_TITLE as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }

        public static String getMesses(String codes) { // 逗号分隔的职称码
            LinkedHashMap<String, String> map = getMap();
            if (StringUtils.isNotBlank(codes)) {
                return Arrays.asList(codes.split(",")).stream().map((s) -> map.get(s)).collect(Collectors.joining(","));
            }
            return "-";
        }
    }

    public enum CERTIFICATE_TYPE {
        ONE("201", "一级建造师"),
        TWO("202", "二级建造师"),
        THREE("203", "注册建筑师"),
        FOUR("204", "注册造价工程师（一级）"),
        FIVE("205", "注册结构工程师"),
        SIX("206", "注册土木工程师"),
        SEVEN("207", "注册公用设备工程师"),
        EIGHT("208", "注册电气工程师"),
        NINE("209", "注册人防防护工程师"),
        TEN("210", "爆破工程技术人员安全作业证"),
        ELEVEN("211", "注册安全工程师"),
        TWELVE("212", "试验检测工程师"),
        THIRTEEN("213", "助理试验检测师"),
        FOURTEEN("214", "注册会计师");

        private String code;
        private String mess;

        CERTIFICATE_TYPE(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static List<Map<String, Object>> getSelectList() {
            List<Map<String, Object>> list = new ArrayList<>();
            LinkedHashMap<String, Object> map = null;
            for (CERTIFICATE_TYPE as : values()) {
                map = new LinkedHashMap<>();
                map.put("certificateType", as.getCode());
                map.put("certificateTypeText", as.getMess());
                list.add(map);
            }
            return list;
        }
    }

    public enum PROFESSIONAL_NAME {
        ONE_ONE(CERTIFICATE_TYPE.ONE.getCode(), "水利水电"),
        ONE_TWO(CERTIFICATE_TYPE.ONE.getCode(), "市政工程"),
        ONE_THREE(CERTIFICATE_TYPE.ONE.getCode(), "房屋建筑"),
        ONE_FOUR(CERTIFICATE_TYPE.ONE.getCode(), "民航机场"),
        ONE_FIVE(CERTIFICATE_TYPE.ONE.getCode(), "港口与航道"),
        ONE_SIX(CERTIFICATE_TYPE.ONE.getCode(), "通信广电"),
        ONE_SEVEN(CERTIFICATE_TYPE.ONE.getCode(), "机电安装"),
        ONE_EIGHT(CERTIFICATE_TYPE.ONE.getCode(), "矿业"),
        ONE_NINE(CERTIFICATE_TYPE.ONE.getCode(), "铁路"),
        ONE_TEN(CERTIFICATE_TYPE.ONE.getCode(), "公路"),

        TWO_ONE(CERTIFICATE_TYPE.TWO.getCode(), "建筑工程"),
        TWO_TWO(CERTIFICATE_TYPE.TWO.getCode(), "公路工程"),
        TWO_THREE(CERTIFICATE_TYPE.TWO.getCode(), "水利水电工程"),
        TWO_FOUR(CERTIFICATE_TYPE.TWO.getCode(), "市政公用工程"),
        TWO_FIVE(CERTIFICATE_TYPE.TWO.getCode(), "矿业工程"),
        TWO_SIX(CERTIFICATE_TYPE.TWO.getCode(), "机电工程"),

        THREE_ONE(CERTIFICATE_TYPE.THREE.getCode(), "一级"),

        FOUR_ONE(CERTIFICATE_TYPE.FOUR.getCode(), "住建部"),
        FOUR_TWO(CERTIFICATE_TYPE.FOUR.getCode(), "水利部"),
        FOUR_THREE(CERTIFICATE_TYPE.FOUR.getCode(), "交通部"),

        FIVE_ONE(CERTIFICATE_TYPE.FIVE.getCode(), "一级"),

        SIX_ONE(CERTIFICATE_TYPE.SIX.getCode(), "岩土工程"),

        SEVEN_ONE(CERTIFICATE_TYPE.SEVEN.getCode(), "给排水工程"),
        SEVEN_TWO(CERTIFICATE_TYPE.SEVEN.getCode(), "暖通工程"),
        SEVEN_THREE(CERTIFICATE_TYPE.SEVEN.getCode(), "动力工程"),

        EIGHT_ONE(CERTIFICATE_TYPE.EIGHT.getCode(), "供配电工程"),

        NINE_ONE(CERTIFICATE_TYPE.NINE.getCode(), "一级"),

        TEN_ONE(CERTIFICATE_TYPE.TEN.getCode(), "高级A级"),
        TEN_TWO(CERTIFICATE_TYPE.TEN.getCode(), "高级B级"),
        TEN_THREE(CERTIFICATE_TYPE.TEN.getCode(), "中级C级"),
        TEN_FOUR(CERTIFICATE_TYPE.TEN.getCode(), "初级D级"),

        ELEVEN_ONE(CERTIFICATE_TYPE.ELEVEN.getCode(), "注册"),

        TWELVE_ONE(CERTIFICATE_TYPE.TWELVE.getCode(), "公路"),
        TWELVE_TWO(CERTIFICATE_TYPE.TWELVE.getCode(), "水运"),

        THIRTEEN_ONE(CERTIFICATE_TYPE.THIRTEEN.getCode(), "公路"),
        THIRTEEN_TWO(CERTIFICATE_TYPE.THIRTEEN.getCode(), "水运"),

        FOURTEEN_ONE(CERTIFICATE_TYPE.FOURTEEN.getCode(), "非执业会员");

        private String code;
        private String mess;

        PROFESSIONAL_NAME(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static List<Map<String, String>> getProfessionalNameList(String certificateType) {
            List<Map<String, String>> list = new ArrayList<>();
            LinkedHashMap<String, String> map = null;
            for (PROFESSIONAL_NAME as : values()) {
                if (as.getCode().equals(certificateType)) {
                    map = new LinkedHashMap<>();
                    map.put("professionalName", as.getMess());
                    list.add(map);
                }

            }
            return list;
        }
    }

    /**
     * 申诉详情类型
     *
     * <AUTHOR>
     */
    public enum EXAM_PAPER_TYPE {
        RANDOM("1", "随机组卷"),
        CUSTOM("2", "自定义组卷"),
        FIXED("3", "固定组卷");
        private String code;
        private String mess;

        EXAM_PAPER_TYPE(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (EXAM_PAPER_TYPE as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }


    public enum LOG_OPERATION_TYPE {
        I("新增"), U("修改"), D("删除"), IM("导入"), EX("导出"), R("重置");

        private String mess;

        LOG_OPERATION_TYPE(String mess) {
            this.mess = mess;
        }

        public String getMess() {
            return mess;
        }

        public static List<Map<String, Object>> getSelectList() {
            List<Map<String, Object>> list = new ArrayList<>();
            LinkedHashMap<String, Object> map = null;
            for (LOG_OPERATION_TYPE as : values()) {
                map = new LinkedHashMap<>();
                map.put("logOperationType", as.name());
                map.put("logOperationTypeText", as.getMess());
                list.add(map);
            }
            return list;
        }
    }

    public enum LOG_OPERATION_MODULE {
        OM1("用户组群"),
        OM2("用户组成员"),
        OM3("岗位属性"),
        OM4("课程类别"),
        OM5("课程"),
        OM6("课时"),
        OM7("试题类别"),
        OM8("试题库"),
        OM9("考试"),
        OM10("用户信息"),
        OM11("培训情况"),
        OM12("考试情况"),
        OM13("个人培训情况"),
        OM14("个人考试情况");

        private String mess;

        LOG_OPERATION_MODULE(String mess) {
            this.mess = mess;
        }

        public String getMess() {
            return mess;
        }

        public static List<Map<String, Object>> getSelectList() {
            List<Map<String, Object>> list = new ArrayList<>();
            LinkedHashMap<String, Object> map = null;
            for (LOG_OPERATION_MODULE as : values()) {
                map = new LinkedHashMap<>();
                map.put("logOperationModule", as.name());
                map.put("logOperationModuleText", as.getMess());
                list.add(map);
            }
            return list;
        }

    }

    public enum NOTIFY_MESSAGE {
        COMPULSION_COMPLAINT_UNTREATED(1, "强制交卷申诉待处理"),
        COMPULSION_COMPLAINT_PASS(2, "强制交卷申诉已通过"),
        COMPULSION_COMPLAINT_REFUSE(3, "强制交卷申诉已拒绝"),
        COMPULSION_COMPLAINT_TIMEOUT(4, "强制交卷申诉已超时"),
        DISPUTE_COMPLAINT_UNTREATED(5, "纠分申诉待处理"),
        DISPUTE_COMPLAINT_PROCESSED(6, "纠分申诉已处理"),
        DISTRIBUTION_COURSE(7, "分配课程"),
        CREATE_EXAM(8, "创建考试");


        private Integer code;
        private String name;

        NOTIFY_MESSAGE(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Map<Integer, String> getSelectList() {
            Map<Integer, String> map = new LinkedHashMap<>();
            for (NOTIFY_MESSAGE as : values()) {
                map.put(as.getCode(), as.getName());
            }
            return map;
        }

    }

    /**
     * 培训方式（0:不培训,1:规定课程,2:自选课程）
     *
     * <AUTHOR>
     */
    public enum FUNCTION_OPEN_TYPE {
        JOB_ATTR(1, "用户选择岗位属性"),
        MOCK_EXAM(2, "模拟考试");
        private Integer code;
        private String mess;

        FUNCTION_OPEN_TYPE(Integer code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public Integer getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (FUNCTION_OPEN_TYPE as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 经管职称
     *
     * <AUTHOR>
     * @since 2022-08-22
     */
    public enum PROFESSIONAL_TITLE_MANAGEMENT {
        Professorate_Senior_engineer("1", "正高级工程师"), Professorate_senior_economist("2", "正高级经济师"), senior_engineer("3",
            "高级工程师"), senior_economist("4", "高级经济师"), senior_political_engineer("5", "高级政工师"), engineer("6", "工程师"), economist(
            "7", "经济师"), political_engineer("8", "政工师"), assistant_engineer("9", "助理工程师"), assistant_economist("10",
            "助理经济师"), staff_level("11", "员级");

        String code;
        String name;

        PROFESSIONAL_TITLE_MANAGEMENT(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static List<Map<String, String>> getSelectList() {
            List<Map<String, String>> list = new ArrayList<>();
            LinkedHashMap<String, String> map = null;
            for (PROFESSIONAL_TITLE_MANAGEMENT as : values()) {
                map = new LinkedHashMap<>();
                map.put("professionalTitle", as.getCode());
                map.put("professionalTitleText", as.getName());
                list.add(map);
            }
            return list;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (PROFESSIONAL_TITLE_MANAGEMENT as : values()) {
                map.put(as.getCode(), as.getName());
            }
            return map;
        }

        public static String getMesses(String codes) { // 逗号分隔的职称码
            LinkedHashMap<String, String> map = getMap();
            if (StringUtils.isNotBlank(codes)) {
                return Arrays.asList(codes.split(",")).stream().map((s) -> map.get(s)).collect(Collectors.joining(","));
            }
            return "-";
        }
    }

    /**
     * 经管政治面貌
     *
     * <AUTHOR>
     * @since 2022-08-22
     */
    public enum POLITICAL_STATUS {
        PARTY_MEMBER("1", "中共党员"), PROBATIONARY_PARTY_MEMBER("2", "预备党员"), PUBLIC_PEOPLE("3", "群众"), OTHERS("4", "其他");

        String code;
        String name;

        POLITICAL_STATUS(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static List<Map<String, String>> getSelectList() {
            List<Map<String, String>> list = new ArrayList<>();
            LinkedHashMap<String, String> map = null;
            for (POLITICAL_STATUS as : values()) {
                map = new LinkedHashMap<>();
                map.put("professionalTitle", as.getCode());
                map.put("professionalTitleText", as.getName());
                list.add(map);
            }
            return list;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (POLITICAL_STATUS as : values()) {
                map.put(as.getCode(), as.getName());
            }
            return map;
        }

        public static String getMesses(String codes) { // 逗号分隔的职称码
            LinkedHashMap<String, String> map = getMap();
            if (StringUtils.isNotBlank(codes)) {
                return Arrays.asList(codes.split(",")).stream().map((s) -> map.get(s)).collect(Collectors.joining(","));
            }
            return "-";
        }
    }

    /**
     * 违规行为
     *
     * <AUTHOR>
     * @since 2022-10-20
     */
    public enum WRONG_ACTION {
        WRONG_ACTION_1("人脸识别失败", "人脸识别失败"), WRONG_ACTION_2("切换页面", "切换页面"), WRONG_ACTION_3("低头、抬头、摆头",
            "低头、抬头、摆头"), WRONG_ACTION_4("多个人脸", "多个人脸"), WRONG_ACTION_5("人脸非本人", "人脸非本人"), WRONG_ACTION_6("无人脸", "无人脸");

        String code;
        String name;

        WRONG_ACTION(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static List<Map<String, String>> getSelectList() {
            List<Map<String, String>> list = new ArrayList<>();
            LinkedHashMap<String, String> map = null;
            for (WRONG_ACTION as : values()) {
                map = new LinkedHashMap<>();
                map.put("key", as.getCode());
                map.put("value", as.getName());
                list.add(map);
            }
            return list;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (WRONG_ACTION as : values()) {
                map.put(as.getCode(), as.getName());
            }
            return map;
        }

        public static String getMesses(String codes) { // 逗号分隔的职称码
            LinkedHashMap<String, String> map = getMap();
            if (StringUtils.isNotBlank(codes)) {
                return Arrays.asList(codes.split(",")).stream().map((s) -> map.get(s)).collect(Collectors.joining(","));
            }
            return "-";
        }
    }
}
