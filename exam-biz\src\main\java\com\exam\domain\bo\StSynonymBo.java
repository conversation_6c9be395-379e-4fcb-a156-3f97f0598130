package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 同义词替换业务对象 st_synonym
 *
 * <AUTHOR>
 * @date 2023-11-14
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StSynonymBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 同义词名称，逗号分割
     */
    @NotBlank(message = "同义词名称，逗号分割不能为空", groups = {AddGroup.class, EditGroup.class})
    private String synonymName;

    /**
     * 同义词替换名称
     */
    @NotBlank(message = "同义词替换名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String replaceName;

    /**
     * 顺序
     */
    @NotNull(message = "顺序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sn;

    /**
     * 租户 ID
     */
    @NotNull(message = "租户 ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;


}
