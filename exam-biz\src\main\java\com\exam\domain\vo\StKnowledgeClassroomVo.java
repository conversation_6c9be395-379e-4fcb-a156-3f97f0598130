package com.exam.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 知识课堂视图对象 st_knowledge_classroom
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StKnowledgeClassroomVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 问题内容
     */
    @ExcelProperty(value = "问题内容")
    private String problem;

    /**
     * 答案解析
     */
    @ExcelProperty(value = "答案解析")
    private String analysis;

    /**
     * 领域
     */
    @ExcelProperty(value = "领域")
    private String domainCode;

    /**
     * 权限
     */
    @ExcelProperty(value = "权限")
    private String companyCode;

    /**
     * 是否删除(1.是；0.否)
     */
    @ExcelProperty(value = "是否删除(1.是；0.否)")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人id
     */
    @ExcelProperty(value = "创建人id")
    private Long createBy;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long updateBy;


}
