package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.page.TableDataInfo;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.helper.LoginHelper;
import com.exam.domain.bo.StQuestionGenreBo;
import com.exam.domain.vo.StQuestionGenreVo;
import com.exam.service.IStQuestionGenreService;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 试题类型
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/questionGenre")
public class StQuestionGenreController extends BaseController {

    @Autowired
    private final IStQuestionGenreService iStQuestionGenreService;

    /**
     * 查询试题类型列表
     */
    @SaCheckPermission("exam:questionGenre:list")
    @GetMapping("/list")
    public TableDataInfo<StQuestionGenreVo> list(StQuestionGenreBo bo, PageQuery pageQuery) {
        return iStQuestionGenreService.queryPageList(bo, pageQuery);
    }

//    /**
//     * 导出试题类型列表
//     */
//    @SaCheckPermission("exam:questionGenre:export")
//    @Log(title = "试题类型", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(StQuestionGenreBo bo, HttpServletResponse response) {
//        List<StQuestionGenreVo> list = iStQuestionGenreService.queryList(bo);
//        ExcelUtil.exportExcel(list, "试题类型", StQuestionGenreVo.class, response);
//    }

    /**
     * 获取试题类型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:questionGenre:query")
    @GetMapping("/{id}")
    @ResponseBody
    public R<StQuestionGenreVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStQuestionGenreService.queryById(id));
    }

    /**
     * 新增试题类型
     */
    @SaCheckPermission("exam:questionGenre:add")
    @Log(title = "试题类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StQuestionGenreBo bo) {
        return toAjax(iStQuestionGenreService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改试题类型
     */
    @SaCheckPermission("exam:questionGenre:edit")
    @Log(title = "试题类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StQuestionGenreBo bo) {
        return toAjax(iStQuestionGenreService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除试题类型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:questionGenre:remove")
    @Log(title = "试题类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable Long[] ids) {
        return toAjax(iStQuestionGenreService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }


    /**
     * 查询试题类型（无参数）
     */
    @GetMapping(value = "/selectQuestionGenre")
    @Log(title = "试题类型", businessType = BusinessType.OTHER)
    @ResponseBody
    public R<List<StQuestionGenreVo>> selectQuestionGenre() {
        Map conditionParam = new HashMap<String, String>();
        return R.ok(iStQuestionGenreService.selectQuestionGenre(conditionParam));
    }


    /**
     * 查询各类型试题个数
     *
     * @param questionTypeIds 试题类别 ID 集合
     * @return 试题类型及该类型试题个数的映射
     */
    @GetMapping(value = "/selectQuestionGenreCount")
    @ResponseBody
    public R<Map<Integer, Integer>> selectQuestionGenreCount(String questionTypeIds) {

        Map<String, String> conditionParam = new HashMap<>();
        conditionParam.put("questionTypeIds", questionTypeIds);
        Map<Integer, Integer> retMap = iStQuestionGenreService.selectQuestionGenreCount(conditionParam);

        return R.ok(retMap);
    }
}
