package com.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.R;
import com.exam.common.helper.LoginHelper;
import com.exam.domain.StGroup;
import com.exam.domain.vo.StGroupVo;
import com.exam.service.IStGroupService;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 组群
 *
 * <AUTHOR>
 * @date 2023-11-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/group")
public class StGroupController extends BaseController {

    @Resource
    private final IStGroupService iStGroupService;

//    /**
//     * 查询组群列表
//     */
//    @SaCheckPermission("exam:group:list")
//    @GetMapping("/list")
//    public TableDataInfo<StGroupVo> list(StGroupBo bo, PageQuery pageQuery) {
//        return iStGroupService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出组群列表
//     */
//    @SaCheckPermission("exam:group:export")
//    @Log(title = "组群", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(StGroupBo bo, HttpServletResponse response) {
//        List<StGroupVo> list = iStGroupService.queryList(bo);
//        ExcelUtil.exportExcel(list, "组群", StGroupVo.class, response);
//    }
//
//    /**
//     * 获取组群详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("exam:group:query")
//    @GetMapping("/{id}")
//    public R<StGroupVo> getInfo(@NotNull(message = "主键不能为空")
//    @PathVariable Long id) {
//        return R.ok(iStGroupService.queryById(id));
//    }
//
//    /**
//     * 新增组群
//     */
//    @SaCheckPermission("exam:group:add")
//    @Log(title = "组群", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody StGroupBo bo) {
//        return toAjax(iStGroupService.insertByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 修改组群
//     */
//    @SaCheckPermission("exam:group:edit")
//    @Log(title = "组群", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StGroupBo bo) {
//        return toAjax(iStGroupService.updateByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 删除组群
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("exam:group:remove")
//    @Log(title = "组群", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//    @PathVariable Long[] ids) {
//        return toAjax(iStGroupService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
//    }


//    /**
//     * 查询三类人员组群
//     *
//     * @return 组群信息
//     */
//    @GetMapping(value = "/seleteSanLeiGroups")
//    @ResponseBody
//    public R<List<StGroupVo>> seleteSanLeiGroups() {
//        Map conditionParam = new HashMap<String, String>();
//        List<StGroupVo> stGroups = iStGroupService.seleteSanLeiGroups(conditionParam);
//        return R.ok(stGroups);
//    }


    @GetMapping(value = "/getGroupInfo")
    @ResponseBody
    public R<IPage<StGroupVo>> getGroupInfo(@RequestParam(value = "groupName", required = false) String groupName,
        @DateTimeFormat(iso = ISO.DATE) @RequestParam(value = "createTime", required = false) Date createTime,
        @RequestParam(value = "size", required = false) Integer size,
        @RequestParam(value = "current", required = false) Integer current) {

        StGroup stGroup = new StGroup();
        stGroup.setGroupName(groupName);
        stGroup.setCreateTime(createTime);
        stGroup.setProjectId(LoginHelper.getProjectId());
        stGroup.setTenantId(LoginHelper.getTenantId());

        IPage<StGroupVo> list = iStGroupService.getGroupInfo(new Page<>(current, size), stGroup);

        return R.ok(list);
    }
}
