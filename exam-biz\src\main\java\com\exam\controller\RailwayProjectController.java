package com.exam.controller;

import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.domain.RailwayProject;
import com.exam.service.RailwayProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 企业管理-子公司或项目管理
 *
 * @ClassName: railwayProjectController
 * @Description: 企业管理-子公司或项目管理
 * @Author: Xmj
 * @Date: 2025/5/20 17:22
 * @Version: 1.0
 */
@RequestMapping("/railwayProject")
@RestController
@Api(tags = "企业管理-子公司或项目管理")
public class RailwayProjectController extends BaseController {

    @Autowired
    private RailwayProjectService projectService;


    @Log(title = "企业管理", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增子公司或项目")
    @PostMapping(value = "/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody RailwayProject project) {
        projectService.add(project);
        return R.ok();
    }


    @Log(title = "企业管理", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "根据子公司或项目Id rpId编辑")
    @PostMapping(value = "/editById")
    public R<Void> editById(@Validated(EditGroup.class) @RequestBody RailwayProject project) {
        projectService.editById(project);
        return R.ok();
    }


    @Log(title = "企业管理", businessType = BusinessType.DELETE)
    @ApiOperation(value = "根据子公司或项目Id rpId删除")
    @GetMapping(value = "/delByRpId")
    @ApiImplicitParam(name = "rpId", value = "子公司或项目Id", dataTypeClass = Long.class, paramType = "query")
    public R<Void> delByRpId(Long rpId) {
        projectService.removeById(rpId);
        return R.ok();
    }

    @ApiOperation(value = "根据企业Id rfId获取子公司列表")
    @GetMapping(value = "/queryByRfId")
    @ApiImplicitParam(name = "rfId", value = "企业Id", dataTypeClass = Long.class, paramType = "query")
    public R<List<RailwayProject>> queryByRfId(Long rfId) {
        List<RailwayProject> projectList = projectService.queryByRfId(rfId);
        return R.ok(projectList);
    }

}
