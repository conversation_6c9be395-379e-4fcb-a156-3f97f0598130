package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 人脸对比成功比例视图对象 face_ratio_dictionary
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class FaceRatioDictionaryVo {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 人脸对比成功比例
     */
    @ExcelProperty(value = "人脸对比成功比例")
    private Long radio;

    /**
     * 系统名称
     */
    @ExcelProperty(value = "系统名称")
    private String app;

    /**
     * 系统描述
     */
    @ExcelProperty(value = "系统描述")
    private String appDesc;


}
