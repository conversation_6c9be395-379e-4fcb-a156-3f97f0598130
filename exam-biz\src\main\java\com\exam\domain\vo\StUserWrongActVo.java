package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 用户组群关系视图对象 st_user_wrong_act
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@Data
@ExcelIgnoreUnannotated
public class StUserWrongActVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 答题卡id
     */
    @ExcelProperty(value = "答题卡id")
    private Long answerCardId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户姓名
     */
    @ExcelProperty(value = "用户姓名")
    private String userName;

    /**
     * 违规行为
     */
    @ExcelProperty(value = "违规行为")
    private String wrongAction;

    /**
     * 违规编号
     */
    @ExcelProperty(value = "违规编号")
    private Integer code;

    /**
     * 是否提醒
     */
    @ExcelProperty(value = "是否提醒")
    private Long isRemind;

    /**
     * 是否强制提交
     */
    @ExcelProperty(value = "是否强制提交")
    private Long isForceSubmit;

    /**
     * 考试id
     */
    @ExcelProperty(value = "考试id")
    private Long examId;

    /**
     * 身份证正面照片地址
     */
    @ExcelProperty(value = "身份证正面照片地址")
    private String idImageUrl;

    /**
     * 错误图片base64编码
     */
    @ExcelProperty(value = "错误图片base64编码")
    private String errorPicBase;

    /**
     * 人脸识别错误编码
     */
    @ExcelProperty(value = "人脸识别错误编码")
    private String errorCode;

    /**
     * 比对结果置信度
     */
    @ExcelProperty(value = "比对结果置信度")
    private String confidence;

    /**
     * 补考次数
     */
    @ExcelProperty(value = "补考次数")
    private String retestTimes;

    /**
     * 提醒内容
     */
    @ExcelProperty(value = "提醒内容")
    private String remindContent;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long projectId;

    /**
     * 考试名称
     */
    @ExcelProperty(value = "考试名称")
    private String examName;


}
