package com.exam.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.exam.common.core.domain.entity.SysUser;
import com.exam.common.enums.RailwayEnums;
import com.exam.common.helper.LoginHelper;
import com.exam.common.utils.StringUtils;
import com.exam.domain.RailwayFirm;
import com.exam.domain.RailwayFirmApprove;
import com.exam.domain.RailwayFirmApproveDto;
import com.exam.mapper.RailwayFirmApproveMapper;
import com.exam.mapper.RailwayFirmMapper;
import com.exam.service.RailwayFirmApproveService;
import com.exam.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class RailwayFirmApproveServiceImpl extends ServiceImpl<RailwayFirmApproveMapper, RailwayFirmApprove> implements RailwayFirmApproveService{

    @Autowired
    private RailwayFirmMapper firmMapper;
    @Autowired
    private ISysUserService userService;


    @Override
    public void approve(RailwayFirmApproveDto firmApproveDto) {
        List<RailwayFirmApprove> approveList = list(new LambdaQueryWrapper<RailwayFirmApprove>()
            .in(RailwayFirmApprove::getRfId, firmApproveDto.getRfIds()));
        approveList.forEach(fa-> {
            fa.setRfaCause(firmApproveDto.getRfaCause());
            fa.setRfaApprove(firmApproveDto.getRfaApprove());
            fa.setRfaTime(LocalDateTime.now());
            fa.setRfaApprover(LoginHelper.getUserId());
        });
        saveBatch(approveList);
        Date date = new Date();
        if (RailwayEnums.RFA_APPROVE.PASS.getCode().equals(firmApproveDto.getRfaApprove())) {
            List<RailwayFirm> firmList = firmMapper.selectList(new LambdaQueryWrapper<RailwayFirm>().in(RailwayFirm::getRfId, firmApproveDto.getRfIds()));
            firmList.forEach(f-> {
                SysUser sysUser = new SysUser();
                sysUser.setUserName(f.getRfPhone());
                String password = f.getRfPhone() + StringUtils.randomLetter(6);
                sysUser.setTenantId(f.getRfId());
                sysUser.setNickName(f.getRfContact());
                sysUser.setUserType("sys_user");
                sysUser.setPhonenumber(f.getRfPhone());
                sysUser.setPassword(BCrypt.hashpw(password));
                sysUser.setCreateBy(LoginHelper.getUserId());
                sysUser.setCreateTime(date);
                sysUser.setUpdateBy(LoginHelper.getUserId());
                sysUser.setUpdateTime(date);
                sysUser.setFirmType(2);
                sysUser.setIdNumber(f.getRfIdCard());
                Long[] roleIds = {3L};
                sysUser.setRoleIds(roleIds);
                userService.insertUser(sysUser);
                //todo 需要添加短信发送通知用户账号密码信息
            });
        }
    }
}
