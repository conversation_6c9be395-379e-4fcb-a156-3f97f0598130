package com.exam.domain.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;

import com.exam.common.core.domain.BaseEntity;
import com.exam.utils.GyUtils;
import com.exam.utils.ReadFileUtil;
import com.exam.utils.SpringContextUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户课程类别列表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-18
 */
@Data
//@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
@ApiModel(value="UserCourseCategoryVO", description="用户课程类别信息")
public class UserCourseCategoryVO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "类别id")
    private String categoryId;
    @ApiModelProperty(value = "父id")
    private String fatherId;
    @ApiModelProperty(value = "分类层级")
    private Integer level;
    @ApiModelProperty(value = "类别名称")
    private String categoryName;
    @ApiModelProperty(value = "封面图片路径")
    private String picPath;

    @ApiModelProperty(value = "封面图片全路径")
    private String fullPicPath;

    @ApiModelProperty(value = "课程数")
    private int courseCount;

    @ApiModelProperty(value = "本身的课程数（判断开始学习是否可用）")
    private int courseCountOwn;

    @ApiModelProperty(value = "课时数")
    private int classhourCount;
    @ApiModelProperty(value = "总时长")
    private long durationTotal;
    @ApiModelProperty(value = "播放总时长")
    private long playDurationTotal;
    @ApiModelProperty(value = "总进度")
    private int progresTotal;

    @ApiModelProperty(value = "课程集合")
    private List<StCourseVo> StCourseVoList;

    @ApiModelProperty(value = "课程集合")
    private IPage<StCourseVo> StCourseVoPage;

    @ApiModelProperty(value = "当前学习视频")
    private StClasshourVo currClasshour;
    @ApiModelProperty(value = "二级课程类别")
    private List<UserCourseCategoryVO> children;
    public String getFullPicPath() {
        if(GyUtils.isNull(picPath)){
            return "";
        }
        return SpringContextUtil.getProperty("train.image.server")+picPath;
    }

    public String getDurationTotalDis() {
        return ReadFileUtil.secondToTime(durationTotal);
    }
    public String getProgresTotalDis() {
        if(durationTotal!=0){
            int progresTotal = (int)(playDurationTotal*100/durationTotal);
//            if(progresTotal >= 99){
//                progresTotal = 100;
//            }
            return progresTotal+"%";
        }
        return "0%";
    }

}
