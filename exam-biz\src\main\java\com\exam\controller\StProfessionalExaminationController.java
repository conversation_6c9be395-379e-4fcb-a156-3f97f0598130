package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StProfessionalExaminationBo;
import com.exam.domain.vo.StProfessionalExaminationVo;
import com.exam.service.IStProfessionalExaminationService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 职业考试
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/professionalExamination")
public class StProfessionalExaminationController extends BaseController {

    private final IStProfessionalExaminationService iStProfessionalExaminationService;

    /**
     * 查询职业考试列表
     */
    @SaCheckPermission("exam:professionalExamination:list")
    @GetMapping("/list")
    public TableDataInfo<StProfessionalExaminationVo> list(StProfessionalExaminationBo bo, PageQuery pageQuery) {
        return iStProfessionalExaminationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出职业考试列表
     */
    @SaCheckPermission("exam:professionalExamination:export")
    @Log(title = "职业考试", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StProfessionalExaminationBo bo, HttpServletResponse response) {
        List<StProfessionalExaminationVo> list = iStProfessionalExaminationService.queryList(bo);
        ExcelUtil.exportExcel(list, "职业考试", StProfessionalExaminationVo.class, response);
    }

    /**
     * 获取职业考试详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:professionalExamination:query")
    @GetMapping("/{id}")
    public R<StProfessionalExaminationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStProfessionalExaminationService.queryById(id));
    }

    /**
     * 新增职业考试
     */
    @SaCheckPermission("exam:professionalExamination:add")
    @Log(title = "职业考试", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StProfessionalExaminationBo bo) {
        return toAjax(iStProfessionalExaminationService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改职业考试
     */
    @SaCheckPermission("exam:professionalExamination:edit")
    @Log(title = "职业考试", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StProfessionalExaminationBo bo) {
        return toAjax(iStProfessionalExaminationService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除职业考试
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:professionalExamination:remove")
    @Log(title = "职业考试", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStProfessionalExaminationService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
