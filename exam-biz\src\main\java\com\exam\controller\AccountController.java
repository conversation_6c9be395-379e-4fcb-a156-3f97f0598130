package com.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.annotation.Log;
import com.exam.common.core.domain.R;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.vo.ExamStatisticsVo;
import com.exam.domain.vo.ExamUserStatisticsVoOne;
import com.exam.domain.vo.ExamUserStatisticsVoTwo;
import com.exam.service.AccountService;
import com.exam.utils.GyUtils;
import com.exam.utils.MapKeyComparator;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@ApiSort(value = 7)
@RequestMapping("AccountController")
@RestController
@Api(tags = "AccountController(统计数据-图表)")
public class AccountController {
    private static final Logger logger = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    AccountService accountService;


    @ApiOperationSupport(order = 1)
    @GetMapping(value = "/getTrainUserCount")
    @ApiOperation(value = "7.1 本月及上月参加培训用户数统计")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<Map<String, Object>>> getTrainUserCount(@RequestParam(value = "dateTime", required = false) String dateTime,
                                                          @RequestParam(value = "projectId", required = false) String projectId) {
        return R.ok(accountService.getTrainUserCount(dateTime, projectId));
    }

    @ApiOperationSupport(order = 2)
    @GetMapping(value = "/getExamUserCount")
    @ApiOperation(value = "7.2 本月及上月参加考试用户数统计")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<Map<String, Object>>> getExamUserCount(@RequestParam(value = "dateTime", required = false) String dateTime,
                                                                  @RequestParam(value = "projectId", required = false) String projectId) {
        return R.ok(accountService.getExamUserCount(dateTime, projectId));
    }

    @ApiOperationSupport(order = 3)
    @GetMapping(value = "/getPassExamUserCount")
    @ApiOperation(value = "7.3 本月及上月通过考试用户数统计")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<Map<String, Object>>> getPassExamUserCount(@RequestParam(value = "dateTime", required = false) String dateTime,
                                                                      @RequestParam(value = "projectId", required = false) String projectId) {
        return R.ok(accountService.getPassExamUserCount(dateTime, projectId));
    }

    @ApiOperationSupport(order = 4)
    @GetMapping(value = "/getFinishTrainUserCount")
    @ApiOperation(value = "7.4 本月及上月完成培训用户数统计")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<Map<String, Object>>> getFinishTrainUserCount(@RequestParam(value = "dateTime", required = false) String dateTime,
                                                                         @RequestParam(value = "projectId", required = false) String projectId) {
        return R.ok(accountService.getFinishTrainUserCount(dateTime,projectId));
    }

    @ApiOperationSupport(order = 5)
    @GetMapping(value = "/trainAndExamPassLine")
    @ApiOperation(value = "7.5 培训及考试合格情况折线图")
    @ApiImplicitParams({@ApiImplicitParam(name = "dateTime", value = "dateTime", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "projectId", value = "projectId", paramType = "query", required = false, dataType = "String")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<Map<String, Object>>> trainAndExamPassLine(@RequestParam(value = "dateTime", required = false) String dateTime,
                                                                      @RequestParam(value = "projectId", required = false) String projectId) {
        Map<String, Object> map = new HashMap<>();
        //判断传入的是 x年还是x年x月，2020年4位，202107 6位
        //pbkey,1 年,2 月
        if (GyUtils.isNotNull(dateTime)) {
            map.put("dateTime", dateTime);
            if (dateTime.length() == 4) {
                map.put("pbkey", "1");
            } else if (dateTime.length() == 6) {
                map.put("pbkey", "2");
            }
        }
        List<Map<String, Object>> list = accountService.trainAndExamPassLine(map);
        Map<String, List<Map<String, Object>>> resultMap = GyUtils.getCollectionIndexByKey(list, "timet");
        Iterator<Map.Entry<String, List<Map<String, Object>>>> it = resultMap.entrySet().iterator();
        Map<String, List<Map<String, Object>>> resultMap1 = new HashMap<>();
        while (it.hasNext()) {
            Map.Entry<String, List<Map<String, Object>>> entry = it.next();
//            System.out.println("key:"+entry.getKey()+" "
//                    +"Value:"+entry.getValue());
            List<Map<String, Object>> gzList = entry.getValue();
            Map<String, Object> mapGz = new HashMap<>();
            //判断gzList大小，2不用构造，既有考试也有培训，1查一下差哪个
            if (gzList.size() == 1) {
                String key = gzList.get(0).get("content").toString();
                if ("培训完成".equals(key)) {//构造考试合格的为0
                    mapGz.put("total", 0);
                    mapGz.put("timet", entry.getKey());
                    mapGz.put("content", "考试合格");

                } else {
                    mapGz.put("total", 0);
                    mapGz.put("timet", entry.getKey());
                    mapGz.put("content", "培训完成");
                }
            }
            gzList.add(mapGz);
            resultMap1.put(entry.getKey(), gzList);

        }
        Map<String, List<Map<String, Object>>> sortMap = new TreeMap<String, List<Map<String, Object>>>(
                new MapKeyComparator());
        sortMap.putAll(resultMap1);
        List<Map<String, Object>> list1 = new ArrayList<>();
        Iterator<Map.Entry<String, List<Map<String, Object>>>> it1 = sortMap.entrySet().iterator();
        while (it1.hasNext()) {
            Map.Entry<String, List<Map<String, Object>>> entry = it1.next();
//            System.out.println("key:"+entry.getKey()+" "
//                    +"Value:"+entry.getValue());
            list1.addAll(entry.getValue());
        }

        return R.ok(list1);
    }

    @ApiOperationSupport(order = 6)
    @GetMapping(value = "/passExamPie")
    @ApiOperation(value = "7.6 考试合格情况饼图")
    @ApiImplicitParams({@ApiImplicitParam(name = "dateTime", value = "dateTime", paramType = "query", required = false, dataType = "String"),
    @ApiImplicitParam(name = "projectId", value = "projectId", paramType = "query", required = false, dataType = "String")
            })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<Map<String, Object>>> passExamPie(@RequestParam(value = "dateTime", required = false) String dateTime,
                                                             @RequestParam(value = "projectId", required = false) String projectId) {
        logger.info("passExamPie--------------------------------------------------执行");
        Map<String, Object> map = new HashMap<>();
        List<Map<String,Object>> result = accountService.passExamPie(map);
        double allCountns = result.stream().mapToDouble(s->((Long)s.get("countns")).doubleValue()).sum();
        result.stream().forEach(p->p.put("proportion",String.format("%.2f", (((Long)p.get("countns")).doubleValue()/(allCountns==0?1:allCountns)*100))));
        return R.ok(result);
    }

    @ApiOperationSupport(order = 7)
    @GetMapping(value = "/trainFinishPie")
    @ApiOperation(value = "7.7 培训完成饼图")
    @ApiImplicitParams({@ApiImplicitParam(name = "dateTime", value = "dateTime", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "projectId", value = "projectId", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "area", value = "area", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "project", value = "project", paramType = "query", required = false, dataType = "String")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<Map<String, Object>>> trainFinishPie(@RequestParam(value = "dateTime", required = false) String dateTime,
                                                                @RequestParam(value = "projectId", required = false) String projectId) {
        Map<String, Object> map = new HashMap<>();
        List<Map<String,Object>> result = accountService.trainFinishPie(map);
        double allCountns = result.stream().mapToDouble(s->((Long)s.get("countns")).doubleValue()).sum();
        result.stream().forEach(p->p.put("proportion",String.format("%.2f", (((Long)p.get("countns")).doubleValue()/(allCountns==0?1:allCountns)*100))));
        return R.ok(result);
    }


    @ApiOperationSupport(order = 9)
    @GetMapping(value = "/avgTrainBar")
    @ApiOperation(value = "7.9 平均培训进度柱状图")
    @ApiImplicitParams({@ApiImplicitParam(name = "cxType", value = "cxType", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "dateTime", value = "dateTime", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "projectId", value = "projectId", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "area", value = "area", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "current", value = "当前页", paramType = "query", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "size", value = "每页数量", paramType = "query", required = true, dataType = "Integer")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<IPage<Map<String, Object>>> avgTrainBar(@RequestParam(value = "cxType", required = false) String cxType,
                                                              @RequestParam(value = "dateTime", required = false) String dateTime,
                                                              @RequestParam(value = "projectId", required = false) String projectId,
                                                              @RequestParam(value = "size", required = false) Integer size,
                                                              @RequestParam(value = "current", required = false) Integer current) {
        Map<String, Object> map = new HashMap<>();
        if (GyUtils.isNotNull(dateTime)) {
            map.put("dateTime", dateTime);
            if (dateTime.length() == 4) {//年
                map.put("pbkey", "1");
            } else if (dateTime.length() == 6) {//月
                map.put("pbkey", "2");
            }
        }


        return R.ok(accountService.avgTrainBar(new Page<>(current, size), map));
    }


    @ApiOperationSupport(order = 10)
    @GetMapping(value = "/avgScoreBar")
    @ApiOperation(value = "7.10 平均成绩柱状图")
    @ApiImplicitParams({@ApiImplicitParam(name = "cxType", value = "cxType", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "dateTime", value = "dateTime", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "projectId", value = "projectId", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "current", value = "当前页", paramType = "query", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "size", value = "每页数量", paramType = "query", required = true, dataType = "Integer")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<IPage<Map<String, Object>>> avgScoreBar(@RequestParam(value = "cxType", required = false) String cxType,
                                                              @RequestParam(value = "dateTime", required = false) String dateTime,
                                                              @RequestParam(value = "projectId", required = false) String projectId,
                                                              @RequestParam(value = "size", required = false) Integer size,
                                                              @RequestParam(value = "current", required = false) Integer current) {

        logger.info("avgScoreBar----------------------------------------执行");
        Map<String, Object> map = new HashMap<>();
        if (GyUtils.isNotNull(dateTime)) {
            map.put("dateTime", dateTime);
            if (dateTime.length() == 4) {//年
                map.put("pbkey", "1");
            } else if (dateTime.length() == 6) {//月
                map.put("pbkey", "2");
            }
        }
        return R.ok(accountService.avgScoreBar(new Page<>(current, size), map));
    }


    /**
     * (国铁局)考试人员成绩统计(按照考试id区分)
     * @param tenantName
     * @param examStatus
     * @param examId
     * @param size
     * @param current
     * @return
     */
    @GetMapping(value = "/getExamUserStatisticsOne")
    @ResponseBody
    public R<IPage<ExamUserStatisticsVoOne>> getExamUserStatisticsOne(@RequestParam(value = "tenantName", required = false) String tenantName,
                                                                      @RequestParam(value = "examStatus", required = false) String examStatus,
                                                                      @RequestParam(value = "examId", required = true) String examId,
                                                                      @RequestParam(value = "size", required = false) Integer size,
                                                                      @RequestParam(value = "current", required = false) Integer current){
        Map<String, Object> map = new HashMap<>();
        map.put("examName", tenantName);
        map.put("examId", examId);
        map.put("examStatus", examStatus);
        return R.ok(accountService.getExamUserStatisticsOne(new Page<>(current, size), map));
    }

    /**
     * (国铁局)考试人员成绩统计导出(按照考试id区分)
     * @param tenantName
     * @param examStatus
     * @param examId
     * @return
     */
    @Log(title = "查询统计", businessType = BusinessType.EXPORT)
    @GetMapping(value = "/examUserStatisticsExportOne")
    @ResponseBody
    public R<Void> examUserStatisticsExportOne(@RequestParam(value = "tenantName", required = false) String tenantName,
                                         @RequestParam(value = "examStatus", required = false) String examStatus,
                                         @RequestParam(value = "examId", required = true) String examId, HttpServletResponse response){
        Map<String, Object> map = new HashMap<>();
        map.put("examName", tenantName);
        map.put("examId", examId);
        map.put("examStatus", examStatus);
        List<ExamUserStatisticsVoOne> list = accountService.getExamUserStatisticsOne(map);
        ExcelUtil.exportExcel(list, "考试人员成绩统计", ExamUserStatisticsVoOne.class, response);
        return R.ok();
    }
    /**
     * (国铁局)考试情况统计表
     * @param examName
     * @param examStatus
     * @param size
     * @param current
     * @return
     */
    @GetMapping(value = "/getExamStatisticsOne")
    @ResponseBody
    public R<IPage<ExamStatisticsVo>> getExamStatisticsOne(@RequestParam(value = "examName", required = false) String examName,
                                                  @RequestParam(value = "examStatus", required = false) String examStatus,
                                                  @RequestParam(value = "size", required = false) Integer size,
                                                  @RequestParam(value = "current", required = false) Integer current){
        Map<String, Object> map = new HashMap<>();
        map.put("examName", examName);
        map.put("examStatus", examStatus);
        return R.ok(accountService.getExamStatisticsOne(new Page<>(current, size), map));
    }

    /**
     * (企业)考试人员成绩统计(根据考试id)
     * @param projectName
     * @param examStatus
     * @param examId
     * @param size
     * @param current
     * @return
     */
    @GetMapping(value = "/getExamUserStatisticsTwo")
    @ResponseBody
    public R<IPage<ExamUserStatisticsVoTwo>> getExamUserStatisticsTwo(@RequestParam(value = "projectName", required = false) String projectName,
                                                                      @RequestParam(value = "examStatus", required = false) String examStatus,
                                                                      @RequestParam(value = "examId", required = true) String examId,
                                                                      @RequestParam(value = "size", required = false) Integer size,
                                                                      @RequestParam(value = "current", required = false) Integer current){
        Map<String, Object> map = new HashMap<>();
        map.put("projectName", projectName);
        map.put("examId", examId);
        map.put("examStatus", examStatus);
        return R.ok(accountService.getExamUserStatisticsTwo(new Page<>(current, size), map));
    }

    /**
     * (企业)考试人员成绩统计导出(按照考试区分)
     * @param projectName
     * @param examStatus
     * @param examId
     * @return
     */
    @Log(title = "查询统计", businessType = BusinessType.EXPORT)
    @GetMapping(value = "/examUserStatisticsExportTwo")
    @ResponseBody
    public R<Void> examUserStatisticsExport(@RequestParam(value = "projectName", required = false) String projectName,
                                         @RequestParam(value = "examStatus", required = false) String examStatus,
                                         @RequestParam(value = "examId", required = true) String examId, HttpServletResponse response){
        Map<String, Object> map = new HashMap<>();
        map.put("projectName", projectName);
        map.put("examId", examId);
        map.put("examStatus", examStatus);
        List<ExamUserStatisticsVoTwo> list = accountService.getExamUserStatisticsTwo(map);
        ExcelUtil.exportExcel(list, "考试人员成绩统计", ExamUserStatisticsVoTwo.class, response);
        return R.ok();
    }
    /**
     * (企业)考试情况统计表
     * @param examName
     * @param examStatus
     * @param size
     * @param current
     * @return
     */
    @GetMapping(value = "/getExamStatisticsTwo")
    @ResponseBody
    public R<IPage<ExamStatisticsVo>> getExamStatisticsTwo(@RequestParam(value = "examName", required = false) String examName,
                                                  @RequestParam(value = "examStatus", required = false) String examStatus,
                                                  @RequestParam(value = "size", required = false) Integer size,
                                                  @RequestParam(value = "current", required = false) Integer current){
        Map<String, Object> map = new HashMap<>();
        map.put("examName", examName);
        map.put("examStatus", examStatus);
        return R.ok(accountService.getExamStatisticsTwo(new Page<>(current, size), map));
    }


    @ApiOperationSupport(order = 11)
    @GetMapping(value = "/getExamInfo")
    @ApiOperation(value = "7.11 考试情况统计表")
    @ApiImplicitParams({@ApiImplicitParam(name = "groupIds", value = "组群名称", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "certificateType", value = "持证类型", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "userName", value = "用户名", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "examNames", value = "考试名称", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "achievemenLevel", value = "成绩等级", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "scoreBegin", value = "成绩范围开始", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "scoreEnd", value = "成绩范围结束", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "jobId", value = "岗位", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "deptName", value = "部门", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "jobName", value = "岗位", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "year", value = "年份", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "scoreOne", value = "分值1", paramType = "query", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "scoreTwo", value = "分值2", paramType = "query", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "courseSource", value = "课程来源", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "isMissExam", value = "是否缺考(1：是，0：不是)", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "isForced", value = "是否强制交卷(1.是；0.否)", paramType = "query", required = false, dataType = "Integer"),
            @ApiImplicitParam(name = "jobAttrId", value = "岗位属性", paramType = "query", required = false, dataType = "Long"),
            @ApiImplicitParam(name = "current", value = "当前页", paramType = "query", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "size", value = "每页数量", paramType = "query", required = true, dataType = "Integer")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<IPage<Map<String, Object>>> getExamInfo(@RequestParam(value = "groupIds", required = false) String groupIds,
                                                              @RequestParam(value = "certificateType", required = false) String certificateType,
                                                              @RequestParam(value = "userName", required = false) String userName,
                                                              @RequestParam(value = "examNames", required = false) String examNames,
                                                              @RequestParam(value = "achievemenLevel", required = false) String achievemenLevel,
                                                              @RequestParam(value = "scoreBegin", required = false) String scoreBegin,
                                                              @RequestParam(value = "scoreEnd", required = false) String scoreEnd,
                                                              @RequestParam(value = "projectId", required = false) String projectId,
                                                              @RequestParam(value = "deptId", required = false) String deptId,
                                                              @RequestParam(value = "tenentId", required = false) String tenentId,
                                                              @RequestParam(value = "jobId", required = false) String jobId,
                                                              @RequestParam(value = "deptName", required = false) String deptName,
                                                              @RequestParam(value = "jobName", required = false) String jobName,
                                                              @RequestParam(value = "year", required = false) String year,
                                                              @RequestParam(value = "scoreOne", required = false) Integer scoreOne,
                                                              @RequestParam(value = "scoreTwo", required = false) Integer scoreTwo,
                                                              @RequestParam(value = "courseSource", required = false) String courseSource,
                                                              @RequestParam(value = "isMissExam", required = false) Integer isMissExam,
                                                              @RequestParam(value = "isForced", required = false) Integer isForced,
                                                              @RequestParam(value = "size", required = false) Integer size,
                                                              @RequestParam(value = "current", required = false) Integer current) {

        Map<String, Object> map = new HashMap<>();
        if(GyUtils.isNotNull(groupIds)){
            List<String > groupIdList = Arrays.asList(groupIds.split(",")).stream().distinct().collect(Collectors.toList());
            map.put("groupIdList", groupIdList);
        }
        if(GyUtils.isNotNull(examNames)){
            List<String > examNameList = Arrays.asList(examNames.split(",")).stream().distinct().collect(Collectors.toList());
            map.put("examNameList", examNameList);
        }

        if(scoreOne != null && scoreTwo != null && scoreOne.intValue() > scoreTwo.intValue()) {
            Integer temp = scoreOne;
            scoreOne = scoreTwo;
            scoreTwo = temp;
        }

        map.put("groupIds", groupIds);
        map.put("certificateType", certificateType);
        map.put("userName", userName);
        map.put("achievemenLevel", achievemenLevel);
        map.put("scoreBegin", scoreBegin);
        map.put("scoreEnd", scoreEnd);
        map.put("deptId", deptId);
        map.put("tenentId", tenentId);
        map.put("jobId", jobId);
        map.put("deptName", deptName);
        map.put("jobName", jobName);
        map.put("year", year);
        map.put("courseSource", courseSource);
        map.put("examNames", examNames);
        map.put("isMissExam", isMissExam);
        map.put("isForced", isForced);
        map.put("scoreOne", scoreOne);
        map.put("scoreTwo", scoreTwo);
        return R.ok(accountService.getExamInfo(new Page<>(current, size), map));
    }
    @ApiOperationSupport(order = 12)
    @GetMapping(value = "/getTrainInfo")
    @ApiOperation(value = "7.12 培训情况统计表")
    @ApiImplicitParams({@ApiImplicitParam(name = "groupId", value = "组群名称", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "certificateType", value = "持证类型", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "userName", value = "用户名", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "jobId", value = "岗位", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "deptName", value = "部门", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "jobName", value = "岗位", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "year", value = "年份", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "courseSource", value = "课程来源", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "playStatus", value = "培训情况", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "current", value = "当前页", paramType = "query", required = true, dataType = "Integer"),
        @ApiImplicitParam(name = "size", value = "每页数量", paramType = "query", required = true, dataType = "Integer")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<IPage<Map<String, Object>>> getTrainInfo(@RequestParam(value = "groupId", required = false) String groupId,
                                                               @RequestParam(value = "certificateType", required = false) String certificateType,
                                                               @RequestParam(value = "userName", required = false) String userName,
                                                               @RequestParam(value = "deptId", required = false) String deptId,
                                                               @RequestParam(value = "jobId", required = false) String jobId,
                                                               @RequestParam(value = "deptName", required = false) String deptName,
                                                               @RequestParam(value = "jobName", required = false) String jobName,
                                                               @RequestParam(value = "year", required = false) String year,
                                                               @RequestParam(value = "courseSource", required = false) String courseSource,
                                                               @RequestParam(value = "playStatus", required = false) String playStatus,
                                                               @RequestParam(value = "jobAttrId", required = false) Long jobAttrId,
                                                               @RequestParam(value = "size", required = false) Integer size,
                                                               @RequestParam(value = "current", required = false) Integer current) {

        Map<String, Object> map = new HashMap<>();
        map.put("groupId", groupId);
        map.put("certificateType", certificateType);
        map.put("deptId", deptId);
        map.put("jobId", jobId);
        map.put("deptName", deptName);
        map.put("jobName", jobName);
        map.put("userName", userName);
        map.put("year", year);
        map.put("courseSource", courseSource);
        map.put("playStatus", playStatus);

        return R.ok(accountService.getTrainInfo(new Page<>(current, size), map));
    }

}
