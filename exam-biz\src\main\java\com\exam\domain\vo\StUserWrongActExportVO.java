package com.exam.domain.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 用户错误信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StUserWrongActExportVO implements Serializable {

    /**
     * 违规编号
     */
    private String code;


    /**
     * 考试名称
     */
    private String examName;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 违规行为
     */
    private String wrongAction;

    /**
     * 违规图片路径
     */
    private String errorPicPath;

    /**
     * 是否提醒(1.是；0.否)
     */
    private String isRemindStr;

    /**
     * 是否强制提交(1.是；0.否)
     */
    private String isForceSubmitStr;

    /**
     * 违规时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}

