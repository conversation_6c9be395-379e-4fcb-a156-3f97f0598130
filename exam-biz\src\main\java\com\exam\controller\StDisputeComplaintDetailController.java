package com.exam.controller;

import java.util.List;
import java.util.Arrays;
import java.util.Map;

import com.exam.domain.StDisputeComplaintDetailDTO;
import com.exam.domain.bo.StDisputeComplaintDetailBo;
import com.exam.domain.qo.StDisputeComplaintDetailQO;
import com.exam.domain.vo.StDisputeComplaintDetailVo;
import com.exam.service.IStDisputeComplaintDetailService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 纠纷申诉详情
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/disputeComplaintDetail")
public class StDisputeComplaintDetailController extends BaseController {

    private final IStDisputeComplaintDetailService iStDisputeComplaintDetailService;

    /**
     * 查询纠纷申诉详情列表
     */
    @SaCheckPermission("exam:disputeComplaintDetail:list")
    @GetMapping("/list")
    public TableDataInfo<StDisputeComplaintDetailVo> list(StDisputeComplaintDetailBo bo, PageQuery pageQuery) {
        return iStDisputeComplaintDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出纠纷申诉详情列表
     */
    @SaCheckPermission("exam:disputeComplaintDetail:export")
    @Log(title = "纠纷申诉详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StDisputeComplaintDetailBo bo, HttpServletResponse response) {
        List<StDisputeComplaintDetailVo> list = iStDisputeComplaintDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "纠纷申诉详情", StDisputeComplaintDetailVo.class, response);
    }

    /**
     * 获取纠纷申诉详情详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:disputeComplaintDetail:query")
    @GetMapping("/{id}")
    public R<StDisputeComplaintDetailVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStDisputeComplaintDetailService.queryById(id));
    }

    /**
     * 新增纠纷申诉详情
     */
    @SaCheckPermission("exam:disputeComplaintDetail:add")
    @Log(title = "纠纷申诉详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StDisputeComplaintDetailBo bo) {
        return toAjax(iStDisputeComplaintDetailService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改纠纷申诉详情
     */
    @SaCheckPermission("exam:disputeComplaintDetail:edit")
    @Log(title = "纠纷申诉详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StDisputeComplaintDetailBo bo) {
        return toAjax(iStDisputeComplaintDetailService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除纠纷申诉详情
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:disputeComplaintDetail:remove")
    @Log(title = "纠纷申诉详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStDisputeComplaintDetailService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
    @ApiOperationSupport(order = 1)
    @PostMapping(value = "/saveDisputeComplaintDetail")
    @ApiOperation(value = "1.1 保存纠纷申诉详情")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<String> saveDisputeComplaintDetail(@ApiParam @RequestBody StDisputeComplaintDetailDTO stDisputeComplaintDetailDTO) {
        Long id = iStDisputeComplaintDetailService.saveStDisputeComplaintDetail(stDisputeComplaintDetailDTO);
        return R.ok(id.toString());
    }

    @ApiOperationSupport(order = 2)
    @GetMapping(value = "/deleteDisputeComplaintDetail")
    @ApiOperation(value = "1.2 删除纠纷申诉详情")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> deleteDisputeComplaintDetail(Long id) {
        iStDisputeComplaintDetailService.delete(id);
        return R.ok();
    }

    @ApiOperationSupport(order = 3)
    @PostMapping(value = "/listDisputeComplaintDetail")
    @ApiOperation(value = "1.3 列表纠纷申诉详情")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Map<Integer, List<StDisputeComplaintDetailVo>>> listStDisputeComplaintDetail(@ApiParam @RequestBody StDisputeComplaintDetailQO stDisputeComplaintDetailQO) {
        return R.ok(iStDisputeComplaintDetailService.listStDisputeComplaintDetail(stDisputeComplaintDetailQO));
    }
}
