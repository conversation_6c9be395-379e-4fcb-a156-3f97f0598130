package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import java.io.Serializable;
import java.util.List;
import lombok.Data;


/**
 * 【请填写功能名称】视图对象 st_rotation_picture
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
@Data
@ExcelIgnoreUnannotated
public class StRotationPictureVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 停留时间
     */
    @ExcelProperty(value = "停留时间")
    private Long residenceTime;

    /**
     * 自动适配
     */
    @ExcelProperty(value = "自动适配")
    private Long autoAdapt;

    /**
     * 图片地址（多个用，分割）
     */
    @ExcelProperty(value = "图片地址", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "多=个用，分割")
    private String picPaths;

    /**
     * 区分（web,1;  APP,2）
     */
    @ExcelProperty(value = "区分", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "w=eb,1;,A=PP,2")
    private Long differentiate;

    /**
     * 租户ID
     */
    @ExcelProperty(value = "租户ID")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;


    /**
     * 图片地址（多个用，分割
     */
    private List<String> picPathList;

    /**
     * 图片地址（多个用，分割）(全路径)
     */
    private List<String> fullPicPathList;
}
