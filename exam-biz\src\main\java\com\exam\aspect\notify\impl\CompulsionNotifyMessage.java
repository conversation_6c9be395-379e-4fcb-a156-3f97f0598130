package com.exam.aspect.notify.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.exam.aspect.notify.NotifyMessageObject;
import com.exam.constant.CommonConsts;
import com.exam.constant.CommonDataBaseConst;
import com.exam.constant.TableFieldNameConsts;
import com.exam.domain.StCompulsionComplaint;
import com.exam.domain.StExam;
import com.exam.domain.StMessageUser;
import com.exam.domain.StMessageUserShield;
import com.exam.domain.vo.StMessageVo;
import com.exam.service.IStExamService;
import com.exam.service.IStMessageService;
import com.exam.service.IStMessageUserService;
import com.exam.service.IStMessageUserShieldService;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import com.exam.utils.SequenceBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CompulsionNotifyMessage extends NotifyMessageObject {

    @Autowired
    private IStMessageService stMessageService;
    @Autowired
    private IStMessageUserService stMessageUserService;
    @Autowired
    private IStMessageUserShieldService iStMessageUserShieldService;
    @Autowired
    private IStExamService stExamService;

    @Override
    public void notifyMessage(int msgType, int msgDetailType, Object... args){


        StCompulsionComplaint stCompulsionComplaint = null;
        if(args.length != 0 && args[0] instanceof StCompulsionComplaint) {
            stCompulsionComplaint = (StCompulsionComplaint) args[0];
        }

        // TODO 此处待改造，但涉及的内容较多，后续处理

        StMessageVo vo = stMessageService.getByMsgAndDetailType(msgType, msgDetailType, stCompulsionComplaint.getTenantId());

        QueryWrapper<StMessageUserShield> queryWrapper = new QueryWrapper<StMessageUserShield>();
        queryWrapper.eq(TableFieldNameConsts.USER_ID, stCompulsionComplaint.getUserId());
        queryWrapper.eq(TableFieldNameConsts.TENANT_ID, stCompulsionComplaint.getTenantId());
        StMessageUserShield stMessageUserShield = iStMessageUserShieldService.getBaseMapper().selectOne(queryWrapper);

        // 获取申诉通过考试超时时间
        StExam stExam = stExamService.getById(stCompulsionComplaint.getExamId());
//        LocalDateTime complateTime = stCompulsionComplaint.getCompleteTime();
//        LocalDateTime timeout = complateTime.plusMinutes(Long.valueOf(stExam.getAppealExamTime()));
        LocalDateTime disputeTime = stCompulsionComplaint.getDisputeTime();
        LocalDateTime timeout = disputeTime.plusMinutes(Long.valueOf(stExam.getAppealExamTime()));
        if(stMessageUserShield == null || CommonConsts.NO.equals(stMessageUserShield.getIsShield())) {
            StMessageUser stMessageUser = new StMessageUser();
//            EntityUtils.setInsertInfo(stMessageUser);
            stMessageUser.setDelFlag(CommonDataBaseConst.YES_OR_NO.NO.getCode());
            stMessageUser.setId(SequenceBean.getSequence());
            stMessageUser.setMainTableId(stCompulsionComplaint.getId());
            stMessageUser.setPushTime(LocalDateTime.now());
            stMessageUser.setStatus(Integer.valueOf(CommonDataBaseConst.MSG_READ_STATUS.UNREAD.getCode()));
            stMessageUser.setAppendMsg("请在"+timeout.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))+"内继续考试");
            stMessageUser.setUserInfoId(stCompulsionComplaint.getUserId());
            stMessageUser.setMessageId(vo.getId());
            stMessageUser.setMsgDetailType(msgDetailType);
            stMessageUser.setTenantId(stCompulsionComplaint.getTenantId());
            stMessageUserService.save(stMessageUser);
        }
    }

    public static void main(String[] args) {
        LocalDateTime complateTime = LocalDateTime.now();
        complateTime = complateTime.plusMinutes(111);
        System.out.println(complateTime);
    }

}
