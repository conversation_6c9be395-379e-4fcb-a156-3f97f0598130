package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StKnowledgeClassroomBo;
import com.exam.domain.vo.StKnowledgeClassroomVo;
import com.exam.service.IStKnowledgeClassroomService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 知识课堂
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/knowledgeClassroom")
public class StKnowledgeClassroomController extends BaseController {

    private final IStKnowledgeClassroomService iStKnowledgeClassroomService;

    /**
     * 查询知识课堂列表
     */
    @SaCheckPermission("exam:knowledgeClassroom:list")
    @GetMapping("/list")
    public TableDataInfo<StKnowledgeClassroomVo> list(StKnowledgeClassroomBo bo, PageQuery pageQuery) {
        return iStKnowledgeClassroomService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出知识课堂列表
     */
    @SaCheckPermission("exam:knowledgeClassroom:export")
    @Log(title = "知识课堂", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StKnowledgeClassroomBo bo, HttpServletResponse response) {
        List<StKnowledgeClassroomVo> list = iStKnowledgeClassroomService.queryList(bo);
        ExcelUtil.exportExcel(list, "知识课堂", StKnowledgeClassroomVo.class, response);
    }

    /**
     * 获取知识课堂详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:knowledgeClassroom:query")
    @GetMapping("/{id}")
    public R<StKnowledgeClassroomVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStKnowledgeClassroomService.queryById(id));
    }

    /**
     * 新增知识课堂
     */
    @SaCheckPermission("exam:knowledgeClassroom:add")
    @Log(title = "知识课堂", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StKnowledgeClassroomBo bo) {
        return toAjax(iStKnowledgeClassroomService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改知识课堂
     */
    @SaCheckPermission("exam:knowledgeClassroom:edit")
    @Log(title = "知识课堂", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StKnowledgeClassroomBo bo) {
        return toAjax(iStKnowledgeClassroomService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除知识课堂
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:knowledgeClassroom:remove")
    @Log(title = "知识课堂", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStKnowledgeClassroomService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
