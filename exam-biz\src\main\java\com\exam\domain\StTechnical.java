package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户证件对象 st_technical
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_technical")
public class StTechnical extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private String id;
    /**
     * 职务名称
     */
    private String name;

}
