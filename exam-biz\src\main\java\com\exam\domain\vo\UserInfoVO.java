package com.exam.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.exam.common.annotation.Sensitive;
import com.exam.common.enums.SensitiveStrategy;
import com.exam.domain.RailwayUserInfo;
import com.exam.utils.ReadFileUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "用户信息")
public class UserInfoVO extends RailwayUserInfo implements Serializable {

    @ApiModelProperty(value = "手机号码")
    private String phonenumber;

    @ApiModelProperty(value="子公司或者项目名称")
    private String rpName;

    @ApiModelProperty(value="用户状态（0正常 1停用）")
    private String status;

    @ApiModelProperty(value="用户角色")
    private String roleName;

    @ApiModelProperty(value="审核人")
    private String approveUserName;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime rfaTime;

    @ApiModelProperty(value = "添加人")
    private String createUserName;

    @ApiModelProperty(value = "最后修改人")
    private String updateUserName;
}

