package com.exam.controller;

import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.R;
import com.exam.domain.vo.StBlockSoftwareVo;
import com.exam.service.IStBlockSoftwareService;
import java.util.List;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 屏蔽软件
 *
 * <AUTHOR>
 * @date 2023-12-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/blockSoftware")
public class StBlockSoftwareController extends BaseController {

    @Resource
    private final IStBlockSoftwareService stBlockSoftwareService;

//    /**
//     * 查询屏蔽软件列表
//     */
//    @SaCheckPermission("exam:blockSoftware:list")
//    @GetMapping("/list")
//    public TableDataInfo<StBlockSoftwareVo> list(StBlockSoftwareBo bo, PageQuery pageQuery) {
//        return iStBlockSoftwareService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出屏蔽软件列表
//     */
//    @SaCheckPermission("exam:blockSoftware:export")
//    @Log(title = "屏蔽软件", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(StBlockSoftwareBo bo, HttpServletResponse response) {
//        List<StBlockSoftwareVo> list = iStBlockSoftwareService.queryList(bo);
//        ExcelUtil.exportExcel(list, "屏蔽软件", StBlockSoftwareVo.class, response);
//    }
//
//    /**
//     * 获取屏蔽软件详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("exam:blockSoftware:query")
//    @GetMapping("/{id}")
//    public R<StBlockSoftwareVo> getInfo(@NotNull(message = "主键不能为空")
//    @PathVariable Long id) {
//        return R.ok(iStBlockSoftwareService.queryById(id));
//    }
//
//    /**
//     * 新增屏蔽软件
//     */
//    @SaCheckPermission("exam:blockSoftware:add")
//    @Log(title = "屏蔽软件", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody StBlockSoftwareBo bo) {
//        return toAjax(iStBlockSoftwareService.insertByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 修改屏蔽软件
//     */
//    @SaCheckPermission("exam:blockSoftware:edit")
//    @Log(title = "屏蔽软件", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StBlockSoftwareBo bo) {
//        return toAjax(iStBlockSoftwareService.updateByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 删除屏蔽软件
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("exam:blockSoftware:remove")
//    @Log(title = "屏蔽软件", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//    @PathVariable Long[] ids) {
//        return toAjax(iStBlockSoftwareService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
//    }


    @GetMapping(value = "/selectBlockSoftwareList")
    @ResponseBody
    public R<List<StBlockSoftwareVo>> selectBlockSoftwareList() {
        List<StBlockSoftwareVo> stBlockSoftwareVOList = stBlockSoftwareService.selectBlockSoftwareList();
        return R.ok(stBlockSoftwareVOList);
    }

}
