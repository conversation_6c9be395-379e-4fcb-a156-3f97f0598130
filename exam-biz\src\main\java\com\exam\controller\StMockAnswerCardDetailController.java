package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.page.TableDataInfo;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.bo.StMockAnswerCardDetailBo;
import com.exam.domain.vo.StMockAnswerCardDetailVo;
import com.exam.service.IStMockAnswerCardDetailService;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 答题卡详细
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/mockAnswerCardDetail")
public class StMockAnswerCardDetailController extends BaseController {

    private final IStMockAnswerCardDetailService iStMockAnswerCardDetailService;

//    /**
//     * 查询答题卡详细列表
//     */
//    @SaCheckPermission("exam:mockAnswerCardDetail:list")
//    @GetMapping("/list")
//    public TableDataInfo<StMockAnswerCardDetailVo> list(StMockAnswerCardDetailBo bo, PageQuery pageQuery) {
//        return iStMockAnswerCardDetailService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出答题卡详细列表
//     */
//    @SaCheckPermission("exam:mockAnswerCardDetail:export")
//    @Log(title = "答题卡详细", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(StMockAnswerCardDetailBo bo, HttpServletResponse response) {
//        List<StMockAnswerCardDetailVo> list = iStMockAnswerCardDetailService.queryList(bo);
//        ExcelUtil.exportExcel(list, "答题卡详细", StMockAnswerCardDetailVo.class, response);
//    }
//
//    /**
//     * 获取答题卡详细详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("exam:mockAnswerCardDetail:query")
//    @GetMapping("/{id}")
//    public R<StMockAnswerCardDetailVo> getInfo(@NotNull(message = "主键不能为空")
//    @PathVariable Long id) {
//        return R.ok(iStMockAnswerCardDetailService.queryById(id));
//    }
//
//    /**
//     * 新增答题卡详细
//     */
//    @SaCheckPermission("exam:mockAnswerCardDetail:add")
//    @Log(title = "答题卡详细", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody StMockAnswerCardDetailBo bo) {
//        return toAjax(iStMockAnswerCardDetailService.insertByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 修改答题卡详细
//     */
//    @SaCheckPermission("exam:mockAnswerCardDetail:edit")
//    @Log(title = "答题卡详细", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StMockAnswerCardDetailBo bo) {
//        return toAjax(iStMockAnswerCardDetailService.updateByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 删除答题卡详细
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("exam:mockAnswerCardDetail:remove")
//    @Log(title = "答题卡详细", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//    @PathVariable Long[] ids) {
//        return toAjax(iStMockAnswerCardDetailService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
//    }
}
