package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 用户组群关系对象 st_user_wrong_act
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_user_wrong_act")
public class StUserWrongAct extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 答题卡id
     */
    private Long answerCardId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 违规行为
     */
    private String wrongAction;
    /**
     * 违规编号
     */
    private Integer code;
    /**
     * 是否提醒
     */
    private Long isRemind;
    /**
     * 是否强制提交
     */
    private Long isForceSubmit;
    /**
     * 考试id
     */
    private Long examId;
    /**
     * 身份证正面照片地址
     */
    private String idImageUrl;
    /**
     * 错误图片base64编码
     */
    private String errorPicBase;
    /**
     * 人脸识别错误编码
     */
    private String errorCode;
    /**
     * 比对结果置信度
     */
    private String confidence;
    /**
     * 补考次数
     */
    private String retestTimes;
    /**
     * 提醒内容
     */
    private String remindContent;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 考试名称
     */
    private String examName;

}
