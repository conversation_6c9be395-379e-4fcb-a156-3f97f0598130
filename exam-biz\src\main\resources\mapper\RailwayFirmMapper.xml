<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.exam.mapper.RailwayFirmMapper">
    <resultMap id="BaseResultMap" type="com.exam.domain.RailwayFirm">
        <!--@mbg.generated-->
        <!--@Table railway_firm-->
        <id column="rf_id" jdbcType="BIGINT" property="rfId"/>
        <result column="rf_name" jdbcType="VARCHAR" property="rfName"/>
        <result column="rf_credit_code" jdbcType="VARCHAR" property="rfCreditCode"/>
        <result column="rf_address" jdbcType="VARCHAR" property="rfAddress"/>
        <result column="rf_contact" jdbcType="VARCHAR" property="rfContact"/>
        <result column="rf_phone" jdbcType="VARCHAR" property="rfPhone"/>
        <result column="rf_id_card" jdbcType="VARCHAR" property="rfIdCard"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        rf_id,
        rf_name,
        rf_credit_code,
        rf_address,
        rf_contact,
        rf_phone,
        rf_id_card,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <select id="queryPage" resultType="com.exam.domain.vo.RailwayFirmVo">
        select f.rf_id,
               f.rf_name,
               f.rf_phone,
               f.rf_business_license_sso_id,
               f.rf_safe_production_sso_id,
               fa.rfa_approve,
               fa.rfa_approver,
               fa.rfa_time,
               f.update_time,
               f.update_by,
               f.create_time,
               f.create_by
        from railway_firm f
                 left join railway_firm_approve fa on f.rf_id = fa.rf_id
        <where>
            <if test="rfName != null and rfName != ''">
                and f.rf_name like concat('%', #{rfName}, '%')
            </if>
            <if test="rfPhone != null and rfPhone != ''">
                and f.rf_phone like concat('%', #{rfPhone}, '%')
            </if>
            <if test="rfContact != null and rfContact != ''">
                and f.rf_contact like concat('%', #{rfContact}, '%')
            </if>
            <if test="rfApprove != null">
                and fa.rfa_approve = #{rfApprove}
            </if>
        </where>
        order by f.rf_id
    </select>
    <select id="getById" resultType="com.exam.domain.RailwayFirm">
        select *
        from railway_firm
        where rf_id = #{tenantId}
    </select>
</mapper>
