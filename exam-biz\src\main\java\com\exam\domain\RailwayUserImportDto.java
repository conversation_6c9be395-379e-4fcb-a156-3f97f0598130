package com.exam.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 国铁企业用户导入DTO
 *
 * @ClassName: RailwayUserImportDto
 * @Description: 国铁企业用户导入DTO
 * @Author: Xmj
 * @Date: 2025/5/22 14:10
 * @Version: 1.0
 */
@ApiModel(description = "铁路企业用户")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RailwayUserImportDto {

    @ExcelProperty(value = "姓名")
    @ApiModelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "手机号码")
    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ExcelProperty(value = "身份证号码")
    @ApiModelProperty(value = "身份证号码")
    private String idCard;



    @ExcelProperty(value = "所属子公司")
    @ApiModelProperty(value = "所属子公司")
    private String projectName;

    /**
     * 角色组
     */
    @ExcelProperty(value = "用户角色")
    @ApiModelProperty(value = "用户角色")
    private String roleNames;

    @ExcelProperty(value = "证书注册企业")
    @ApiModelProperty(value = "证书注册企业")
    private String certificateUnit;

}
