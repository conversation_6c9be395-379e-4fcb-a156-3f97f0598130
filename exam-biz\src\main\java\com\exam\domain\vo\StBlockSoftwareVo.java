package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 屏蔽软件视图对象 st_block_software
 *
 * <AUTHOR>
 * @date 2023-12-19
 */
@Data
@ExcelIgnoreUnannotated
public class StBlockSoftwareVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 软件名称
     */
    @ExcelProperty(value = "软件名称")
    private String name;

    /**
     * 软件中文名称
     */
    @ExcelProperty(value = "软件中文名称")
    private String nameCh;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 顺序
     */
    @ExcelProperty(value = "顺序")
    private Long sn;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;


}
