package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.domain.BaseEntity2;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 租户业务对象 tenant
 *
 * <AUTHOR>
 * @date 2023-10-27
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TenantBo extends BaseEntity2 {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 租户名称
     */
    @NotBlank(message = "租户名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 项目数量限制
     */
    @NotNull(message = "项目数量限制不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectLimit;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 截至时间
     */
    private Date endTime;

    /**
     * 企业介绍
     */
    private String enterpriseIntroduction;


}
