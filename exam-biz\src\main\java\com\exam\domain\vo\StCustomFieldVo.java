package com.exam.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 岗位属性视图对象 st_custom_field
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StCustomFieldVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 页面类型
     */
    @ExcelProperty(value = "页面类型")
    private String fieldType;

    /**
     * 自定义列json
     */
    @ExcelProperty(value = "自定义列json")
    private String fieldJson;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 是否删除(1.是；0.否)
     */
    @ExcelProperty(value = "是否删除(1.是；0.否)")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人id
     */
    @ExcelProperty(value = "创建人id")
    private Long createBy;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long updateBy;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private String projectId;

    /**
     * 所属租户
     */
    @ExcelProperty(value = "所属租户")
    private String tenantId;


}
