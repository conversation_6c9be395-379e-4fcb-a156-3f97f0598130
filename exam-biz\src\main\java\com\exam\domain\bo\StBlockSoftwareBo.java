package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 屏蔽软件业务对象 st_block_software
 *
 * <AUTHOR>
 * @date 2023-12-19
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StBlockSoftwareBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 软件名称
     */
    @NotBlank(message = "软件名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 软件中文名称
     */
    @NotBlank(message = "软件中文名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String nameCh;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;

    /**
     * 顺序
     */
    @NotNull(message = "顺序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sn;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;

    /**
     * 所属项目
     */
    @NotNull(message = "所属项目不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;


}
