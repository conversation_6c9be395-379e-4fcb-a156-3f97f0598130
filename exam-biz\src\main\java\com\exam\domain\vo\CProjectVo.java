package com.exam.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 项目视图对象 c_project
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class CProjectVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 项目名称(公司名称)
     */
    @ExcelProperty(value = "项目名称(公司名称)")
    private String companyName;

    /**
     * 项目编码(公司编码)，与归属公司有层级关系
     */
    @ExcelProperty(value = "项目编码(公司编码)，与归属公司有层级关系")
    private String companyCode;

    /**
     * 归属公司的编码
     */
    @ExcelProperty(value = "归属公司的编码")
    private String parentCompanyCode;

    /**
     * 项目编码(公司编码),展示所用
     */
    @ExcelProperty(value = "项目编码(公司编码),展示所用")
    private String companyNum;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Long longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Long latitude;

    /**
     * 所在区县ID
     */
    @ExcelProperty(value = "所在区县ID")
    private Long countyId;

    /**
     * 详细地址
     */
    @ExcelProperty(value = "详细地址")
    private String location;

    /**
     * 合同开工日期
     */
    @ExcelProperty(value = "合同开工日期")
    private Date startTime;

    /**
     * 合同完成日期
     */
    @ExcelProperty(value = "合同完成日期")
    private Date endTime;

    /**
     * 所属公司ID
     */
    @ExcelProperty(value = "所属公司ID")
    private Long companyId;

    /**
     * 中标单位公司编码
     */
    @ExcelProperty(value = "中标单位公司编码")
    private String bidWinnerCompanyCode;

    /**
     * 工程类别
     */
    @ExcelProperty(value = "工程类别")
    private String projectCategory;

    /**
     * 中标时间
     */
    @ExcelProperty(value = "中标时间")
    private String winningTheBidTime;

    /**
     * 中标合同金额
     */
    @ExcelProperty(value = "中标合同金额")
    private BigDecimal winningTheBidContractM;

    /**
     * 管理模式
     */
    @ExcelProperty(value = "管理模式")
    private String managementMode;

    /**
     * 合同工期
     */
    @ExcelProperty(value = "合同工期")
    private Integer contractConstructionPeriod;

    /**
     * 建设单位
     */
    @ExcelProperty(value = "建设单位")
    private String buildCompany;

    /**
     * 监理单位
     */
    @ExcelProperty(value = "监理单位")
    private String supervisorCompany;

    /**
     * 设计单位
     */
    @ExcelProperty(value = "设计单位")
    private String designCompany;

    /**
     * 隶属单位
     */
    @ExcelProperty(value = "隶属单位")
    private String subordinateCompany;

    /**
     * 投资分类
     */
    @ExcelProperty(value = "投资分类")
    private String investmentType;

    /**
     * 项目经理
     */
    @ExcelProperty(value = "项目经理")
    private String projectManager;

    /**
     * 项目经理联系方式
     */
    @ExcelProperty(value = "项目经理联系方式")
    private String projectManagerTel;

    /**
     * 计划负责人
     */
    @ExcelProperty(value = "计划负责人")
    private String planManager;

    /**
     * 计划负责人联系方式
     */
    @ExcelProperty(value = "计划负责人联系方式")
    private String planManagerTel;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @ExcelProperty(value = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))")
    private Long dataStatus;

    /**
     * 更新人ID
     */
    @ExcelProperty(value = "更新人ID")
    private Long updateUserId;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private String updateTimeStr;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private Long createUserId;

    /**
     * 项目描述
     */
    @ExcelProperty(value = "项目描述")
    private String projectDesc;

    /**
     * 项目简称
     */
    @ExcelProperty(value = "项目简称")
    private String projectAbbreviation;

    /**
     * 项目类型
     */
    @ExcelProperty(value = "项目类型")
    private String projectType;

    /**
     * 节点编码
     */
    @ExcelProperty(value = "节点编码")
    private String orgCode;

    /**
     * 上级节点编码
     */
    @ExcelProperty(value = "上级节点编码")
    private String parentOrgCode;

    /**
     * org表id
     */
    @ExcelProperty(value = "org表id")
    private Long orgId;

    /**
     * 项目经理 手机号码 区号
     */
    @ExcelProperty(value = "项目经理 手机号码 区号")
    private String phoneNumberCode;

    /**
     * 国外区域：1      国内区域：0
     */
    @ExcelProperty(value = "国外区域：1      国内区域：0")
    private Long isOverseas;


}
