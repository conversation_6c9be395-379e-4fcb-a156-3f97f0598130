package com.exam.domain.vo;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.web.multipart.MultipartFile;


/**
 * 上传试题VO
 *
 * <AUTHOR>
 * @since 2021-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ImportQuestionVO implements Serializable {

    /**
     * 试题类别
     */
    private String questionType;

    /**
     * 课程 ID
     */
    private String courseId;

    /**
     * 是否公开 1 公有 0 私有
     */
    private Integer isOpen;

    /**
     * 上传文件
     */
    private MultipartFile file;
}
