package com.exam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 铁路局——企业管理
 */
@ApiModel(description = "铁路局——企业管理-新增dto")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RailwayFirmDto {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "企业管理Id")
    private Long rfId;

    /**
     * 企业名称
     */
    @TableField(value = "rf_name")
    @ApiModelProperty(value = "企业名称")
    private String rfName;

    /**
     * 信用码
     */
    @TableField(value = "rf_credit_code")
    @ApiModelProperty(value = "信用码")
    private String rfCreditCode;

    /**
     * 注册登记地
     */
    @TableField(value = "rf_address")
    @ApiModelProperty(value = "注册登记地")
    private String rfAddress;

    /**
     * 联系人
     */
    @TableField(value = "rf_contact")
    @ApiModelProperty(value = "联系人")
    private String rfContact;

    /**
     * 联系人手机号
     */
    @TableField(value = "rf_phone")
    @ApiModelProperty(value = "联系人手机号")
    private String rfPhone;

    /**
     * 身份证号
     */
    @TableField(value = "rf_id_card")
    @ApiModelProperty(value = "身份证号")
    private String rfIdCard;

    /**
     * 营业执照ssoId
     */
    @TableField(value = "rf_business_license_sso_id")
    @ApiModelProperty(value = "营业执照ssoId")
    private String rfBusinessLicenseSsoId;

    /**
     * 安全生产许可证ssoId
     */
    @TableField(value = "rf_safe_production_sso_id")
    @ApiModelProperty(value = "安全生产许可证ssoId")
    private String rfSafeProductionSsoId;

    /**
     * 省id
     */
    @TableField(value = "province_id")
    @ApiModelProperty(value = "省id")
    private Long provinceId;

    /**
     * 市id
     */
    @TableField(value = "city_id")
    @ApiModelProperty(value = "市id")
    private Long cityId;

    /**
     * 区id
     */
    @TableField(value = "county_id")
    @ApiModelProperty(value = "区id")
    private Long countyId;


    /**
     * 省
     */
    @TableField(value = "province")
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 市
     */
    @TableField(value = "city")
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区
     */
    @TableField(value = "county")
    @ApiModelProperty(value = "区")
    private String county;

    @ApiModelProperty(value = "验证码")
    private String verificationCode;
}
