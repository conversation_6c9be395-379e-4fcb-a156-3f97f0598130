package com.exam.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 岗位属性视图对象 st_cheat_pictrue
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StCheatPictrueVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 考试id
     */
    @ExcelProperty(value = "考试id")
    private Long examId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 图片路径
     */
    @ExcelProperty(value = "图片路径")
    private String fileUrl;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息")
    private String msg;

    /**
     * 接口返回信息
     */
    @ExcelProperty(value = "接口返回信息")
    private String response;

    /**
     * 是否删除(1.是；0.否)
     */
    @ExcelProperty(value = "是否删除(1.是；0.否)")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人id
     */
    @ExcelProperty(value = "创建人id")
    private Long createBy;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long updateBy;

    /**
     * 领域
     */
    @ExcelProperty(value = "领域")
    private String domainCode;

    /**
     * 所属单位
     */
    @ExcelProperty(value = "所属单位")
    private String companyCode;


}
