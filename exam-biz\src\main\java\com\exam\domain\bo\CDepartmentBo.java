package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 部门业务对象 c_department
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class CDepartmentBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 上级单位（公司或项目）ID
     */
    @NotNull(message = "上级单位（公司或项目）ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long superiorId;

    /**
     * 部门类型（0：公司；1：项目；）
     */
    @NotNull(message = "部门类型（0：公司；1：项目；）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long departmentType;

    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 父节点ID
     */
    @NotNull(message = "父节点ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fatherId;

    /**
     * 序号
     */
    @NotBlank(message = "序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sn;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @NotNull(message = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dataStatus;

    /**
     * 所属公司
     */
    @NotBlank(message = "所属公司不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;

    /**
     * 注册时间(显示使用)
     */
    @NotBlank(message = "注册时间(显示使用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String signTimeStr;

    /**
     * 编辑时间(显示使用)
     */
    @NotBlank(message = "编辑时间(显示使用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String updateTimeStr;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long signUserId;

    /**
     * 编辑人id
     */
    @NotNull(message = "编辑人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateUserId;

    /**
     * 节点编码
     */
    @NotBlank(message = "节点编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgCode;

    /**
     * 是否为虚拟部门(0：否；1：是；)
     */
    @NotNull(message = "是否为虚拟部门(0：否；1：是；)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isFictitious;

    /**
     * 上级节点编码
     */
    @NotBlank(message = "上级节点编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentOrgCode;

    /**
     * org表id
     */
    @NotNull(message = "org表id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orgId;

    /**
     * 是否为外部部门(0：否；1：是；)
     */
    @NotNull(message = "是否为外部部门(0：否；1：是；)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isExternal;


}
