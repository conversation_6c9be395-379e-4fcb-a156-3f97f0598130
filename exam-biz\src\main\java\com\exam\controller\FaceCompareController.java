package com.exam.controller;

import com.alibaba.fastjson.JSONObject;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.R;
import com.exam.common.helper.LoginHelper;
import com.exam.common.utils.redis.RedisUtils;
import com.exam.constant.InvigilatorBaseEnum;
import com.exam.constant.InvigilatorConsts;
import com.exam.constant.RedisConsts;
import com.exam.domain.AliFaceDTO;
import com.exam.domain.AliFaceVO;
import com.exam.domain.vo.UserFileDTO;
import com.exam.domain.vo.UserFileVO;
import com.exam.service.FaceCompareService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;

/**
 * 人脸对比接口
 *
 * <AUTHOR>
 * @date 2023-11-09
 */

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/faceCompareController")
public class FaceCompareController extends BaseController {
    @Resource
    FaceCompareService faceCompareService;

    private static Logger logger = LoggerFactory.getLogger(BaseController.class);

    @ApiOperation(value = "比对人脸 异步(自主研发)", notes = "比对人脸 异步， 参数需要图片文件")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "app", value = "调用系统", paramType = "query", required = true, dataType = "String"),
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @PostMapping(value = "getImageCompareAsynchronousAi")
    @ApiOperationSupport(order = 1)
    public R<Object> getImageCompareAsynchronousAi(UserFileDTO userFileDTO) throws Exception {
        String app = userFileDTO.getApp();
        if (StringUtils.isEmpty(app)) {
            return R.fail(InvigilatorBaseEnum.FACE_APP_ERROR);
        }
        if(null== InvigilatorConsts.FACE_COMPARE_APP_CONFIDENCE.get(app)){
            return R.fail(InvigilatorBaseEnum.FACE_RATIO_ERROR);
        }
//        userFileService.getImageCompareAsynchronous(userFileVO);
        return faceCompareService.getImageCompareAsynchronousAi(userFileDTO);
    }

    /**
     * @Description: 获取人脸识别redis结果
     * @return: com.crcc.invigilator.exception.ResultBody
     * @author: weidongyang
     * @date: 2022/7/21 8:48
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "获取人脸识别redis结果", notes = "获取人脸识别redis结果")
    @ApiResponse(code = 1000, message = "操作成功")
    @PostMapping(value = "getImageCompareAsynchronousRedis")
    public R<Object> getImageCompareAsynchronousRedis(@RequestBody UserFileVO userFileVO) throws Exception{
        return faceCompareService.getImageCompareAsynchronousRedis(userFileVO);
    }


    /**
     * @Description: 获取系统设置人脸对比成功比例
     * @return: com.crcc.invigilator.exception.ResultBody
     * @author: weidongyang
     * @date: 2022/7/21 8:48
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "获取系统设置人脸对比成功比例", notes = "获取系统设置人脸对比成功比例")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "app", value = "调用系统", paramType = "query", required = false, dataType = "String")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @PostMapping(value = "getFaceCompareAppConfidence")
    public R<Object> getFaceCompareAppConfidence(String app) {
        HashMap<String,Object> all = (HashMap) InvigilatorConsts.FACE_COMPARE_APP_CONFIDENCE;
        if(StringUtils.isEmpty(app)){
            return   R.ok(all);
        }else {
            Integer confidence = null;
            if(null!=all){
                confidence = (Integer) all.get(app);
            }
            HashMap<String,Object> result =new HashMap<>();
            result.put(app,confidence);
            return   R.ok(result);
        }

    }
/**
 * @Description: 设置人脸对比成功比例
 * @param: [app, appDesc, confidence, type]
 * @return: com.crcc.invigilator.exception.ResultBody
 * @author: weidongyang
 * @date: 2022/7/22 13:12
 */
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "设置人脸对比成功比例", notes = "设置人脸对比成功比例")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "app", value = "调用系统", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "appDesc", value = "系统描述", paramType = "query", required = false, dataType = "String"),
            @ApiImplicitParam(name = "confidence", value = "成功比例", paramType = "query", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "type", value = "1：新增/修改 ，0:删除", paramType = "query", required = true, dataType = "Integer")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @PostMapping(value = "updateFaceCompareAppConfidence")
    public R<Void> updateFaceCompareAppConfidence(String app,String appDesc,Integer confidence,Integer type) throws Exception {
        return   faceCompareService.updateFaceCompareAppConfidence(app,appDesc,confidence,type);
    }

    @ApiOperationSupport(order = 5)
    @PostMapping(value = "/cacheMonitorExamination")
    @ApiOperation(value = "动态监考线上监考缓存图片")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> cacheMonitorExamination(AliFaceDTO aliFaceDTO) {

        String alifaceKey = RedisConsts.ALIFACE_USER_REULST + aliFaceDTO.getApp() + ":" +  aliFaceDTO.getId()+ ":" + LoginHelper.getUserId();
        // 阿里人脸 超时机制
        RedisUtils.setCacheObject(alifaceKey, InvigilatorConsts.FACE_PROCESSING, InvigilatorConsts.FACE_PROCESSING_TIME);
//        AliFaceMQDTO dto = new AliFaceMQDTO();
//        CopyUtils.copyProperties(aliFaceDTO, dto);

        // 前端传文件， 将文件转成Base64
//        if(aliFaceDTO.getFile() != null) {
//            aliFaceDTO.setBaseFile(Base64Util.imgToBase64String(aliFaceDTO.getFile()));
//        }
        // 发送Mq
//        jmsMessagingTemplate.convertAndSend(this.alifaceQueue, JSONObject.toJSONString(aliFaceDTO));
        faceCompareService.cacheMonitorExamination(aliFaceDTO);
        return R.ok();
    }

    @ApiOperationSupport(order = 6)
    @GetMapping(value = "/getMonitorExamination")
    @ApiOperation(value = "1.1 获取比对结果")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> getMonitorExamination(@NotBlank(message = "系统标识不能为空") String app, @NotBlank(message = "唯一标识不能为空") String id) {
        R<Void> result = new R<Void>();
        Long userId = LoginHelper.getUserId();
        String alifaceKey = RedisConsts.ALIFACE_USER_REULST + app + ":" + id + ":" + userId;
        logger.info("alifaceKey = " + alifaceKey);
        // 判断 阿里人员 是否超时
        if(!RedisUtils.hasKey(alifaceKey)) {
            result.setCode(Integer.parseInt(InvigilatorConsts.FACE_TIMEOUT));
            return result;
        }
        Object alifaceResult = RedisUtils.getCacheObject(alifaceKey);
        // 判断 阿里人脸 是否正在处理
        if(alifaceResult instanceof String) {
            String resultStr = String.valueOf(alifaceResult);
            if(StringUtil.equals(resultStr, InvigilatorConsts.FACE_PROCESSING)) {
                result.setCode(Integer.parseInt(InvigilatorConsts.FACE_PROCESSING));
                return result;
            }
        }
        try {
            // 获取 人脸处理结果
            AliFaceVO aliFaceVO = JSONObject.parseObject(String.valueOf(alifaceResult), AliFaceVO.class);
            result.setCode(Integer.parseInt(aliFaceVO.getCode()));
            result.setMsg(aliFaceVO.getMsg());
        } catch (Exception e) {
            logger.error("获取比对结果出错", e);
            result.setCode(Integer.parseInt(InvigilatorConsts.INTERNAL_SERVER_ERROR));
            result.setMsg("获取比对结果出错" + e.getMessage());
        }finally {
            //应对压力测试 不能删掉alifaceKey
            if(!"4188".equals(userId) && !"17758".equals(userId) ){
                RedisUtils.deleteObject(alifaceKey);
            }
        }
        return result;
    }

}
