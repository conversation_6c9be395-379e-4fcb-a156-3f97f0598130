package com.exam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.exam.domain.RailwayExamRoom;
import com.exam.mapper.RailwayExamRoomMapper;
import com.exam.service.RailwayExamRoomService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class RailwayExamRoomServiceImpl extends ServiceImpl<RailwayExamRoomMapper, RailwayExamRoom> implements RailwayExamRoomService{

}
