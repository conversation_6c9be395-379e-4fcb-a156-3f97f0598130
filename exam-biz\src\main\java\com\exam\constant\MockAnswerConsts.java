package com.exam.constant;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.exam.constant.CommonDataBaseConst.QUESTION_GENRE_NAME;
import com.exam.exception.BizException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Data
public class MockAnswerConsts {

    @Value("${mockQuestion:{'100':[{'id':1,'num':4,'score':5,'sn':0},{'id':2,'num':4,'score':5,'sn':1},{'id':3,'num':4,'score':5,'sn':2},{'id':4,'num':4,'score':5,'sn':3},{'id':5,'num':4,'score':5,'sn':4}],'150':[{'id':1,'num':5,'score':6,'sn':0},{'id':2,'num':5,'score':6,'sn':1},{'id':3,'num':5,'score':6,'sn':2},{'id':4,'num':5,'score':6,'sn':3},{'id':5,'num':5,'score':6,'sn':4}]}}")
    private String question;
    public final static double SCORE = 2.0;

    public Map<Integer, List<QuestionGenre>> QuestionGenreInit() throws Exception {
        Map<Integer, List<QuestionGenre>> questionMap = new HashMap<Integer, List<QuestionGenre>>();
        JSONObject json = JSONObject.parseObject(question);
        for (String key : json.keySet()) {
            JSONArray array = json.getJSONArray(key);
            List<QuestionGenre> genreList = new ArrayList<MockAnswerConsts.QuestionGenre>();
            for (int i = 0; i < array.size(); i++) {
                QuestionGenre genre = new QuestionGenre();
                JSONObject genreJson = array.getJSONObject(i);
                genre.setQuestionId(genreJson.getInteger("id"));
                genre.setQuestionNum(genreJson.getInteger("num"));
                genre.setQuestionScore(genreJson.getInteger("score"));
                genre.setQuestionSn(genreJson.getInteger("sn"));
                genre.setQuestionGenreName(QUESTION_GENRE_NAME.getMap().get(genreJson.getInteger("id")));
                genreList.add(genre);
            }
            questionMap.put(Integer.valueOf(key), genreList);
        }
        return questionMap;
    }

    public Map<Integer, List<QuestionGenre>> getQuestionMap() {
        Map<Integer, List<QuestionGenre>> questionMap = null;
        try {
            questionMap = QuestionGenreInit();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("获取提醒说明出错");

        }
        return questionMap;
    }

    @Data
    public class QuestionGenre {

        private Integer questionId;
        private String questionGenreName;
        private Integer questionNum;
        private Integer questionScore;
        private Integer questionSn;
    }
}
