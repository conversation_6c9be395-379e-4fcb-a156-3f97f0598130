package com.exam.domain.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 申诉QO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="StComplaintQo", description="申诉QO")
public class StComplaintQo extends SearchQO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "时间")
    private String disputeTime;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "领域")
    private String tenantId;

    @ApiModelProperty(value="申诉类型 1 纠纷申诉 2 强制交卷")
    private Integer type;
}
