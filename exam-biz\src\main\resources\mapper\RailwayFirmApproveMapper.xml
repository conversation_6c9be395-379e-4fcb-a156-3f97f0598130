<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.exam.mapper.RailwayFirmApproveMapper">
  <resultMap id="BaseResultMap" type="com.exam.domain.RailwayFirmApprove">
    <!--@mbg.generated-->
    <!--@Table railway_firm_approve-->
    <id column="rfa_id" jdbcType="BIGINT" property="rfpId" />
    <result column="rf_id" jdbcType="BIGINT" property="rfId" />
    <result column="rfa_cause" jdbcType="VARCHAR" property="rfaCause" />
    <result column="rfa_approve" jdbcType="BOOLEAN" property="rfaApprove" />
    <result column="rfa_time" jdbcType="TIMESTAMP" property="rfaTime" />
    <result column="rfa_approver" jdbcType="BIGINT" property="rfaApprover" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    rfa_id, rf_id, rfa_cause, rfa_approve, rfa_time, rfa_approver
  </sql>
</mapper>
