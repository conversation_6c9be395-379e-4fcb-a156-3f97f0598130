package com.exam.common.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 中铁局相关枚举
 *
 * @ClassName: RailwayEnums
 * @Description: 中铁局相关枚举
 * @Author: Xmj
 * @Date: 2025/5/21 14:47
 * @Version: 1.0
 */
public class RailwayEnums {

    public enum RFA_APPROVE{
        //待审核
        PENDING_APPROVAL(0, "待审核"),
        //通过
        PASS(1, "审核通过"),
        //驳回
        OVERRULE(2, "审核驳回");

        @Getter
        private final Integer code;
        @Getter
        private final String msg;

        RFA_APPROVE(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

    public static RFA_APPROVE getRfaApprove(final String msg) throws IllegalArgumentException {
        return Arrays.stream(RFA_APPROVE.values()).filter(m -> m.getMsg().equals(msg)).findFirst().orElse(null);
    }

    public static RFA_APPROVE getRfaApprove(final Integer code) throws IllegalArgumentException {
        return Arrays.stream(RFA_APPROVE.values()).filter(c -> c.getCode().equals(code)).findFirst().orElse(null);
    }

}
