package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StIssueAuthorityBo;
import com.exam.domain.vo.StIssueAuthorityVo;
import com.exam.service.IStIssueAuthorityService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 用户证件
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/issueAuthority")
public class StIssueAuthorityController extends BaseController {

    private final IStIssueAuthorityService iStIssueAuthorityService;

    /**
     * 查询用户证件列表
     */
    @SaCheckPermission("exam:issueAuthority:list")
    @GetMapping("/list")
    public TableDataInfo<StIssueAuthorityVo> list(StIssueAuthorityBo bo, PageQuery pageQuery) {
        return iStIssueAuthorityService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户证件列表
     */
    @SaCheckPermission("exam:issueAuthority:export")
    @Log(title = "用户证件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StIssueAuthorityBo bo, HttpServletResponse response) {
        List<StIssueAuthorityVo> list = iStIssueAuthorityService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户证件", StIssueAuthorityVo.class, response);
    }

    /**
     * 获取用户证件详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:issueAuthority:query")
    @GetMapping("/{id}")
    public R<StIssueAuthorityVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(iStIssueAuthorityService.queryById(id));
    }

    /**
     * 新增用户证件
     */
    @SaCheckPermission("exam:issueAuthority:add")
    @Log(title = "用户证件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StIssueAuthorityBo bo) {
        return toAjax(iStIssueAuthorityService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改用户证件
     */
    @SaCheckPermission("exam:issueAuthority:edit")
    @Log(title = "用户证件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StIssueAuthorityBo bo) {
        return toAjax(iStIssueAuthorityService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户证件
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:issueAuthority:remove")
    @Log(title = "用户证件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(iStIssueAuthorityService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
