package com.exam.domain;

import com.exam.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 试题对象 st_question
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_question")
public class StQuestion extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 试题类型
     */
    private Integer questionGenre;
    /**
     * 题干
     */
    private String questionContent;
    /**
     * 选项内容
     */
    private String optionContent;
    /**
     * 正确答案
     */
    private String rightKey;
    /**
     * 解析
     */
    private String analysis;
    /**
     * 试题类别
     */
    private Long questionType;
    /**
     * 图片路径
     */
    private String picPath;
    /**
     * 试题序号
     */
    private Long sn;
    /**
     * 是否公开 1 公有 0 私有
     */
    private Long isOpen;
    /**
     * 是否有关键词 1 是 0 否
     */
    private Long isKeyWord;
    /**
     * 背景资料
     */
    private String background;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 所属项目
     */
    private Long projectId;

}
