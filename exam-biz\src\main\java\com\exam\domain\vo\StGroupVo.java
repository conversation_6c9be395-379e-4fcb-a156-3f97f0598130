package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.core.domain.BaseEntity;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 组群视图对象 st_group
 *
 * <AUTHOR>
 * @date 2023-11-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ExcelIgnoreUnannotated
public class StGroupVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private String id;

    /**
     * 删除标识(0:未删除,其他删除)
     */
    private Integer delFlag;

    /**
     * 父id
     */
    @ExcelProperty(value = "父id")
    private Long fatherId;

    /**
     * 组群名称
     */
    @ExcelProperty(value = "组群名称")
    private String groupName;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long projectId;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;


    /**
     * 创建人姓名
     */
    private String userName;

    /**
     * 是否为二级组(1.是；0.否)
     */
    private String isSecLevel;

    /**
     * 二级组群
     */
    private List<StGroupVo> children;


    /**
     * 成员数量
     */
    private String countMem;
}
