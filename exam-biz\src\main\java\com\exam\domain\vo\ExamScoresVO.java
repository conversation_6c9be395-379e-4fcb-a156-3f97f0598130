package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 提交试卷后考试成绩页面用
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ExamScoresVO", description="考试成绩vo")
public class ExamScoresVO implements Serializable {


    /**
     * id
     */
    @ExcelProperty(value = "id")
    private String id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private String userId;

    /**
     * 第几次补考
     */
    @ExcelProperty(value = "第几次补考")
    private Integer retestTimes;

    /**
     * 允许补考次数
     */
    @ExcelProperty(value = "允许补考次数")
    private Integer allowedTimes;

    /**
     * 剩余考试次数
     */
    @ExcelProperty(value = "剩余考试次数")
    private Integer remainCount;

    /**
     * 答题卡id
     */
    @ExcelProperty(value = "答题卡id")
    private String answerCardId;

    /**
     * 成绩级别
     */
    @ExcelProperty(value = "成绩级别")
    private String achievemenLevel;

    /**
     * 总分数
     */
    @ExcelProperty(value = "总分数")
    private double totalScore;

    /**
     * 总答对数
     */
    @ExcelProperty(value = "总答对数")
    private Integer totalRightCount;

    /**
     * 总答错数
     */
    @ExcelProperty(value = "总答错数")
    private Integer totalWrongCount;

    /**
     * 单选题得分
     */
    @ExcelProperty(value = "单选题得分")
    private double singleChoiceScore;

    /**
     * 单选题答对数
     */
    @ExcelProperty(value = "单选题答对数")
    private Integer singleChoiceRightCount;

    /**
     * 单选题答错数
     */
    @ExcelProperty(value = "单选题答错数")
    private Integer singleChoiceWrongCount;

    /**
     * 单选题集合
     */
    @ExcelProperty(value = "单选题集合")
    private List<StAnswerCardDetailVo> singleChoiceList;

    /**
     * 多选题得分
     */
    @ExcelProperty(value = "多选题得分")
    private double multipleChoiceScore;

    /**
     * 多选题答对数
     */
    @ExcelProperty(value = "多选题答对数")
    private Integer multipleChoiceRightCount;

    /**
     * 多选题答错数
     */
    @ExcelProperty(value = "多选题答错数")
    private Integer multipleChoiceWrongCount;

    /**
     * 多选题集合
     */
    @ExcelProperty(value = "多选题集合")
    private List<StAnswerCardDetailVo> multipleChoiceList;

    /**
     * 判断题得分
     */
    @ExcelProperty(value = "判断题得分")
    private double judgeScore;

    /**
     * 判断题答对数
     */
    @ExcelProperty(value = "判断题答对数")
    private Integer judgeRightCount;

    /**
     * 判断题答错数
     */
    @ExcelProperty(value = "判断题答错数")
    private Integer judgeWrongCount;

    /**
     * 判断题集合
     */
    @ExcelProperty(value = "判断题集合")
    private List<StAnswerCardDetailVo> judgeList;

    /**
     * 填空题得分
     */
    @ExcelProperty(value = "填空题得分")
    private double completionScore;

    /**
     * 填空题答对数
     */
    @ExcelProperty(value = "填空题答对数")
    private Integer completionRightCount;

    /**
     * 填空题答错数
     */
    @ExcelProperty(value = "填空题答错数")
    private Integer completionWrongCount;

    /**
     * 填空题集合
     */
    @ExcelProperty(value = "填空题集合")
    private List<StAnswerCardDetailVo> completionList;

    /**
     * 简答题得分
     */
    @ExcelProperty(value = "简答题得分")
    private double shortAnswerScore;

    /**
     * 简答题答对数
     */
    @ExcelProperty(value = "简答题答对数")
    private Integer shortAnswerRightCount;

    /**
     * 简答题答错数
     */
    @ExcelProperty(value = "简答题答错数")
    private Integer shortAnswerWrongCount;

    /**
     * 填简答题集合
     */
    @ExcelProperty(value = "填简答题集合")
    private List<StAnswerCardDetailVo> shortAnswerList;

    /**
     * 案例题得分
     */
    @ExcelProperty(value = "案例题得分")
    private double caseScore;

    /**
     * 案例题答对数
     */
    @ExcelProperty(value = "案例题答对数")
    private Integer caseRightCount;

    /**
     * 案例题答错数
     */
    @ExcelProperty(value = "案例题答错数")
    private Integer caseWrongCount;

    /**
     * 案例题题集合
     */
    @ExcelProperty(value = "案例题题集合")
    private List<StAnswerCardDetailVo> caseList;

    public Integer getRemainCount() {
        if(allowedTimes == null){
            allowedTimes = 0;
        }
        if(retestTimes!=null){
            remainCount = allowedTimes - retestTimes;
            if(remainCount <0){
                return 0;
            }
        }else {
            remainCount = allowedTimes;
        }
        return remainCount+1;
    }
    /**
     * 剩余补考次数
     */
    @ExcelProperty(value = "剩余补考次数")
    private Integer remainRetestTimes;

    public Integer getRemainRetestTimes() {
        if((getRemainCount()-1)<0){
            return 0;
        }
        return getRemainCount()-1;
    }

//    public String getUserName() {
//        if(StringUtils.isBlank(userId)){
//            return "";
//        }
//        return UserHelpBean.getUserNameById(Long.parseLong(userId));
//    }

    //    /**
    //     * id
    //     */
    //    @ExcelProperty(value = "成绩级别显示")
    public String getAchievemenLevelDis() {
        return achievemenLevel;
    }
}
