package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 试题类别抽题比例视图对象 st_question_proportion
 *
 * <AUTHOR>
 * @date 2023-11-03
 */
@Data
@ExcelIgnoreUnannotated
public class StQuestionProportionVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private String id;

    /**
     * 试题类别
     */
    @ExcelProperty(value = "试题类别")
    private Long questionTypeId;

    /**
     * 试题比例
     */
    @ExcelProperty(value = "试题比例")
    private Integer questionProportion;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;


    /**
     * 类别名称
     */
    private String questionTypeName;


    /**
     * 试题数量
     */
    private String questionCount;

}
