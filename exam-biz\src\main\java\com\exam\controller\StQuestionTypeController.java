package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.R;
import com.exam.common.enums.BusinessType;
import com.exam.common.helper.LoginHelper;
import com.exam.domain.bo.StQuestionProportionBo;
import com.exam.domain.vo.StQuestionTypeVo;
import com.exam.service.IStQuestionProportionService;
import com.exam.service.IStQuestionTypeService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 试题类型
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/questionType")
public class StQuestionTypeController extends BaseController {

    @Resource
    private final IStQuestionTypeService iStQuestionTypeService;

    @Resource
    private IStQuestionProportionService iStQuestionProportionService;


    /**
     * 查询试题类别列表
     * @param questionTypeName 试题类别名称
     * @param createTime 创建时间
     * @param size 每页条数
     * @param current 当前页数
     * @return 满足条件的试题类型类别列表
     */
//    @SaCheckPermission("exam:questionType:list")
    @GetMapping("/list")
    @ResponseBody
    public R<IPage<StQuestionTypeVo>> selectStQuestionTypePage(@RequestParam(required = false) String questionTypeName,
        @RequestParam(required = false) String createTime, Integer size, Integer current) {
        Map conditionParam = new HashMap<String, String>();
        conditionParam.put("questionTypeName", questionTypeName);
        conditionParam.put("createTime", createTime);
        return R.ok(iStQuestionTypeService.selectStQuestionTypePage(new Page<>(current, size), conditionParam));
    }

//    /**
//     * 导出试题类型列表
//     */
//    @SaCheckPermission("exam:questionType:export")
//    @Log(title = "试题类型", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(StQuestionTypeBo bo, HttpServletResponse response) {
//        List<StQuestionTypeVo> list = iStQuestionTypeService.queryList(bo);
//        ExcelUtil.exportExcel(list, "试题类型", StQuestionTypeVo.class, response);
//    }

    /**
     * 获取试题类型详细信息
     *
     * @param id 主键
     */
//    @SaCheckPermission("exam:questionType:query")
    @GetMapping("/{id}")
    @ResponseBody
    public R<StQuestionTypeVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStQuestionTypeService.queryById(id));
    }

    /**
     * 保存试题类别
     */
//    @SaCheckPermission("exam:questionType:add")
    @Log(title = "试题类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    @ResponseBody
    public R<String> saveStQuestionType(@RequestBody StQuestionTypeVo stQuestionTypeVo) {
        long id = iStQuestionTypeService.saveStQuestionType(stQuestionTypeVo);
        return R.ok(String.valueOf(id));
    }

//    /**
//     * 修改试题类型
//     */
//    @SaCheckPermission("exam:questionType:edit")
//    @Log(title = "试题类型", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StQuestionTypeBo bo) {
//        return toAjax(iStQuestionTypeService.updateByBo(bo) ? 1 : 0);
//    }

    /**
     * 删除试题类型
     *
     * @param id 主键串
     */
//    @SaCheckPermission("exam:questionType:remove")
    @Log(title = "试题类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @ResponseBody
    public R<String> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable String id) {
        int ret = iStQuestionTypeService.deleteQuestionType(id);
        if (ret < 0) {
            return R.fail("该试题类别存在试题，不能删除");
        }
        return R.ok();
    }


    /**
     * 查询试题类别集合（级联）
     *
     * @param categoryName 类别名称
     * @param createTime 创建时间
     * @return 试题类别集合
     */
    @GetMapping(value = "/selectSecStQuestionTypeList")
    @ResponseBody
    public R<List<StQuestionTypeVo>> selectSecStQuestionTypeList(@RequestParam(required = false) String categoryName,
        @RequestParam(required = false) String createTime) {
        Map conditionParam = new HashMap<String, String>();
        conditionParam.put("categoryName", categoryName);
        conditionParam.put("createTime", createTime);
        conditionParam.put("projectId", LoginHelper.getProjectId());
        List<StQuestionTypeVo> courseCategoryVOs = iStQuestionTypeService.selectSecStQuestionTypeList(conditionParam);
        return R.ok(courseCategoryVOs);
    }

    /**
     * 查询试题类别
     *
     * @param questionTypeName 试题类别名称
     * @param createTime 创建时间
     * @param fatherId 父类别 ID
     */
    @GetMapping(value = "/selectStQuestionTypeList")
    @ResponseBody
    public R<List<StQuestionTypeVo>> selectStQuestionTypeList(@RequestParam(required = false) String questionTypeName,
        @RequestParam(required = false) String createTime, @RequestParam(required = false) String fatherId) {
        Map conditionParam = new HashMap<String, String>();
        conditionParam.put("questionTypeName", questionTypeName);
        conditionParam.put("createTime", createTime);
        conditionParam.put("fatherId", fatherId);
        conditionParam.put("projectId", LoginHelper.getProjectId());
        return R.ok(iStQuestionTypeService.selectStQuestionTypeList(conditionParam));
    }

    /**
     * 查询试题类别集合（级联全部可见）
     *
     * @param categoryName 类别名称
     * @param createTime 创建时间
     * @return 满足条件的试题类别集合
     */
    @GetMapping(value = "/selectSecStQuestionTypeListVisible")
    @ResponseBody
    public R<List<StQuestionTypeVo>> selectSecStQuestionTypeListVisible(@RequestParam(required = false) String categoryName,
        @RequestParam(required = false) String createTime) {
        Map conditionParam = new HashMap<String, String>();
        conditionParam.put("categoryName", categoryName);
        conditionParam.put("createTime", createTime);
        List<StQuestionTypeVo> courseCategoryVOs = iStQuestionTypeService.selectSecStQuestionTypeList(conditionParam);
        return R.ok(courseCategoryVOs);
    }


    /**
     * 查询试题类别树
     *
     * @param keyWord 关键字过滤
     * @return 满足条件的试题类别
     */
    @GetMapping(value = "/selectStQuestionTypeTree")
    @ResponseBody
    public R<List<StQuestionTypeVo>> selectStQuestionTypeTree(@RequestParam(required = false) String keyWord) {
        Map conditionParam = new HashMap<>();
        conditionParam.put("keyWord", keyWord);
        conditionParam.put("projectId", LoginHelper.getProjectId());
        conditionParam.put("tenantId", LoginHelper.getTenantId());
        return R.ok(iStQuestionTypeService.selectStQuestionTypeTree(conditionParam));
    }


    /**
     * 查询用户试题类别树 带当前公司比例
     */
    @GetMapping(value = "/selectStQuestionTypeTreeByUser")
    @ResponseBody
    public R<List<StQuestionTypeVo>> selectStQuestionTypeTreeByUser(String keyWord) {
        Map<String, Object> conditionParam = new HashMap<>();
        conditionParam.put("keyWord", keyWord);
        conditionParam.put("projectId", LoginHelper.getProjectId());
        conditionParam.put("tenantId", LoginHelper.getTenantId());

        // 查询当前公司的 比例
        StQuestionProportionBo stQuestionProportionQO = new StQuestionProportionBo();
        stQuestionProportionQO.setTenantId(LoginHelper.getTenantId());
        stQuestionProportionQO.setProjectId(LoginHelper.getProjectId());
        List<String> ids = iStQuestionProportionService.listQuestionTypeId(stQuestionProportionQO);
        conditionParam.put("ids", ids);
        List<StQuestionTypeVo> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ids)) {
            list = iStQuestionTypeService.selectStQuestionTypeTree(conditionParam);
        }
        return R.ok(list);
    }


//    @GetMapping(value = "/selectStQuestionTypeTreeByUser")
//    @ResponseBody
//    public R<List<StQuestionTypeVo>> selectStQuestionTypeTreeByUser(String keyWord) {
//
//        Map<String, Object> conditionParam = new HashMap<>();
//
//        conditionParam.put("keyWord", keyWord);
//        conditionParam.put("projectId", LoginHelper.getProjectId());
//        conditionParam.put("tenantId", LoginHelper.getTenantId());
//
//        // 查找试题类别
//
//        StQuestionProportionBo stQuestionProportionQO = new StQuestionProportionBo();
//        stQuestionProportionQO.setTenantId(LoginHelper.getTenantId());
//        stQuestionProportionQO.setProjectId(LoginHelper.getProjectId());
//        List<String> ids = iStQuestionProportionService.listQuestionTypeId(stQuestionProportionQO);
//
//        // 查询当前公司的 比例
//        List<StQuestionTypeVo> list = new ArrayList<>();
//        if (!CollectionUtils.isEmpty(ids)) {
//            conditionParam.put("ids", ids);
//            list = iStQuestionTypeService.selectStQuestionTypeTree(conditionParam);
//        }
//        return R.ok(list);
//    }
}
