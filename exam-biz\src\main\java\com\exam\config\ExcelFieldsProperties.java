package com.exam.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

@Configuration
@ConfigurationProperties(prefix = "fields", ignoreUnknownFields = false)
@PropertySource("classpath:config/excelFields.properties")
@Data
@Component
public class ExcelFieldsProperties {

    private String questionExport;
    private String questionImport;
}
