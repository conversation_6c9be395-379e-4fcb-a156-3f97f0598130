package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import java.math.BigDecimal;
import lombok.Data;


/**
 * 答题卡详细视图对象 st_mock_answer_card_detail
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@Data
@ExcelIgnoreUnannotated
public class StMockAnswerCardDetailVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 答题卡id
     */
    @ExcelProperty(value = "答题卡id")
    private Long answerCardId;

    /**
     * 试题id
     */
    @ExcelProperty(value = "试题id")
    private Long questionId;

    /**
     * 试题类型
     */
    @ExcelProperty(value = "试题类型")
    private Integer questionGenre;

    /**
     * 答案
     */
    @ExcelProperty(value = "答案")
    private String answer;

    /**
     * 题顺序
     */
    @ExcelProperty(value = "题顺序")
    private Long questionSn;

    /**
     * 此题得分
     */
    @ExcelProperty(value = "此题得分")
    private BigDecimal singleScore;

    /**
     * 判定结果（0：错误，1：正确）
     */
    @ExcelProperty(value = "判定结果", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=：错误，1：正确")
    private Long reviewResult;

    /**
     * 详细json
     */
    @ExcelProperty(value = "详细json")
    private String detailJson;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;


    /**
     * 选项内容
     */
    private String questionContent;

    /**
     * 选项答案
     */
    private String optionContent;


    /**
     * 正确答案
     */
    private String rightKey;

    /**
     * 解析
     */
    private String analysis;

    /**
     * 试题类别
     */
    private String questionType;
}
