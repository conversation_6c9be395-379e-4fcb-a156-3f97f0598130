package com.exam.controller;

import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.exam.common.annotation.Anonymous;
import com.exam.common.annotation.Log;
import com.exam.common.constant.UserConstants;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.R;
import com.exam.common.core.domain.entity.SysRole;
import com.exam.common.core.domain.entity.SysUser;
import com.exam.common.core.domain.model.LoginUser;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.helper.LoginHelper;
import com.exam.common.utils.StringUtils;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.*;
import com.exam.domain.vo.UserInfoVO;
import com.exam.service.RailwayProjectService;
import com.exam.service.RailwayUserInfoService;
import com.exam.system.service.ISysRoleService;
import com.exam.system.service.ISysUserEntityService;
import com.exam.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 国铁企业用户管理
 *
 * @ClassName: RailwayUserController
 * @Description: 国铁企业用户管理
 * @Author: Xmj
 * @Date: 2025/5/20 17:22
 * @Version: 1.0
 */
@RequestMapping("/railwayUser")
@RestController
@Api(tags = "国铁企业用户管理")
public class RailwayUserController extends BaseController {

    @Autowired
    private ISysUserService userService;
    @Autowired
    private RailwayUserInfoService userInfoService;
    @Autowired
    private ISysRoleService roleService;
    @Autowired
    private RailwayProjectService projectService;
    @Autowired
    private ISysUserEntityService userEntityService;

    @Anonymous
    @Log(title = "国铁企业用户管理", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增用户")
    @PostMapping(value = "/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody RailwayUserDto railwayUserDto) {

        LoginUser loginUser = LoginHelper.getLoginUser();
        Date date = new Date();
        SysUser sysUser = new SysUser();
        sysUser.setUserName(railwayUserDto.getPhone());
        sysUser.setNickName(railwayUserDto.getName());
        sysUser.setUserType("sys_user");
        sysUser.setPhonenumber(railwayUserDto.getPhone());
        sysUser.setIdNumber(railwayUserDto.getIdCard());
        String password = railwayUserDto.getPhone() + StringUtils.randomLetter(6);
        sysUser.setPassword(password);
        sysUser.setFirmType(railwayUserDto.getFirmType());
        sysUser.setRoleIds(railwayUserDto.getRoleIds());
        sysUser.setCreateBy(loginUser.getUserId());
        sysUser.setCreateTime(date);
        sysUser.setUpdateBy(loginUser.getUserId());
        sysUser.setUpdateTime(date);
        sysUser.setTenantId(railwayUserDto.getTenantId());
        sysUser.setProjectId(railwayUserDto.getProjectId());

        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(sysUser.getUserName()))) {
            return R.fail("新增用户'" + sysUser.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(sysUser.getPhonenumber())
            && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(sysUser))) {
            return R.fail("新增用户'" + sysUser.getUserName() + "'失败，手机号码已存在");
        }
        userService.insertUser(sysUser);
        if (railwayUserDto.getIsSendSms() == 1) {
            //todo 短信通知暂时没有模版
        }
        if (railwayUserDto.getFirmType() == 2) {
            LocalDateTime now = LocalDateTime.now();
            RailwayUserInfo railwayUserInfo = new RailwayUserInfo();
            railwayUserInfo.setUserId(sysUser.getUserId());
            railwayUserInfo.setRuiCertificateUnit(railwayUserDto.getCertificateUnit());
            railwayUserInfo.setRuiName(railwayUserDto.getName());
            railwayUserInfo.setRuiIdCard(railwayUserDto.getIdCard());
            railwayUserInfo.setCreateBy(loginUser.getUserId());
            railwayUserInfo.setCreateTime(now);
            railwayUserInfo.setUpdateBy(loginUser.getUserId());
            railwayUserInfo.setUpdateTime(now);
            railwayUserInfo.setProvinceId(railwayUserDto.getProvinceId());
            railwayUserInfo.setCityId(railwayUserDto.getCityId());
            railwayUserInfo.setCountyId(railwayUserDto.getCountyId());
            railwayUserInfo.setProvince(railwayUserDto.getProvince());
            railwayUserInfo.setCounty(railwayUserDto.getCounty());
            railwayUserInfo.setCounty(railwayUserDto.getCounty());
            userInfoService.save(railwayUserInfo);
        }
        return R.ok();
    }






    @Log(title = "国铁企业用户管理", businessType = BusinessType.IMPORT)
    @ApiOperation(value = "导入用户")
    @PostMapping(value = "/importUser", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importUser(@RequestPart("file") MultipartFile file) throws Exception{
        List<RailwayUserImportDto> result = ExcelUtil.importExcel(file.getInputStream(), RailwayUserImportDto.class);
        LoginUser loginUser = LoginHelper.getLoginUser();
        Date date = new Date();

        // 用于存储明文密码和密文密码的对应关系
        Map<String, String> passwordMap = new HashMap<>();

        for (RailwayUserImportDto railwayUserDto : result) {
            SysUser sysUser = new SysUser();
            sysUser.setUserName(railwayUserDto.getPhone());
            sysUser.setNickName(railwayUserDto.getName());
            sysUser.setUserType("sys_user");
            sysUser.setPhonenumber(railwayUserDto.getPhone());
            sysUser.setIdNumber(railwayUserDto.getIdCard());


            String plainPassword = railwayUserDto.getPhone();
            String hashedPassword = BCrypt.hashpw(plainPassword);

            sysUser.setPassword(hashedPassword);

            passwordMap.put(plainPassword, hashedPassword);

            if (loginUser.getTenantId() != null) {
                sysUser.setFirmType(2);
                sysUser.setTenantId(loginUser.getTenantId());
            }
            if (StringUtils.isNotEmpty(railwayUserDto.getRoleNames())) {
                List<SysRole> sysRoles = roleService.selectRoleAll();
                String[] roleNameArray = railwayUserDto.getRoleNames().split("/");
                Long[] roleIdArray = sysRoles.stream().map(r -> {
                    for (String roleName : roleNameArray) {
                        if (r.getRoleName().equals(roleName)) {
                            return r.getRoleId();
                        }
                    }
                    return null;
                }).filter(Objects::nonNull).toArray(Long[]::new);

                sysUser.setRoleIds(roleIdArray);
            }


            sysUser.setCreateBy(loginUser.getUserId());
            sysUser.setCreateTime(date);
            sysUser.setUpdateBy(loginUser.getUserId());
            sysUser.setUpdateTime(date);

            RailwayProject project = projectService.getOne(new LambdaQueryWrapper<RailwayProject>()
                .eq(RailwayProject::getRfId, loginUser.getTenantId())
                .eq(RailwayProject::getRpName, railwayUserDto.getProjectName()));
            sysUser.setProjectId(project.getRpId());
            userService.insertUser(sysUser);
            //todo 发送短信 通知用户账号密码 与 消息中心推送信息
        }


        if (!passwordMap.isEmpty()) {
            userEntityService.cachePassword(passwordMap);
        }


        return R.ok();
    }

    @ApiOperation(value = "查询用户列表")
    @PostMapping(value = "/queryUserPage")
    public R<IPage<UserInfoVO>> queryUserPage(@RequestBody RailwayUserPageDto railwayUserPageDto) {
        return R.ok(userInfoService.queryUserPage(railwayUserPageDto));
    }

    @Log(title = "国铁企业用户管理", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "用户启用/停用")
    @PostMapping(value = "/updateUserStatus")
    public R updateUserStatus(@RequestParam(value = "userId", required = false) Long userId,
                           @RequestParam(value = "status", required = false) String status) {
        SysUser user = new SysUser();
        user.setUserId(userId);
        user.setStatus(status);
        userService.updateUserStatus(user);
        return R.ok();
    }

    @Log(title = "国铁企业用户管理", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "重置密码")
    @PostMapping(value = "/resetPassword")
    public R resetPassword(@RequestParam(value = "userId", required = false) Long userId,
                           @RequestParam(value = "isSendSms", required = false) Integer isSendSms) {
        SysUser user = userService.selectUserById(userId);
        String password = user.getPhonenumber() + StringUtils.randomLetter(6);
        user.setPassword(password);
        userService.resetPwd(user);
        if (isSendSms == 1) {
            //todo 短信通知暂时没有模版
        }
        return R.ok();
    }
}
