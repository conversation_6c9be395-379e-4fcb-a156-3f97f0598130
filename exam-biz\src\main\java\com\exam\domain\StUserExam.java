package com.exam.domain;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <p>
 * 用户考试关系
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
@TableName("st_user_exam")
@ApiModel(value = "StUserExam对象", description = "用户考试关系")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StUserExam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty("考试id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long examId;

    @ApiModelProperty("用户id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    @ApiModelProperty("考场id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long examRoomId;

    @ApiModelProperty("考号")
    private String examNum;

    @ApiModelProperty("准考证号")
    private String admissionTicketNumber;

    @ApiModelProperty("是否参加考试（0否；1是）")
    private Integer joinFlag;

    @ApiModelProperty("是否生效（0否；1是）")
    private Integer effectFlag;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("修改人")
    private Long updateBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    private Long delFlag;

    @ApiModelProperty("租户id")
    private Long tenantId;


}
