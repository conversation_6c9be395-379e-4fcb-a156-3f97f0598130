package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.exam.domain.StDisputeComplaintDTO;
import com.exam.domain.bo.StDisputeComplaintBo;
import com.exam.domain.qo.StDisputeComplaintQo;
import com.exam.domain.vo.StDisputeComplaintVo;
import com.exam.service.IStDisputeComplaintService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 纠纷申诉
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/disputeComplaint")
public class StDisputeComplaintController extends BaseController {

    private final IStDisputeComplaintService iStDisputeComplaintService;

//    /**
//     * 查询纠纷申诉列表
//     */
//    @SaCheckPermission("exam:disputeComplaint:list")
//    @GetMapping("/list")
//    public TableDataInfo<StDisputeComplaintVo> list(StDisputeComplaintBo bo, PageQuery pageQuery) {
//        return iStDisputeComplaintService.queryPageList(bo, pageQuery);
//    }

    /**
     * 导出纠纷申诉列表
     */
    @SaCheckPermission("exam:disputeComplaint:export")
    @Log(title = "纠纷申诉", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StDisputeComplaintBo bo, HttpServletResponse response) {
        List<StDisputeComplaintVo> list = iStDisputeComplaintService.queryList(bo);
        ExcelUtil.exportExcel(list, "纠纷申诉", StDisputeComplaintVo.class, response);
    }

    /**
     * 获取纠纷申诉详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:disputeComplaint:query")
    @GetMapping("/{id}")
    public R<StDisputeComplaintVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStDisputeComplaintService.queryById(id));
    }

    /**
     * 新增纠纷申诉
     */
    @SaCheckPermission("exam:disputeComplaint:add")
    @Log(title = "纠纷申诉", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StDisputeComplaintBo bo) {
        return toAjax(iStDisputeComplaintService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改纠纷申诉
     */
    @SaCheckPermission("exam:disputeComplaint:edit")
    @Log(title = "纠纷申诉", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StDisputeComplaintBo bo) {
        return toAjax(iStDisputeComplaintService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除纠纷申诉
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:disputeComplaint:remove")
    @Log(title = "纠纷申诉", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStDisputeComplaintService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }


    /**
     * 发起纠纷申诉
     * @param stDisputeComplaintDTO
     * @return
     */
    @ApiOperationSupport(order = 1)
    @SaCheckPermission("exam:disputeComplaint:saveByInitiate")
    @PostMapping(value = "/saveByInitiate")
    @ApiOperation(value = "1.1 发起纠纷申诉")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> saveByInitiate(@RequestBody StDisputeComplaintDTO stDisputeComplaintDTO) {
        iStDisputeComplaintService.saveByInitiate(stDisputeComplaintDTO);
        return R.ok();
    }

    /**
     * 分页查询纠纷申诉列表
     */
    @ApiOperationSupport(order = 2)
    @GetMapping(value = "/pageDisputeComplaint")
    @ApiOperation(value = "1.2 分页查询纠纷申诉")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    @SaCheckPermission("exam:disputeComplaint:pageDisputeComplaint")
    public R<IPage<StDisputeComplaintVo>> pageDisputeComplaint(StDisputeComplaintQo stDisputeComplaintQO, PageQuery pageQuery) {
        return R.ok(iStDisputeComplaintService.selectPage(stDisputeComplaintQO,pageQuery));
    }

    /**
     * 提交纠纷申诉
     * @param stDisputeComplaintDTO
     * @return
     */
    @ApiOperationSupport(order = 3)
    @SaCheckPermission("exam:disputeComplaint:submitDisputeComplaint")
    @PostMapping(value = "/submitDisputeComplaint")
    @ApiOperation(value = "1.3 提交纠纷申诉")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> submitDisputeComplaint(@RequestBody StDisputeComplaintDTO stDisputeComplaintDTO) {
        iStDisputeComplaintService.submitDisputeComplaint(stDisputeComplaintDTO);
        return R.ok();
    }


}
