package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.page.TableDataInfo;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.bo.StScoreLevelBo;
import com.exam.domain.vo.StScoreLevelVo;
import com.exam.service.IStScoreLevelService;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 【请填写功能名称】
 *
 * <AUTHOR>
 * @date 2023-11-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/scoreLevel")
public class StScoreLevelController extends BaseController {

    private final IStScoreLevelService iStScoreLevelService;

    /**
     * 查询【请填写功能名称】列表
     */
    @SaCheckPermission("exam:scoreLevel:list")
    @GetMapping("/list")
    public TableDataInfo<StScoreLevelVo> list(StScoreLevelBo bo, PageQuery pageQuery) {
        return iStScoreLevelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @SaCheckPermission("exam:scoreLevel:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StScoreLevelBo bo, HttpServletResponse response) {
        List<StScoreLevelVo> list = iStScoreLevelService.queryList(bo);
        ExcelUtil.exportExcel(list, "【请填写功能名称】", StScoreLevelVo.class, response);
    }

    /**
     * 获取【请填写功能名称】详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:scoreLevel:query")
    @GetMapping("/{id}")
    public R<StScoreLevelVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStScoreLevelService.queryById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @SaCheckPermission("exam:scoreLevel:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StScoreLevelBo bo) {
        return toAjax(iStScoreLevelService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改【请填写功能名称】
     */
    @SaCheckPermission("exam:scoreLevel:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StScoreLevelBo bo) {
        return toAjax(iStScoreLevelService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除【请填写功能名称】
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:scoreLevel:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable Long[] ids) {
        return toAjax(iStScoreLevelService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
