package com.exam.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.exam.domain.StUserExam;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.exam.domain.StUserExamVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 用户考试关系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */

@Mapper
public interface StUserExamMapper extends BaseMapper<StUserExam> {


    @Select("SELECT\n" +
        "\tsue.*,\n" +
        "\tse.exam_type examType,\n" +
        "\tse.start_time startTime,\n" +
        "\tse.end_time endTime,\n" +
        "\tser.address,\n" +
        "\tser.`code`\n" +
        "FROM\n" +
        "\tst_user_exam sue\n" +
        "\tLEFT JOIN st_exam se ON se.id = sue.exam_id\n" +
        "\tLeft JOIN st_exam_room ser ON ser.id=sue.exam_room_id\n" +
        "where\n" +
        "sue.user_id=#{userId}\n" +
        "and (se.start_time>NOW() OR se.resit_start_time>NOW())")
    @InterceptorIgnore(tenantLine = "true")
    List<StUserExamVo> getList(Long userId);


    @Select("SELECT\n" +
        "\tsue.*,\n" +
        "\tse.exam_type examType,\n" +
        "\tse.start_time startTime,\n" +
        "\tse.end_time endTime,\n" +
        "\tser.address,\n" +
        "\tser.`code`\n" +
        "FROM\n" +
        "\tst_user_exam sue\n" +
        "\tLEFT JOIN st_exam se ON se.id = sue.exam_id\n" +
        "\tLeft JOIN st_exam_room ser ON ser.id=sue.exam_room_id\n" +
        "where\n" +
        "sue.id=#{examId}")
    StUserExam getOneById(Long examId);
}
