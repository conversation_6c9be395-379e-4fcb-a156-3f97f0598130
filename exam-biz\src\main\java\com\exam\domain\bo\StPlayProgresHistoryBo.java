package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 播放进度业务对象 st_play_progres_history
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StPlayProgresHistoryBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 课时id
     */
    @NotNull(message = "课时id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long classhourId;

    /**
     * 播放时长
     */
    @NotNull(message = "播放时长不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long playDuration;

    /**
     * 进度
     */
    @NotNull(message = "进度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long progres;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date createTime;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createBy;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateBy;

    /**
     * 播放状态(1.已看完；0.未看完)
     */
    @NotNull(message = "播放状态(1.已看完；0.未看完)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long playStatus;

    /**
     * 所属公司
     */
    @NotBlank(message = "所属公司不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;

    /**
     * 领域
     */
    @NotBlank(message = "领域不能为空", groups = { AddGroup.class, EditGroup.class })
    private String domainCode;


}
