package com.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.helper.LoginHelper;
import com.exam.domain.StExamRoom;
import com.exam.domain.StUserExam;
import com.exam.domain.vo.ExamRoomVo;
import com.exam.exception.BizException;
import com.exam.mapper.StExamRoomMapper;
import com.exam.service.IStExamRoomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.exam.service.IStUserExamService;
import com.exam.utils.SequenceBean;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 考场表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Service
@RequiredArgsConstructor
public class StExamRoomServiceImpl extends ServiceImpl<StExamRoomMapper, StExamRoom> implements IStExamRoomService {

    private final StExamRoomMapper stExamRoomMapper;

    private final IStUserExamService iStUserExamService;

    @Override
    public IPage<ExamRoomVo> getExamRoomList(Page page, String name, Long provinceId, Long cityId, Long countyId, Long status) {
        IPage<ExamRoomVo> ipage = stExamRoomMapper.getExamRoomList(page,name,provinceId,cityId,countyId,status);
        return ipage;
    }

    @Override
    public void saveExamRoom(StExamRoom stExamRoom) {
        List<StExamRoom> list = list(new LambdaQueryWrapper<StExamRoom>().eq(StExamRoom::getName, stExamRoom.getName()));
        if(list==null||list.size()>0){
            throw new BizException("考场名称重复");
        }
        int count = stExamRoomMapper.getCount();
        String code = String.format("%04d", count+1);
        stExamRoom.setId(SequenceBean.getSequence());
        stExamRoom.setStatus(1L);
        stExamRoom.setTenantId(LoginHelper.getTenantId());
        stExamRoom.setCode(code);
        save(stExamRoom);
    }

    @Override
    public void updateExamRoom(StExamRoom stExamRoom) {
        List<StExamRoom> list = list(new LambdaQueryWrapper<StExamRoom>()
            .eq(StExamRoom::getName, stExamRoom.getName())
            .ne(StExamRoom::getId,stExamRoom.getId())
        );
        if(list==null||list.size()>0){
            throw new BizException("考场名称重复");
        }
        updateById(stExamRoom);
    }

    @Override
    public void enableExamRoom(Long id, Long status) {
        if(status.equals(2L)){
            //校验停用
            List<StUserExam> list = iStUserExamService.list(new LambdaQueryWrapper<StUserExam>().eq(StUserExam::getExamRoomId, id).eq(StUserExam::getDelFlag, 0));
            if(list!=null&&list.size()>0){
                throw new BizException("考场已预约考试，无法停用！");
            }
        }
        LambdaUpdateWrapper<StExamRoom> stExamRoomLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        stExamRoomLambdaUpdateWrapper.eq(StExamRoom::getId,id);
        stExamRoomLambdaUpdateWrapper.set(StExamRoom::getStatus,status);
        stExamRoomLambdaUpdateWrapper.set(StExamRoom::getUpdateBy,LoginHelper.getUserId());
        stExamRoomLambdaUpdateWrapper.set(StExamRoom::getUpdateTime,new Date());
        update(stExamRoomLambdaUpdateWrapper);
    }

    @Override
    public List<ExamRoomVo> getNotOccupiedExamRoomList() {
        return stExamRoomMapper.getNotOccupiedExamRoomList();
    }
}
