<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.exam.mapper.RailwayExamRoomMapper">
  <resultMap id="BaseResultMap" type="com.exam.domain.RailwayExamRoom">
    <!--@mbg.generated-->
    <!--@Table railway_exam_room-->
    <id column="rer_id" jdbcType="BIGINT" property="rerId" />
    <result column="rf_id" jdbcType="BIGINT" property="rfId" />
    <result column="rer_name" jdbcType="VARCHAR" property="rerName" />
    <result column="rer_address" jdbcType="VARCHAR" property="rerAddress" />
    <result column="rer_people_count" jdbcType="INTEGER" property="rerPeopleCount" />
    <result column="rer_status" jdbcType="BOOLEAN" property="rerStatus" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    rer_id, rf_id, rer_name, rer_address, rer_people_count, rer_status, create_by, create_time,
    update_by, update_time
  </sql>
</mapper>
