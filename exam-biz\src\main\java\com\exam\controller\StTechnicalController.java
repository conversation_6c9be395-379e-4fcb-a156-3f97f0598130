package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StTechnicalBo;
import com.exam.domain.vo.StTechnicalVo;
import com.exam.service.IStTechnicalService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 用户证件
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/technical")
public class StTechnicalController extends BaseController {

    private final IStTechnicalService iStTechnicalService;

    /**
     * 查询用户证件列表
     */
    @SaCheckPermission("exam:technical:list")
    @GetMapping("/list")
    public TableDataInfo<StTechnicalVo> list(StTechnicalBo bo, PageQuery pageQuery) {
        return iStTechnicalService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户证件列表
     */
    @SaCheckPermission("exam:technical:export")
    @Log(title = "用户证件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StTechnicalBo bo, HttpServletResponse response) {
        List<StTechnicalVo> list = iStTechnicalService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户证件", StTechnicalVo.class, response);
    }

    /**
     * 获取用户证件详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:technical:query")
    @GetMapping("/{id}")
    public R<StTechnicalVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(iStTechnicalService.queryById(id));
    }

    /**
     * 新增用户证件
     */
    @SaCheckPermission("exam:technical:add")
    @Log(title = "用户证件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StTechnicalBo bo) {
        return toAjax(iStTechnicalService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改用户证件
     */
    @SaCheckPermission("exam:technical:edit")
    @Log(title = "用户证件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StTechnicalBo bo) {
        return toAjax(iStTechnicalService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户证件
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:technical:remove")
    @Log(title = "用户证件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(iStTechnicalService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
