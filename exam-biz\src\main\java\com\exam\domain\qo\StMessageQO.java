package com.exam.domain.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 消息通知QO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="StMessageQO对象", description="消息通知QO")
public class StMessageQO extends SearchQO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "消息类型")
    private String msgType;

    @ApiModelProperty(value = "领域")
    private Long projectId;

}
