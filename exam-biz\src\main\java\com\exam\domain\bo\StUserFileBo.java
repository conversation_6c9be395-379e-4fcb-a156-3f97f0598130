package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 用户文件业务对象 st_user_file
 *
 * <AUTHOR>
 * @date 2023-11-24
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StUserFileBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userInfoId;

    /**
     * 文件相对路径
     */
    @NotBlank(message = "文件相对路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String path;

    /**
     * 图片类型  （0：身份证正面， 1：身份证反面）
     */
    @NotNull(message = "图片类型  （0：身份证正面， 1：身份证反面）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long type;

    /**
     * 人脸识别失败照片  文件相对路径
     */
    @NotBlank(message = "人脸识别失败照片  文件相对路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String faceFailPath;


}
