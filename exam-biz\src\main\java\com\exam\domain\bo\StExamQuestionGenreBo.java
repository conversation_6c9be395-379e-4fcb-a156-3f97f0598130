package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 试题类型业务对象 st_exam_question_genre
 *
 * <AUTHOR>
 * @date 2023-11-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StExamQuestionGenreBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 考试id
     */
    @NotNull(message = "考试id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long examId;

    /**
     * 试题类别id
     */
    @NotBlank(message = "试题类别id不能为空", groups = {AddGroup.class, EditGroup.class})
    private String questionTypeId;

    /**
     * 试题题型类型id
     */
    @NotNull(message = "试题题型类型id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long questionGenreId;

    /**
     * 试题题型类型分值
     */
    @NotNull(message = "试题题型类型分值不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal questionGenreScore;

    /**
     * 试题题型类型个数
     */
    @NotNull(message = "试题题型类型个数不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer questionGenreCount;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;


}
