package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;
import lombok.Data;


/**
 * 试题类型视图对象 st_exam_question_genre
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Data
@ExcelIgnoreUnannotated
public class StExamQuestionGenreVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 考试id
     */
    @ExcelProperty(value = "考试id")
    private Long examId;

    /**
     * 试题类别id
     */
    @ExcelProperty(value = "试题类别id")
    private String questionTypeId;

    /**
     * 试题题型类型id
     */
    @ExcelProperty(value = "试题题型类型id")
    private Long questionGenreId;

    /**
     * 试题题型类型分值
     */
    @ExcelProperty(value = "试题题型类型分值")
    private double questionGenreScore;

    /**
     * 试题题型类型个数
     */
    @ExcelProperty(value = "试题题型类型个数")
    private Integer questionGenreCount;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;


}
