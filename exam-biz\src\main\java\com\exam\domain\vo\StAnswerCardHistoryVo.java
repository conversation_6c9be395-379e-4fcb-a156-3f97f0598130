package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;
import lombok.Data;


/**
 * 答题卡视图对象 st_answer_card_history
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
@Data
@ExcelIgnoreUnannotated
public class StAnswerCardHistoryVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 考试id
     */
    @ExcelProperty(value = "考试id")
    private Long examId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 答题卡id
     */
    @ExcelProperty(value = "答题卡id")
    private Long answerId;

    /**
     * 答题卡状态
     */
    @ExcelProperty(value = "答题卡状态")
    private Long answerCardStatus;

    /**
     * 分数
     */
    @ExcelProperty(value = "分数")
    private BigDecimal score;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long disputeStatus;

    /**
     * 补考次数
     */
    @ExcelProperty(value = "补考次数")
    private Long retestTimes;

    /**
     * 是否强制交卷(1.是；0.否)
     */
    @ExcelProperty(value = "是否强制交卷(1.是；0.否)")
    private Long isForced;

    /**
     * 是否为最新
     */
    @ExcelProperty(value = "是否为最新")
    private String isLatest;

    /**
     * 是否通过(1.是；0.否)
     */
    @ExcelProperty(value = "是否通过(1.是；0.否)")
    private String isPass;

    /**
     * (1:PC,2:APP)
     */
    @ExcelProperty(value = "(1:PC,2:APP)")
    private String terminal;

    /**
     * 提交原因
     */
    @ExcelProperty(value = "提交原因")
    private String submitReason;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long historyRank;

    /**
     * 租户 ID
     */
    @ExcelProperty(value = "租户 ID")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;


}
