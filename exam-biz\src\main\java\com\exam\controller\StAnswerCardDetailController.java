package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.page.TableDataInfo;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.bo.StAnswerCardDetailBo;
import com.exam.domain.vo.StAnswerCardDetailVo;
import com.exam.service.IStAnswerCardDetailService;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 答题卡详细
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/answerCardDetail")
public class StAnswerCardDetailController extends BaseController {

    private final IStAnswerCardDetailService iStAnswerCardDetailService;

    /**
     * 查询答题卡详细列表
     */
    @SaCheckPermission("exam:answerCardDetail:list")
    @GetMapping("/list")
    public TableDataInfo<StAnswerCardDetailVo> list(StAnswerCardDetailBo bo, PageQuery pageQuery) {
        return iStAnswerCardDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出答题卡详细列表
     */
    @SaCheckPermission("exam:answerCardDetail:export")
    @Log(title = "答题卡详细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StAnswerCardDetailBo bo, HttpServletResponse response) {
        List<StAnswerCardDetailVo> list = iStAnswerCardDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "答题卡详细", StAnswerCardDetailVo.class, response);
    }

    /**
     * 获取答题卡详细详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:answerCardDetail:query")
    @GetMapping("/{id}")
    public R<StAnswerCardDetailVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStAnswerCardDetailService.queryById(id));
    }

    /**
     * 新增答题卡详细
     */
    @SaCheckPermission("exam:answerCardDetail:add")
    @Log(title = "答题卡详细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StAnswerCardDetailBo bo) {
        return toAjax(iStAnswerCardDetailService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改答题卡详细
     */
    @SaCheckPermission("exam:answerCardDetail:edit")
    @Log(title = "答题卡详细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StAnswerCardDetailBo bo) {
        return toAjax(iStAnswerCardDetailService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除答题卡详细
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:answerCardDetail:remove")
    @Log(title = "答题卡详细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable Long[] ids) {
        return toAjax(iStAnswerCardDetailService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
