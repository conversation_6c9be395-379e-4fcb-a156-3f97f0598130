package com.exam.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 案例题分数比例视图对象 st_case_question_score_ratio
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StCaseQuestionScoreRatioVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 试题id
     */
    @ExcelProperty(value = "试题id")
    private Long questionId;

    /**
     * 关键词内容
     */
    @ExcelProperty(value = "关键词内容")
    private String keyWordContent;

    /**
     * 分值占比
     */
    @ExcelProperty(value = "分值占比")
    private Long scoreProportion;

    /**
     * 是否删除(1.是；0.否)
     */
    @ExcelProperty(value = "是否删除(1.是；0.否)")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long createBy;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long updateBy;


}
