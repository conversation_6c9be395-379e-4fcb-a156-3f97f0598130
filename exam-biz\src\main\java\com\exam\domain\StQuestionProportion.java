package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 试题类别抽题比例对象 st_question_proportion
 *
 * <AUTHOR>
 * @date 2023-11-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_question_proportion")
public class StQuestionProportion extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private String id;
    /**
     * 试题类别
     */
    private Long questionTypeId;
    /**
     * 试题比例
     */
    private Integer questionProportion;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 所属项目
     */
    private Long projectId;

}
