<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.exam.mapper.RailwayMessageMapper">
  <resultMap id="BaseResultMap" type="com.exam.domain.RailwayMessage">
    <!--@mbg.generated-->
    <!--@Table railway_message-->
    <id column="rm_id" jdbcType="BIGINT" property="rmId" />
    <result column="rm_title" jdbcType="VARCHAR" property="rmTitle" />
    <result column="rm_content" jdbcType="VARCHAR" property="rmContent" />
    <result column="rm_status" jdbcType="BOOLEAN" property="rmStatus" />
    <result column="rm_read" jdbcType="TIMESTAMP" property="rmRead" />
    <result column="rm_send_user" jdbcType="TIMESTAMP" property="rmSendUser" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    rm_id, rm_title, rm_content, rm_status, rm_read, rm_send_user, create_by, create_time,
    update_by, update_time
  </sql>
</mapper>
