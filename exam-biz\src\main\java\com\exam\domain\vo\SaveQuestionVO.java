package com.exam.domain.vo;

import com.alibaba.fastjson.JSON;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <p>
 * 导入试题成功失败数
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-10
 */
@Data
@AllArgsConstructor
public class SaveQuestionVO {

    public SaveQuestionVO() {
        init();
    }

    private void init() {
        count = 0;
        successCount = 0;
        repeatCount = 0;
        singleChoiceCount = 0;
        singleChoiceSuccessCount = 0;
        singleChoiceRepeatCount = 0;
        singleChoiceRepeatList = new ArrayList<>();
        multipleChoiceCount = 0;
        multipleChoiceCount = 0;
        multipleChoiceSuccessCount = 0;
        multipleChoiceRepeatCount = 0;
        multipleChoiceRepeatList = new ArrayList<>();
        trueFalseCount = 0;
        trueFalseSuccessCount = 0;
        trueFalseRepeatCount = 0;
        trueFalseRepeatList = new ArrayList<>();
        fillInBlankCount = 0;
        fillInBlankSuccessCount = 0;
        fillInBlankRepeatCount = 0;
        fillInBlankRepeatList = new ArrayList<>();
        shortAnswerCount = 0;
        shortAnswerSuccessCount = 0;
        shortAnswerRepeatCount = 0;
        shortAnswerRepeatList = new ArrayList<>();
    }

    /**
     * 试题总数量
     */
    private Integer count;

    /**
     * 试题成功数量
     */
    private Integer successCount;

    /**
     * 试题重复数量
     */
    private Integer repeatCount;

    /**
     * 单选总数量
     */
    private Integer singleChoiceCount;

    /**
     * 单选成功数量
     */
    private Integer singleChoiceSuccessCount;

    /**
     * 单选重复数量
     */
    private Integer singleChoiceRepeatCount;

    /**
     * 单选重复导入题
     */
    private List<Integer> singleChoiceRepeatList;

    /**
     * 多选总数量
     */
    private Integer multipleChoiceCount;

    /**
     * 多选成功数量
     */
    private Integer multipleChoiceSuccessCount;

    /**
     * 多选重复数量
     */
    private Integer multipleChoiceRepeatCount;

    /**
     * 多选重复导入题
     */
    private List<Integer> multipleChoiceRepeatList;

    /**
     * 判断总数量
     */
    private Integer trueFalseCount;

    /**
     * 判断成功数量
     */
    private Integer trueFalseSuccessCount;

    /**
     * 判断重复数量
     */
    private Integer trueFalseRepeatCount;

    /**
     * 判断重复导入题
     */
    private List<Integer> trueFalseRepeatList;

    /**
     * 填空题总数量
     */
    private Integer fillInBlankCount;

    /**
     * 填空题成功数量
     */
    private Integer fillInBlankSuccessCount;

    /**
     * 填空题重复数量
     */
    private Integer fillInBlankRepeatCount;

    /**
     * 填空重复导入题
     */
    private List<Integer> fillInBlankRepeatList;

    /**
     * 简答总数量
     */
    private Integer shortAnswerCount;

    /**
     * 简答成功数量
     */
    private Integer shortAnswerSuccessCount;

    /**
     * 简答重复数量
     */
    private Integer shortAnswerRepeatCount;

    /**
     * 简答重复导入题
     */
    private List<Integer> shortAnswerRepeatList;

    /**
     * 导入文件路径
     */
    private String importFilePath;

    public void increaseCount(int questionGenreId, long isRepeat) {
        this.count++;
        switch (questionGenreId) {
            case 1:
                this.singleChoiceCount++;
                if (isRepeat > 0) {
                    this.singleChoiceSuccessCount++;
                    this.successCount++;
                } else {
                    this.singleChoiceRepeatCount++;
                    this.repeatCount++;
                    singleChoiceRepeatList.add(this.singleChoiceCount);
                }
                break;
            case 2:
                this.multipleChoiceCount++;
                if (isRepeat > 0) {
                    this.multipleChoiceSuccessCount++;
                    this.successCount++;
                } else {
                    this.multipleChoiceRepeatCount++;
                    this.repeatCount++;
                    multipleChoiceRepeatList.add(this.multipleChoiceCount);
                }
                break;
            case 3:
                this.trueFalseCount++;
                if (isRepeat > 0) {
                    this.trueFalseSuccessCount++;
                    this.successCount++;
                } else {
                    this.trueFalseRepeatCount++;
                    this.repeatCount++;
                    trueFalseRepeatList.add(this.trueFalseCount);
                }
                break;
            case 4:
                this.fillInBlankCount++;
                if (isRepeat > 0) {
                    this.successCount++;
                    this.fillInBlankSuccessCount++;
                } else {
                    this.fillInBlankRepeatCount++;
                    this.repeatCount++;
                    fillInBlankRepeatList.add(this.fillInBlankCount);
                }
                break;
            case 5:
                this.shortAnswerCount++;
                if (isRepeat > 0) {
                    this.shortAnswerSuccessCount++;
                    this.successCount++;
                } else {
                    this.shortAnswerRepeatCount++;
                    this.repeatCount++;
                    shortAnswerRepeatList.add(this.shortAnswerCount);
                }
                break;
        }
    }

    public int getCount(int questionGenreId) {
        int count = 0;
        switch (questionGenreId) {
            case 1:
                count = this.singleChoiceCount;
                break;
            case 2:
                count = this.multipleChoiceCount;
                break;
            case 3:
                count = this.trueFalseCount;
                break;
            case 4:
                count = this.fillInBlankCount;
                break;
            case 5:
                count = this.shortAnswerCount;
                break;
        }
        return count + 1;
    }

    public String getMsg() {
        StringBuffer sb = new StringBuffer();
        sb.append("试题总数：").append(this.count);
        sb.append("<br>试题导入成功数：").append(this.successCount);
        sb.append("<br>试题导入重复数：").append(this.repeatCount);

        if (this.singleChoiceCount > 0) {
            sb.append("<br><br>单选试题总数：").append(this.singleChoiceCount);
            sb.append("<br>单选试题导入成功数：").append(this.singleChoiceSuccessCount);
            sb.append("<br>单选试题导入重复数：").append(this.singleChoiceRepeatCount);
            if (this.singleChoiceRepeatCount > 0) {
                sb.append("<br>单选试题重复序号：").append(JSON.toJSONString(this.singleChoiceRepeatList));
            }
        }

        if (this.multipleChoiceCount > 0) {
            sb.append("<br><br>多选试题总数：").append(this.multipleChoiceCount);
            sb.append("<br>多选试题导入成功数：").append(this.multipleChoiceSuccessCount);
            sb.append("<br>多选试题导入重复数：").append(this.multipleChoiceRepeatCount);
            if (this.multipleChoiceRepeatCount > 0) {
                sb.append("<br>多选试题重复序号：").append(JSON.toJSONString(this.multipleChoiceRepeatList));
            }
        }

        if (this.trueFalseCount > 0) {
            sb.append("<br><br>判断试题总数：").append(this.trueFalseCount);
            sb.append("<br>判断试题导入成功数：").append(this.trueFalseSuccessCount);
            sb.append("<br>判断试题导入重复数：").append(this.trueFalseRepeatCount);
            if (this.trueFalseRepeatCount > 0) {
                sb.append("<br>判断试题重复序号：").append(JSON.toJSONString(this.trueFalseRepeatList));
            }
        }

        if (this.fillInBlankCount > 0) {
            sb.append("<br><br>填空试题总数：").append(this.fillInBlankCount);
            sb.append("<br>填空试题导入成功数：").append(this.fillInBlankSuccessCount);
            sb.append("<br>填空试题导入重复数：").append(this.fillInBlankRepeatCount);
            if (this.fillInBlankRepeatCount > 0) {
                sb.append("<br>填空试题重复序号：").append(JSON.toJSONString(this.fillInBlankRepeatList));
            }
        }

        if (this.shortAnswerCount > 0) {
            sb.append("<br><br>简答试题总数：").append(this.shortAnswerCount);
            sb.append("<br>简答试题导入成功数：").append(this.shortAnswerSuccessCount);
            sb.append("<br>简答试题导入重复数：").append(this.shortAnswerRepeatCount);
            if (this.shortAnswerRepeatCount > 0) {
                sb.append("<br>简答试题重复序号：").append(JSON.toJSONString(this.shortAnswerRepeatList));
            }
        }

        return sb.toString();
    }

}
