package com.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.exam.common.annotation.Anonymous;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.R;
import com.exam.common.core.page.TableDataInfo;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.domain.RailwayFirmDto;
import com.exam.domain.vo.RailwayFirmTreeVo;
import com.exam.domain.vo.RailwayFirmVo;
import com.exam.service.RailwayFirmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 企业管理
 *
 * @ClassName: RailwayFirmController
 * @Description: 企业管理
 * @Author: Xmj
 * @Date: 2025/5/20 17:22
 * @Version: 1.0
 */
@RequestMapping("/railwayFirm")
@RestController
@Api(tags = "企业管理")
public class RailwayFirmController extends BaseController {

    @Autowired
    private RailwayFirmService railwayFirmService;

    @Anonymous
    @Log(title = "企业管理", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增企业管理")
    @PostMapping(value = "/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody RailwayFirmDto firmDto) {
        railwayFirmService.add(firmDto);
        return R.ok();
    }


    @Log(title = "企业管理", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "根据企业Id rfId编辑企业管理")
    @PostMapping(value = "/editById")
    public R<Void> editById(@Validated(EditGroup.class) @RequestBody RailwayFirmDto firmDto) {
        railwayFirmService.editById(firmDto);
        return R.ok();
    }


    @ApiOperation(value = "查询企业管理")
    @GetMapping(value = "/queryPage")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "rfName", value = "企业名称", paramType = "query", dataTypeClass = String.class),
        @ApiImplicitParam(name = "rfContact", value = "联系人", paramType = "query", dataTypeClass = String.class),
        @ApiImplicitParam(name = "rfPhone", value = "联系人手机号", paramType = "query", dataTypeClass = String.class),
        @ApiImplicitParam(name = "rfaApprove", value = "审核状态：0未审核｜1通过｜2驳回", paramType = "query", dataTypeClass = Integer.class),
        @ApiImplicitParam(name = "size", value = "页码", paramType = "query", dataTypeClass = Integer.class),
        @ApiImplicitParam(name = "current", value = "每页打小", paramType = "query", dataTypeClass = Integer.class),
    })
    public R<IPage<RailwayFirmVo>> queryPage(String rfName,
                                                  String rfContact,
                                                  String rfPhone,
                                                  Integer rfaApprove,
                                                  Integer size,
                                                  Integer current) {
        IPage<RailwayFirmVo> queryPage = railwayFirmService.queryPage(rfName, rfContact, rfPhone, rfaApprove, size, current);
        return R.ok(queryPage);
    }

    @ApiOperation(value = "/根据企业Id查询企业详细信息")
    @GetMapping(value = "/queryByRfId")
    @ApiImplicitParam(name = "rfId", value = "企业名称", paramType = "query", dataTypeClass = Long.class)
    public R<RailwayFirmVo> queryByRfId(Long rfId) {
        return R.ok(railwayFirmService.queryByRfId(rfId));
    }


    @ApiOperation(value = "组织架构")
    @GetMapping(value = "/queryFirmTree")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "rfName", value = "企业名称", paramType = "query", dataTypeClass = String.class),
        @ApiImplicitParam(name = "rfId", value = "企业Id", paramType = "query", dataTypeClass = Long.class),
        @ApiImplicitParam(name = "isParent", value = "是否只查企业：1是｜0否", paramType = "query", dataTypeClass = Integer.class),
    })
    public R<List<RailwayFirmTreeVo>> queryFirmTree(String rfName,
                                                    Long rfId,
                                                    Integer isParent) {
        List<RailwayFirmTreeVo> queryFirmTree = railwayFirmService.queryFirmTree(rfName, rfId, isParent);
        return R.ok(queryFirmTree);
    }

}
