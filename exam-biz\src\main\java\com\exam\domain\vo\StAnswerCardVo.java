package com.exam.domain.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import com.exam.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 答题卡视图对象 st_answer_card
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
@Data
@ExcelIgnoreUnannotated
public class StAnswerCardVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 预设id
     */
    @ExcelProperty(value = "预设id")
    private Long preId;

    /**
     * 考试id
     */
    @ExcelProperty(value = "考试id")
    private Long examId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 答题卡状态
     */
    @ExcelProperty(value = "答题卡状态")
    private Long answerCardStatus;

    /**
     * 单选题得分
     */
    @ExcelProperty(value = "单选题得分")
    private  double singleChoiceScore;

    /**
     * 多选题得分
     */
    @ExcelProperty(value = "多选题得分")
    private  double multipleChoiceScore;

    /**
     * 判断题得分
     */
    @ExcelProperty(value = "判断题得分")
    private  double judgeScore;

    /**
     * 分数
     */
    @ExcelProperty(value = "分数")
    private Double score;

    /**
     * 及格分数
     */
    private double passScore;

    /**
     * 申诉状态(-1：拒绝，0：未审核，1：通过)
     */
    @ExcelProperty(value = "申诉状态(-1：拒绝，0：未审核，1：通过)")
    private Long disputeStatus;

    /**
     * 补考次数
     */
    @ExcelProperty(value = "补考次数")
    private Long retestTimes;

    /**
     * 是否强制交卷(1.是；0.否)
     */
    @ExcelProperty(value = "是否强制交卷(1.是；0.否)")
    private Long isForced;

    /**
     * 是否为最新
     */
    @ExcelProperty(value = "是否为最新")
    private String isLatest;

    /**
     * 是否通过(1.是；0.否)
     */
    @ExcelProperty(value = "是否通过(1.是；0.否)")
    private String isPass;

    /**
     * (1:PC,2:APP)
     */
    @ExcelProperty(value = "(1:PC,2:APP)")
    private String terminal;

    /**
     * 提交原因
     */
    @ExcelProperty(value = "提交原因")
    private String submitReason;


    /**
     * 阅卷状态
     */
    @ExcelProperty(value = "阅卷状态")
    private Long reviewStatus;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;

    /**
     * 考试类型：0：模拟/1：普通
     */
    @ExcelProperty(value = "考试类型：0：模拟/1：普通")
    private int examType;

    /**
     * 答题卡详细list
     */
    @ExcelProperty(value = "答题卡详细list")
    private List<StAnswerCardDetailVo> answerCardDetailVOList;

    /**
     * 是否手动阅卷(1.是；0.否)
     */
    @ExcelProperty(value = "是否手动阅卷(1.是；0.否)")
    private String isManualReview;

    /**
     * 是否自动阅卷
     */
    @ExcelProperty(value = "是否自动阅卷")
    private int isAutoManualReview;

    /**
     * 是否考试过
     */
    @ExcelProperty(value = "是否考试过")
    private String examDisputed;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
