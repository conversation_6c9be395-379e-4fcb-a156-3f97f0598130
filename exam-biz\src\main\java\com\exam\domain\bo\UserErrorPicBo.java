package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 【请填写功能名称】业务对象 user_error_pic
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class UserErrorPicBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 身份证正面照片地址
     */
    @NotBlank(message = "身份证正面照片地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idImageUrl;

    /**
     * 错误图片base64编码
     */
    @NotBlank(message = "错误图片base64编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String errorPicBase;

    /**
     * 注册时间(显示使用)
     */
    @NotBlank(message = "注册时间(显示使用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String signTimeStr;

    /**
     * 编辑时间(显示使用)
     */
    @NotBlank(message = "编辑时间(显示使用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String updateTimeStr;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long signUserId;

    /**
     * 编辑人id
     */
    @NotNull(message = "编辑人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateUserId;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @NotNull(message = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dataStatus;

    /**
     * 错误信息
     */
    @NotBlank(message = "错误信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String errorMessage;

    /**
     * 错误编码
     */
    @NotBlank(message = "错误编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String errorCode;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;


}
