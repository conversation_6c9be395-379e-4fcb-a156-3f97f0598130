package com.exam.utils;


import com.baomidou.mybatisplus.core.toolkit.Sequence;

/**
 * <AUTHOR>
 * @date 2021/03/08
 */
public class SequenceBean {


    private volatile static Sequence sequence ;

    private SequenceBean() {}

    public static long getSequence() {
        if (sequence == null) {
            synchronized (SequenceBean.class) {
                if (sequence == null) {
                    Long workerId = Long.valueOf((String) SpringContextUtil.getProperty("sequence.workerid"));
                    Long datacenterId = Long.valueOf((String) SpringContextUtil.getProperty("sequence.dtacenterid"));
                    sequence = new Sequence(workerId, datacenterId);
                }
            }
        }
        return sequence.nextId();
    }

}
