package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.constant.CommonDataBaseConst;
import com.exam.utils.GyUtils;
import com.exam.utils.SpringContextUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 试题视图对象 st_question
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@Data
@ExcelIgnoreUnannotated
public class StQuestionVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 课程id
     */
    @ExcelProperty(value = "课程id")
    private String courseId;

    /**
     * 试题类型
     */
    @ExcelProperty(value = "试题类型")
    private Integer questionGenre;

    /**
     * 题干
     */
    @ExcelProperty(value = "题干")
    private String questionContent;

    /**
     * 选项内容
     */
    @ExcelProperty(value = "选项内容")
    private String optionContent;

    /**
     * 正确答案
     */
    @ExcelProperty(value = "正确答案")
    private String rightKey;

    /**
     * 解析
     */
    @ExcelProperty(value = "解析")
    private String analysis;

    /**
     * 试题类别
     */
    @ExcelProperty(value = "试题类别")
    private String questionType;

    /**
     * 图片路径
     */
    @ExcelProperty(value = "图片路径")
    private String picPath;

    /**
     * 试题序号
     */
    @ExcelProperty(value = "试题序号")
    private Long sn;

    /**
     * 是否公开 1 公有 0 私有
     */
    @ExcelProperty(value = "是否公开 1 公有 0 私有")
    private Long isOpen;

    /**
     * 是否有关键词 1 是 0 否
     */
    @ExcelProperty(value = "是否有关键词 1 是 0 否")
    private Long isKeyWord;

    /**
     * 背景资料
     */
    @ExcelProperty(value = "背景资料")
    private String background;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;

    /**
     * 关键词
     */
    private List<StQuestionKeyWordVo> keyWordList;

    /**
     * 随机选项
     */
    private String randomOptionMap = "";

    /**
     * 数据权限（0，只读；1，可编辑）
     */
    private String dataPower;

    /**
     * 试题类别名称
     */
    private String questionTypeName;

    /**
     * 所属类别父ID
     */
    private String typeFatherId;

    /**
     * 所属类别父名称
     */
    private String typeFatherName;

    /**
     * questionId
     */
    private String questionId;

    /**
     * 试题类型名称
     */
    private String questionGenreName;


    /**
     * 课程名称
     */
    private String courseName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人ID
     */
    private String createBy;


    /**
     * 创建人姓名
     */
    private String userName;

    private String tabTimeDis;

    private String fullPicPath;

    public String getFullPicPath() {
        if (GyUtils.isNull(picPath)) {
            return "";
        }
        return SpringContextUtil.getProperty("train.image.server") + picPath;
    }

    public String getQuestionGenreName() {
        if (questionGenre != null) {
            return CommonDataBaseConst.QUESTION_GENRE_NAME.getMap().get(questionGenre);
        }
        return "";
    }

}
