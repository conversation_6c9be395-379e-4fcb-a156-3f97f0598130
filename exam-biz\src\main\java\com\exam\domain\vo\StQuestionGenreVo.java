package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import java.util.Date;
import lombok.Data;


/**
 * 试题类型视图对象 st_question_genre
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StQuestionGenreVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 试题类型名称
     */
    @ExcelProperty(value = "试题类型名称")
    private String questionGenreName;

    /**
     * 分值
     */
    @ExcelProperty(value = "分值")
    private Double questionScore;

    /**
     * 类型排序
     */
    @ExcelProperty(value = "类型排序")
    private Long questionGenreSn;

    /**
     * 是否删除(1.是；0.否)
     */
    @ExcelProperty(value = "是否删除(1.是；0.否)")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long createBy;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long updateBy;


    /**
     * 试题数
     */
    private Integer questionCountPaper;
}
