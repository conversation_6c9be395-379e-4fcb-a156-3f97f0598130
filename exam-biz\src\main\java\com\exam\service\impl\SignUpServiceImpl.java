package com.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.helper.LoginHelper;
import com.exam.common.utils.DateUtils;
import com.exam.common.utils.StreamUtils;
import com.exam.common.utils.redis.RedisUtils;
import com.exam.domain.*;
import com.exam.domain.vo.ExamRoomVo;
import com.exam.domain.vo.SignUpDetailVo;
import com.exam.domain.vo.SignUpVo;
import com.exam.exception.BizException;
import com.exam.mapper.SignUpMapper;
import com.exam.service.*;
import com.exam.system.service.ISysUserEntityService;
import com.exam.utils.CopyUtils;
import com.exam.utils.SequenceBean;
import com.exam.utils.SmsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SignUpServiceImpl implements SignUpService {
    private static Logger LOGGER = LoggerFactory.getLogger(SignUpServiceImpl.class);

    private final static String redisKey = "EXAM_SIGN_UP";

    private final SignUpMapper signUpMapper;

    private final IStExamRoomService iStExamRoomService;

    private final RailwayUserInfoService railwayUserInfoService;

    private final IStUserExamService iStUserExamService;

    private final IStExamSignUpService iStExamSignUpService;
    private final ISysUserEntityService iSysUserEntityService;
    private final IStExamService iStExamService;


    @Override
    public IPage<SignUpVo> getSignUpList(Page page, String examName, Integer status) {
        return signUpMapper.getSignUpList(page, LoginHelper.getTenantId(), examName, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void signUp(SignUpDTO signUpDTO) {
        Long tenantId = LoginHelper.getTenantId();
        List<StUserExam> stUserExamList = new ArrayList<>();
        List<Long> userIdList = signUpDTO.getUserIdList();
        Long examId = signUpDTO.getExamId();
        if (userIdList != null && userIdList.size() > 0) {
            signUpMapper.deleteByExamId(examId, tenantId);
            userIdList.forEach(userId -> {
                StUserExam stUserExam = StUserExam.builder().id(SequenceBean.getSequence())
                    .examId(examId).userId(userId).joinFlag(0).effectFlag(2).tenantId(tenantId).build();
                stUserExamList.add(stUserExam);
            });
            iStUserExamService.saveBatch(stUserExamList);


        }
        List<StExamSignUp> list = iStExamSignUpService.list(new LambdaQueryWrapper<StExamSignUp>()
            .eq(StExamSignUp::getExamId, examId).eq(StExamSignUp::getTenantId, tenantId));
        if (list != null || list.size() > 0) {
            return;
        }
        StExamSignUp stExamSignUp = new StExamSignUp();
        stExamSignUp.setId(SequenceBean.getSequence());
        stExamSignUp.setExamId(examId);
        stExamSignUp.setCompleteFlag(0);
        stExamSignUp.setTenantId(tenantId);
        iStExamSignUpService.save(stExamSignUp);
        //添加缓存
        String examIdStr = String.valueOf(examId);
        Object object = RedisUtils.getCacheMapValue(redisKey, examIdStr);
        if (object == null) {
            StExam stExam = iStExamService.getById(examId);
            Date applyEndTime = stExam.getApplyEndTime();
            String dateStr = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", applyEndTime);
            RedisUtils.setCacheMapValue(redisKey, examIdStr, dateStr);
        }
    }

    @Override
    public IPage<SignUpDetailVo> getSignUpDetailList(Page page, Long examId, String examRoomName, String nameOrPhone, String idNumber, Integer joinFlag) {
        return signUpMapper.getSignUpDetailList(page, LoginHelper.getTenantId(), examId, examRoomName, nameOrPhone, idNumber, joinFlag);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishSignUp(SignUpDTO signUpDTO) {
        Long tenantId = LoginHelper.getTenantId();
        List<Long> userIdList = signUpDTO.getUserIdList();
        Long examId = signUpDTO.getExamId();
        if (userIdList != null && userIdList.size() > 0) {
            List<StUserExam> stUserExamList = new ArrayList<>();
            signUpMapper.deleteByExamId(examId, tenantId);
            userIdList.forEach(userId -> {
                StUserExam stUserExam = StUserExam.builder().id(SequenceBean.getSequence())
                    .examId(examId).userId(userId).joinFlag(0).effectFlag(2).tenantId(tenantId).build();
                stUserExamList.add(stUserExam);
            });
            iStUserExamService.saveBatch(stUserExamList);
        }
        LambdaUpdateWrapper<StExamSignUp> stExamSignUpLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        stExamSignUpLambdaUpdateWrapper.eq(StExamSignUp::getExamId, examId);
        stExamSignUpLambdaUpdateWrapper.eq(StExamSignUp::getTenantId, tenantId);
        stExamSignUpLambdaUpdateWrapper.set(StExamSignUp::getCompleteFlag, 1);
        stExamSignUpLambdaUpdateWrapper.set(StExamSignUp::getCompleteTime, LocalDateTime.now());
        stExamSignUpLambdaUpdateWrapper.set(StExamSignUp::getUpdateBy, LoginHelper.getUserId());
        stExamSignUpLambdaUpdateWrapper.set(StExamSignUp::getUpdateTime, LocalDateTime.now());
        iStExamSignUpService.update(stExamSignUpLambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoFinishSignUp() {
        Map<String, String> map = RedisUtils.getCacheMap(redisKey);
        if (map == null) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        List<Long> examIdList = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String examIdStr = entry.getKey();
            String dateTime = entry.getValue();
            LocalDateTime parse = LocalDateTime.parse(dateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            if (now.isAfter(parse)) {
                examIdList.add(Long.valueOf(examIdStr));
                RedisUtils.delCacheMapValue(redisKey, examIdStr);
            }
        }
        if (examIdList.size() > 0) {
            LambdaUpdateWrapper<StExamSignUp> stExamSignUpLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            stExamSignUpLambdaUpdateWrapper.in(StExamSignUp::getExamId, examIdList);
            stExamSignUpLambdaUpdateWrapper.set(StExamSignUp::getCompleteFlag, 1);
            stExamSignUpLambdaUpdateWrapper.set(StExamSignUp::getCompleteTime, LocalDateTime.now());
            stExamSignUpLambdaUpdateWrapper.set(StExamSignUp::getUpdateBy, LoginHelper.getUserId());
            stExamSignUpLambdaUpdateWrapper.set(StExamSignUp::getUpdateTime, LocalDateTime.now());
            iStExamSignUpService.update(stExamSignUpLambdaUpdateWrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void distributedExamRoom(SignUpDTO signUpDTO) {
        List<Long> examRoomIdList = signUpDTO.getExamRoomIdList();
        Long examId = signUpDTO.getExamId();
        List<StUserExam> list = iStUserExamService.list(new LambdaQueryWrapper<StUserExam>()
            .eq(StUserExam::getExamId, examId).eq(StUserExam::getTenantId, LoginHelper.getTenantId()));
        List<Long> userIdList = list.stream().map(StUserExam::getUserId).collect(Collectors.toList());
        signUpMapper.deleteByExamId(examId, LoginHelper.getTenantId());
        List<StExamRoom> stExamRooms = iStExamRoomService.listByIds(examRoomIdList);
        int sum = stExamRooms.stream().mapToInt(StExamRoom::getCapacity).sum();
        if (sum < userIdList.size()) {
            throw new BizException("考场容纳人数不够，还需选择考场！");
        }
        handleDistributedExamRoom(examId, examRoomIdList, userIdList);


        Long tenantId = LoginHelper.getTenantId();

        StExam stExam = iStExamService.getById(examId);

        if (stExam != null && stExam.getStartTime() != null) {

            LocalDateTime examStartTime = stExam.getStartTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

            LocalDateTime cacheDeadline = examStartTime.plusHours(1);

            iSysUserEntityService.cacheExamUser(tenantId, userIdList, cacheDeadline);
        }


        //发送短信
        String ids = userIdList.stream().map(userId -> userId.toString()).collect(Collectors.joining(","));
        List<SignUpSmsInfoVo> signUpSmsInfoList = signUpMapper.getSignUpSmsInfoList(ids);
        for (SignUpSmsInfoVo signUpSmsInfoVo : signUpSmsInfoList) {
            Map<String, String> map = new HashMap<>();
            map.put("namea", signUpSmsInfoVo.getExamName());
            String startTimeFormat = signUpSmsInfoVo.getStartTime().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm"));
            String endTimeFormat = signUpSmsInfoVo.getEndTime().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm"));
            map.put("time", startTimeFormat + "~" + endTimeFormat);
            map.put("nameb", signUpSmsInfoVo.getExamRoomName());
            map.put("number", signUpSmsInfoVo.getExamNum());
            SmsUtil.sendSignUpSMS(signUpSmsInfoVo.getPhonenumber(), map);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleDistributedExamRoom(Long examId, List<Long> examRoomIdList, List<Long> userIdList) {
        Long tenantId = LoginHelper.getTenantId();
        List<StUserExam> stUserExamList = new ArrayList<>();
        List<StExamRoom> stExamRooms = iStExamRoomService.listByIds(examRoomIdList);
        List<ExamRoomVo> examRoomVoList = CopyUtils.copyPropertiesList(stExamRooms, ExamRoomVo.class);
        List<RailwayUserInfo> userList = railwayUserInfoService.list(new LambdaQueryWrapper<RailwayUserInfo>().in(RailwayUserInfo::getUserId, userIdList));
        //分配
        List<RailwayUserInfo> countyDistributed = distributed(examId, tenantId, "countyId", userList, examRoomVoList, ExamRoomVo::getCountyId, stUserExamList);
        List<RailwayUserInfo> cityDistributed = distributed(examId, tenantId, "cityId", countyDistributed, examRoomVoList, ExamRoomVo::getCityId, stUserExamList);
        List<RailwayUserInfo> provinceDistributed = distributed(examId, tenantId, "provinceId", cityDistributed, examRoomVoList, ExamRoomVo::getProvinceId, stUserExamList);
        if (provinceDistributed != null && provinceDistributed.size() > 0) {
            //随机分配
            List<ExamRoomVo> underExamRoom = StreamUtils.filter(examRoomVoList, examRoomVo -> examRoomVo.getCurrentOccupantNum().get() < examRoomVo.getCapacity());
            Collections.shuffle(underExamRoom);
            int i = 0;
            for (ExamRoomVo examRoomVo : underExamRoom) {
                for (int j = i; j < provinceDistributed.size(); j++) {
                    RailwayUserInfo railwayUserInfo = provinceDistributed.get(j);
                    if (examRoomVo.addOccupant()) {
                        ExamRoomVo selectExamRoom = examRoomVo;
                        stUserExamList.add(buildStUserExam(railwayUserInfo.getUserId(), examId, selectExamRoom, tenantId));
                    } else {
                        i = j;
                        break;
                    }
                }
            }
        }
        iStUserExamService.saveBatch(stUserExamList);
    }


    private List<RailwayUserInfo> distributed(Long examId, Long tenantId, String fieldName, List<RailwayUserInfo> userList, List<ExamRoomVo> examRoomVoList, Function<ExamRoomVo, Long> key, List<StUserExam> stUserExamList) {
        List<RailwayUserInfo> undistributedUserList = new ArrayList<>();
        if (userList == null || userList.size() == 0) {
            return undistributedUserList;
        }
        Map<Long, List<ExamRoomVo>> districtListMap = StreamUtils.groupByKey(examRoomVoList, key);
        userList.parallelStream().forEach(railwayUserInfo -> {
            List<ExamRoomVo> examRoomVos = null;
            try {
                Field declaredField = railwayUserInfo.getClass().getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                examRoomVos = districtListMap.get(declaredField.get(railwayUserInfo));
            } catch (Exception e) {
                LOGGER.error("分配考场失败:" + e.getMessage());
                throw new BizException("分配考场失败");
            }
            if (examRoomVos == null) {
                undistributedUserList.add(railwayUserInfo);
            }
            ExamRoomVo selectExamRoom = null;
            for (ExamRoomVo examRoomVo : examRoomVos) {
                if (examRoomVo.addOccupant()) {
                    selectExamRoom = examRoomVo;
                    break;
                }
            }
            if (selectExamRoom == null) {
                undistributedUserList.add(railwayUserInfo);
            } else {
                stUserExamList.add(buildStUserExam(railwayUserInfo.getUserId(), examId, selectExamRoom, tenantId));
            }
        });
        return undistributedUserList;
    }

    private StUserExam buildStUserExam(Long userId, Long examId, ExamRoomVo selectExamRoom, Long tenantId) {
        String nowStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        int count = selectExamRoom.getCurrentOccupantNum().get();
        String examNum = String.format("%03d", count + 1);
        StUserExam stUserExam = StUserExam.builder().id(SequenceBean.getSequence())
            .examId(examId).userId(userId)
            .examRoomId(selectExamRoom.getId()).examNum(examNum)
            .admissionTicketNumber(nowStr + selectExamRoom.getCode() + examNum)
            .effectFlag(1).tenantId(tenantId)
            .build();
        return stUserExam;
    }

}
