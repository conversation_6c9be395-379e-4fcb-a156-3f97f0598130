package com.exam.constant;

public class TableFieldNameConsts {

    public static final String ID = "id";
    public static final String project_id = "project_id";
    public static final String USER_ID = "user_id";
    public static final String TENANT_ID = "tenant_id";
    public static final String STATUS = "status";
    public static final String USER_INFO_ID = "user_info_id";
    public static final String del_flag = "del_flag";
    public static final String create_time = "create_time";
    public static final String TAB_UID = "tab_uid";
    public static final String UPDATE_TIME = "update_time";
    public static final String UPDATE_ID = "update_id";
    public static final String PROJECT_ID = "project_id";
    public static final String TYPE = "type";

    public static class StJobAttrTable {
        public static final String JOB_ATTR_NAME = "job_attr_name";
    }

    public static class StUserJobAttrTable {
        public static final String JOB_ATTR_ID = "job_attr_id";
        public static final String USER_ID = "user_id";
    }

    public static class StCustomFieldTable {
        public static final String FIELD_TYPE = "field_type";
        public static final String USER_ID = "user_id";
    }

    public static class StquestionTable {
        public static final String QUESTION_GENRE = "question_genre";
        public static final String QUESTION_TYPE = "question_type";
        public static final String QUESTION_CONTENT = "question_content";
        public static final String OPTION_CONTENT = "option_content";
    }

    public static class StAnswerCardDetailTable {
        public static final String ANSWER_CARD_ID = "answer_card_id";
    }

    public static class StAnswerCardTable {
        public static final String EXAM_ID = "exam_id";
        public static final String EXAM_TIME = "exam_time";
        public static final String IS_LATEST = "is_latest";
    }

    public static class StMockAnswerCardTable {
        public static final String EXAM_ID = "exam_id";
        public static final String EXAM_TIME = "exam_time";
        public static final String IS_LATEST = "is_latest";
    }

    public static class StMockAnswerCardDetailTable {
        public static final String ANSWER_CARD_ID = "answer_card_id";
        public static final String QUESTION_ID = "question_id";
        public static final String ANSWER = "answer";
        public static final String SINGLE_SCORE = "single_score";
        public static final String REVIEW_RESULT = "review_result";
    }

    public static class StExamQuestionGenreTable {
        public static final String EXAM_ID = "exam_id";
        public static final String QUESTION_GENRE_ID = "question_genre_id";
        public static final String QUESTION_TYPE_ID = "question_type_id";
    }

    public static class StCompulsionComplaintTable {
        public static final String EXAM_ID = "exam_id";
        public static final String ANSWER_ID = "answer_id";
        public static final String DISPUTE_COMPLAINT_ID = "dispute_complaint_id";
        public static final String DISPUTE_TIME = "dispute_time";
        public static final String DISPUTE_STATUS = "dispute_status";
    }

    public static class StDisputeComplaintTable {
        public static final String EXAM_ID = "exam_id";
        public static final String ANSWER_ID = "answer_id";
        public static final String DISPUTE_COMPLAINT_ID = "dispute_complaint_id";
        public static final String DISPUTE_TIME = "dispute_time";
    }

    public static class StDisputeComplaintDetailTable {
        public static final String EXAM_ID = "exam_id";
        public static final String ANSWER_ID = "answer_id";
        public static final String QUESTION_ID = "question_id";
        public static final String DISPUTE_COMPLAINT_ID = "dispute_complaint_id";
        public static final String DISPUTE_TIME = "dispute_time";
    }

    public static class StMessageTable {
        public static final String MSG_TYPE = "msg_type";
        public static final String MSG_STATUS = "msg_status";
        public static final String TERMINAL = "terminal";
    }

    public static class StMessageUserTable {
        public static final String PUSH_TIME = "push_time";
        public static final String READ_TIME = "read_time";
        public static final String STATUS = "status";
    }

    public static class StMockuestion {
        public static final String QUESTION_TYPE_ID = "question_type_id";
        public static final String QUESTION_ID = "question_id";
    }

}
