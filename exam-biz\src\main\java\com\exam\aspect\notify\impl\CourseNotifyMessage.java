package com.exam.aspect.notify.impl;



import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.exam.aspect.notify.NotifyMessageObject;
import com.exam.constant.CommonConsts;
import com.exam.constant.CommonDataBaseConst;
import com.exam.constant.TableFieldNameConsts;
import com.exam.domain.StGroup;
import com.exam.domain.StMessageUser;
import com.exam.domain.StMessageUserShield;
import com.exam.domain.StUserGroup;
import com.exam.domain.vo.AssignCoursesVo;
import com.exam.domain.vo.StMessageVo;
import com.exam.service.IStGroupService;
import com.exam.service.IStMessageService;
import com.exam.service.IStMessageUserService;
import com.exam.service.IStMessageUserShieldService;
import com.exam.service.IStUserGroupService;
import java.time.LocalDateTime;
import java.util.List;

import com.exam.utils.SequenceBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CourseNotifyMessage extends NotifyMessageObject {

    @Autowired
    private IStMessageService stMessageService;
    @Autowired
    private IStMessageUserService stMessageUserService;
    @Autowired
    private IStUserGroupService stUserGroupService;
    @Autowired
    private IStGroupService stGroupService;
    @Autowired
    private IStMessageUserShieldService iStMessageUserShieldService;

    @Override
    public void notifyMessage(int msgType, int msgDetailType, Object... args){


        AssignCoursesVo assignCoursesVO = null;
        if(args.length != 0 && args[0] instanceof AssignCoursesVo) {
            assignCoursesVO = (AssignCoursesVo) args[0];
        }

        for(Long courseId : assignCoursesVO.getCourseIds()) {
            for(Long groupId : assignCoursesVO.getUserGroupIds()) {
                List<StUserGroup> stUserGroupList =  stUserGroupService.getUserGroup(String.valueOf(groupId));
                StGroup sgroup = stGroupService.getById(groupId);
                StMessageVo vo = stMessageService.getByMsgAndDetailType(msgType, msgDetailType, sgroup.getTenantId());
                for(StUserGroup stUserGroup : stUserGroupList) {
                    QueryWrapper<StMessageUserShield> queryWrapper = new QueryWrapper<StMessageUserShield>();
                    queryWrapper.eq(TableFieldNameConsts.USER_ID, stUserGroup.getUserId());
                    queryWrapper.eq(TableFieldNameConsts.TENANT_ID, sgroup.getTenantId());
                    StMessageUserShield stMessageUserShield = iStMessageUserShieldService.getBaseMapper().selectOne(queryWrapper);

                    if(stMessageUserShield == null || CommonConsts.NO.equals(stMessageUserShield.getIsShield())) {
                        StMessageUser stMessageUser = new StMessageUser();
//                        EntityUtils.setInsertInfo(stMessageUser);
                        stMessageUser.setDelFlag(CommonDataBaseConst.YES_OR_NO.NO.getCode());
                        stMessageUser.setId(SequenceBean.getSequence());
                        stMessageUser.setMainTableId(Long.valueOf(courseId));
                        stMessageUser.setPushTime(LocalDateTime.now());
                        stMessageUser.setStatus(Integer.valueOf(CommonDataBaseConst.MSG_READ_STATUS.UNREAD.getCode()));
                        stMessageUser.setUserInfoId(stUserGroup.getUserId());
                        stMessageUser.setMessageId(vo.getId());
                        stMessageUser.setMsgDetailType(msgDetailType);
                        stMessageUser.setTenantId(sgroup.getTenantId());
                        stMessageUserService.save(stMessageUser);
                    }
                }
            }
        }
    }

}
