package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 制度办法视图对象 st_institution
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@Data
@ExcelIgnoreUnannotated
public class StInstitutionVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 附件
     */
    @ExcelProperty(value = "附件")
    private String annex;

    /**
     * 是否置顶
     */
    @ExcelProperty(value = "是否置顶")
    private Long isTop;

    /**
     * 租户ID
     */
    @ExcelProperty(value = "租户ID")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;


}
