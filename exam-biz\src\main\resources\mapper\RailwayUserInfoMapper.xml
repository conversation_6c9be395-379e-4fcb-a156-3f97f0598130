<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.exam.mapper.RailwayUserInfoMapper">
  <resultMap id="BaseResultMap" type="com.exam.domain.RailwayUserInfo">
    <!--@mbg.generated-->
    <!--@Table railway_user_info-->
    <id column="rui_id" jdbcType="BIGINT" property="ruiId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="rui_name" jdbcType="VARCHAR" property="ruiName" />
    <result column="rui_id_card" jdbcType="VARCHAR" property="ruiIdCard" />
    <result column="rui_nation" jdbcType="VARCHAR" property="ruiNation" />
    <result column="rui_sex" jdbcType="BOOLEAN" property="ruiSex" />
    <result column="rui_start_date" jdbcType="VARCHAR" property="ruiStartDate" />
    <result column="rui_end_date" jdbcType="VARCHAR" property="ruiEndDate" />
    <result column="rui_is_perpetual" jdbcType="BOOLEAN" property="ruiIsPerpetual" />
    <result column="rui_birthday" jdbcType="VARCHAR" property="ruiBirthday" />
    <result column="rui_age" jdbcType="VARCHAR" property="ruiAge" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    rui_id, user_id, rui_name, rui_id_card, rui_nation, rui_sex, rui_start_date, rui_end_date, 
    rui_is_perpetual, rui_birthday, rui_age, create_by, create_time, update_by, update_time
  </sql>
    <select id="queryUserPage" resultType="com.exam.domain.vo.UserInfoVO">
        select
        rui.rui_id, rui.rui_name, u.phonenumber, rp.rp_name, rui.rui_certificate_unit, u.status, rui.rui_approve_status,
        su.nick_name approveUserName, rfa.rfa_time, rui.create_time, rui.update_time, su1.nick_name createUserName,
        su2.nick_name updateUserName,GROUP_CONCAT(DISTINCT sr.role_name SEPARATOR ',') as roleName
        from sys_user u
        left join railway_user_info rui on rui.user_id = u.user_id
        left join railway_project rp on rp.rp_id = rui.project_id
        left join railway_firm_approve rfa on rfa.rf_id = u.user_id
        left join sys_user su on su.user_id = rfa.rfa_approver
        left join sys_user su1 on su1.user_id = rui.create_by
        left join sys_user su2 on su2.user_id = rui.update_by
        left join sys_user_role sur on sur.user_id = u.user_id and sur.tenant_id = rui.tenant_id
        left join sys_role sr on sr.role_id = sur.role_id and sr.tenant_id = rui.tenant_id
        <where>
            <if test="dto.tenantId != null">
                and rui.tenant_id = #{dto.tenantId}
            </if>
            <if test="dto.projectId != null">
                and rui.project_id = #{dto.projectId}
            </if>
            <if test="dto.name != null and dto.name != ''">
                and (rui.rui_name like CONCAT('%', #{dto.name,jdbcType=VARCHAR},'%') or
                u.phonenumber like CONCAT('%', #{dto.name,jdbcType=VARCHAR},'%'))
            </if>
            <if test="dto.idCard != null and dto.idCard != ''">
                and rui.rui_id_card like CONCAT('%', #{dto.idCard,jdbcType=VARCHAR},'%')
            </if>
            <if test="dto.approveStatus != null">
                and rui.rui_approve_status = #{dto.approveStatus}
            </if>
            <if test="dto.status != null">
                and u.status = #{dto.status}
            </if>
            <if test="dto.firmType != null">
                and u.firm_type = #{dto.firmType}
            </if>
        </where>
        GROUP BY rui.rui_id
    </select>
</mapper>
