package com.exam.controller;

import java.util.List;
import java.util.Arrays;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import com.exam.domain.Tenant;
import com.exam.common.constant.UserConstants;
import com.exam.common.utils.StringUtils;
import com.exam.domain.bo.TenantBo;
import com.exam.domain.vo.TenantVo;
import com.exam.service.ITenantService;
import com.exam.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.common.core.page.TableDataInfo;

/**
 * 租户
 *
 * <AUTHOR>
 * @date 2023-10-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/tenant")
public class TenantController extends BaseController {

    private final ITenantService iTenantService;

    private final ISysUserService iSysUserService;

    /**
     * 查询租户列表
     */
    @SaCheckPermission("exam:tenant:list")
    @GetMapping("/list")
    public TableDataInfo<TenantVo> list(TenantBo bo, PageQuery pageQuery) {
        return iTenantService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出租户列表
     */
    @SaCheckPermission("exam:tenant:export")
    @Log(title = "租户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TenantBo bo, HttpServletResponse response) {
        List<TenantVo> list = iTenantService.queryList(bo);
        ExcelUtil.exportExcel(list, "租户", TenantVo.class, response);
    }

    /**
     * 获取租户详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:tenant:query")
    @GetMapping("/{id}")
    public R<TenantVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iTenantService.queryById(id));
    }

    /**
     * 新增租户
     */
    @SaCheckPermission("exam:tenant:add")
    @Log(title = "租户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody Tenant bo) {
        if (UserConstants.NOT_UNIQUE.equals(iTenantService.checkNameExist(bo))) {
            return R.fail("新增租户'" + bo.getName() + "'失败，租户名称已存在");
        }
        if (UserConstants.NOT_UNIQUE.equals(iTenantService.checkUserNameExist(bo))) {
            return R.fail("新增租户'" + bo.getName() + "'失败，用户名已经存在");
        }
        if(UserConstants.NOT_UNIQUE.equals(iSysUserService.checkUserNameUnique(bo.getUserName()))){
            return R.fail("新增租户'" + bo.getName() + "'失败，用户名已经存在");
        }
        return toAjax(iTenantService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 初始化租户菜单
     */
    @SaCheckPermission("exam:tenant:reinitialize")
    @Log(title = "租户", businessType = BusinessType.Fill)
    @PostMapping("/reinitialize")
    public R<Void> reinitializeMenu(@NotNull(message = "主键不能为空") @RequestBody Map<String,String> param){
        String id = param.get("id");
        if(StringUtils.isEmpty(id)){
            return R.fail("初始化菜单失败，id不能为空");
        }
        TenantVo bo = iTenantService.queryById(Long.parseLong(id));
        if(ObjectUtil.isNull(bo)){
            return R.fail("初始化菜单失败，没有找到对应租户信息，请联系系统管理员");
        }
        iTenantService.reinitializeMenu(bo);
        return R.ok();
    }

    /**
     * 修改租户
     */
    @SaCheckPermission("exam:tenant:edit")
    @Log(title = "租户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TenantBo bo) {
        return toAjax(iTenantService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除租户
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:tenant:remove")
    @Log(title = "租户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTenantService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
