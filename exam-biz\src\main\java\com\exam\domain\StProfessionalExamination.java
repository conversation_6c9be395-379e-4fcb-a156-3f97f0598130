package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 职业考试对象 st_professional_examination
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_professional_examination")
public class StProfessionalExamination extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 考试名称
     */
    private String examName;
    /**
     * 考试时间
     */
    private Date examTime;
    /**
     * 入口链接
     */
    private String link;
    /**
     * 领域
     */
    private String domainCode;
    /**
     * 权限
     */
    private String companyCode;
    /**
     * 是否删除(1.是；0.否)
     */
    private Integer delFlag;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人id
     */
    private Long createBy;
    /**
     * 修改人id
     */
    private Long updateBy;

}
