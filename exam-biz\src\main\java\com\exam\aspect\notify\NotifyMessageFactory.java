package com.exam.aspect.notify;


import com.exam.aspect.notify.impl.CompulsionNotifyMessage;
import com.exam.aspect.notify.impl.CourseNotifyMessage;
import com.exam.aspect.notify.impl.DisputeNotifyMessage;
import com.exam.aspect.notify.impl.ExamNotifyMessage;
import com.exam.constant.CommonDataBaseConst.MSG_TYPE;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
public class NotifyMessageFactory {

    @Autowired
    private ApplicationContext context;

    public NotifyMessageObject getOperObject(String objectType){
        if(MSG_TYPE.COURSE.getCode().equals(objectType)){
            return context.getBean(CourseNotifyMessage.class);
        } else if(MSG_TYPE.EXAM.getCode().equals(objectType)){
            return context.getBean(ExamNotifyMessage.class);
        } else if(MSG_TYPE.COMPULSION.getCode().equals(objectType)){
            return context.getBean(CompulsionNotifyMessage.class);
        } else if(MSG_TYPE.DISPUTE.getCode().equals(objectType)){
            return context.getBean(DisputeNotifyMessage.class);
        } else {
            throw new NullPointerException("通知对象类型["+objectType+"]不存在");
        }
    }

}
