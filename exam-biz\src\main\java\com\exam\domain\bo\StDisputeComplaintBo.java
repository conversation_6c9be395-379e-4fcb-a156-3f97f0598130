package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 纠纷申诉业务对象 st_dispute_complaint
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StDisputeComplaintBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 考试id
     */
    @NotNull(message = "考试id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long examId;

    /**
     * 答题卡id
     */
    @NotNull(message = "答题卡id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long answerId;

    /**
     * 申诉状态(-1:未提交,0:待审核，1：已处理)
     */
    @NotNull(message = "申诉状态(-1:未提交,0:待审核，1：已处理)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer disputeStatus;

    /**
     * 申诉时间
     */
    @NotNull(message = "申诉时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date disputeTime;

    /**
     * 完成时间
     */
    @NotNull(message = "完成时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date completeTime;

    /**
     * 考生id
     */
    @NotNull(message = "考生id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date createTime;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createBy;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateBy;

    /**
     * 领域
     */
    @NotBlank(message = "领域不能为空", groups = { AddGroup.class, EditGroup.class })
    private String domainCode;

    /**
     * 所属单位
     */
    @NotBlank(message = "所属单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;


}
