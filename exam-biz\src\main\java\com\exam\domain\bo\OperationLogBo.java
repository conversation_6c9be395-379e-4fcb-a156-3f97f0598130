package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 操作日志业务对象 operation_log
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class OperationLogBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 操作类型
     */
    @NotBlank(message = "操作类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String operationType;

    /**
     * 操作模块
     */
    @NotBlank(message = "操作模块不能为空", groups = { AddGroup.class, EditGroup.class })
    private String operationModule;

    /**
     * 操作内容
     */
    @NotBlank(message = "操作内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String operationContent;

    /**
     * 操作时间
     */
    @NotNull(message = "操作时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date operationTime;

    /**
     * 操作人id
     */
    @NotNull(message = "操作人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long operationUserId;

    /**
     * 操作人名称
     */
    @NotBlank(message = "操作人名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String operationUserName;

    /**
     * 领域
     */
    @NotBlank(message = "领域不能为空", groups = { AddGroup.class, EditGroup.class })
    private String domainCode;

    /**
     * 导入文件路径
     */
    @NotBlank(message = "导入文件路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String importFilePath;


}
