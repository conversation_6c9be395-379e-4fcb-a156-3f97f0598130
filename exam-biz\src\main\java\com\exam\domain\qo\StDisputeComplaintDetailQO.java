package com.exam.domain.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 纠纷申诉详情QO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="StDisputeComplaintDetailQO对象", description="纠纷申诉详情QO")
public class StDisputeComplaintDetailQO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "申诉Id")
    private Long disputeComplaintId;

    @ApiModelProperty(value = "考试id")
    private Long examId;

    @ApiModelProperty(value = "答题卡id")
    private Long answerId;

    @ApiModelProperty(value = "用户Id")
    private Long userId;

    @ApiModelProperty(value = "领域")
    private Long tenantId;

}
