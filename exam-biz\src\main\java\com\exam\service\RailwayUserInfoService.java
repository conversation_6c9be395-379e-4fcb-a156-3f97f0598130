package com.exam.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.exam.domain.RailwayUserInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.exam.domain.RailwayUserPageDto;
import com.exam.domain.StUserExam;
import com.exam.domain.StUserExamVo;
import com.exam.domain.bo.RegionBo;
import com.exam.domain.vo.RailwayUserInfoVo;
import com.exam.domain.vo.UserCourseCategoryVO;
import com.exam.domain.vo.UserInfoVO;

import java.util.List;

public interface RailwayUserInfoService extends IService<RailwayUserInfo>{


    RailwayUserInfoVo selectInfo(Long userId);

    List<Object> getRegion(RegionBo bo);

    RailwayUserInfoVo getExamList(RailwayUserInfo railwayUserInfo);

    StUserExam getExam(Long examId);

    IPage<UserInfoVO> queryUserPage(RailwayUserPageDto railwayUserPageDto);
}
