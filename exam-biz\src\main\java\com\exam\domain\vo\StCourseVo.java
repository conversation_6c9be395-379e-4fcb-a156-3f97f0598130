package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.utils.GyUtils;
import com.exam.utils.ReadFileUtil;
import com.exam.utils.SpringContextUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * 课程视图对象 st_course
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@Data
//@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
@ExcelIgnoreUnannotated
public class StCourseVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 所属类别id
     */
    @ExcelProperty(value = "所属类别id")
    private Long categoryId;

    /**
     * 所属类别父id
     */
    @ExcelProperty(value = "所属类别父id")
    private String categoryFatherId;

    /**
     * 所属类别名称
     */
    @ExcelProperty(value = "所属类别名称")
    private String categoryName;

    /**
     * 所属类别父名称
     */
    @ExcelProperty(value = "所属类别父名称")
    private String categoryFatherName;

    /**
     * 所属组id
     */
    @ExcelProperty(value = "所属组id")
    private String groupId;

    /**
     * 所属组名称
     */
    @ExcelProperty(value = "所属组名称")
    private String groupName;


    /**
     * 课程名称
     */
    @ExcelProperty(value = "课程名称")
    private String courseName;

    /**
     * 课时数
     */
    @ExcelProperty(value = "课时数")
    private Long classhourCount;

    /**
     * 课程时长
     */
    @ExcelProperty(value = "课程时长")
    private Long courseDuration;

    /**
     * 主讲老师
     */
    @ExcelProperty(value = "主讲老师")
    private String teacher;

    /**
     * 封面图片路径
     */
    @ExcelProperty(value = "封面图片路径")
    private String picPath;

    /**
     * 播放时长
     */
    @ExcelProperty(value = "播放时长")
    private long playDuration;


    /**
     * 是否为精品课程(1.是；0.否)
     */
    @ExcelProperty(value = "是否为精品课程(1.是；0.否)")
    private Long isBoutique;

    /**
     * 课时集合
     */
    @ExcelProperty(value = "课时集合")
    private List<StClasshourVo> StClasshourVoList;

    /**
     * 是否必修
     */
    @ExcelProperty(value = "是否必修")
    private String isRequiredCourse;


    /**
     * 是否安管人员(1.是；0.否)
     */
    @ExcelProperty(value = "是否安管人员(1.是；0.否)")
    private Long isSafety;

    /**
     * 安管是否分类(1.已分类；0.未分类)
     */
    @ExcelProperty(value = "安管是否分类(1.已分类；0.未分类)")
    private Long isSafetyCategory;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long projectId;

    /**
     * 封面图片路径
     */
    @ExcelProperty(value = "封面图片路径")
    private String fullPicPath;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createName;

    @ApiModelProperty(value = "数据权限（0，只读；1，可编辑）")
    private String dataPower;
    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    public String getFullPicPath() {
        if(GyUtils.isNull(picPath)){
            return "";
        }
        return SpringContextUtil.getProperty("train.image.server")+picPath;
    }

    public String getCourseDurationDis() {
        if(courseDuration!=null){
            return ReadFileUtil.secondToTime(courseDuration);
        }
        return "";
    }

    public String getProgresDis() {
        if(courseDuration!=null && courseDuration!=0){
            int progres= (int)(playDuration*100/courseDuration);
//            if(progres>=99){
//                progres = 100;
//            }
            return progres+"%";
        }
        return "0%";
    }

}
