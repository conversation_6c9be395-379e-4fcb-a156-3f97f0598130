package com.exam.domain.vo;

import com.exam.utils.ReadFileUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户课程类别列表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserCourseProgresVO", description="用户课程培训信息")
public class UserCourseProgresVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程数")
    private int courseCount;
    @ApiModelProperty(value = "课时数")
    private int classhourCount;
    @ApiModelProperty(value = "总时长")
    private long durationTotal;
    @ApiModelProperty(value = "播放总时长")
    private long playDurationTotal;
    @ApiModelProperty(value = "总进度")
    private int progresTotal;
    @ApiModelProperty(value = "培训进度")
    private int progres;

    public String getDurationTotalDis() {
        return ReadFileUtil.secondToTime(durationTotal);
    }
    public String getProgresTotalDis() {
        if(durationTotal!=0){
            return (playDurationTotal*100/durationTotal) + "%";
        }
        return "0%";
    }
}
