package com.exam.utils;

import com.alibaba.fastjson.JSON;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.exam.common.utils.StringUtils;
import com.exam.common.utils.redis.RedisUtils;

import java.util.Map;

import static com.exam.constant.RedisKeyConstant.VERIFICATION_CODE;

/**
 * 短信工具类
 */
public class SmsUtil {
    private static String accessKeyId = "LTAI5tQxvDhm5zYGxuyjrm7F";
    private static String accessSecret = "******************************";
    //区域
    private static String regionId = "cn-hangzhou";
    //签名
    private static String signName = "十九局矿业北京信息分公司";
    //验证码短信模板
    private static String codeTemplateCode = "SMS_485280042";
    // 密码短信模板
    private static String pwdTemplateCode = "SMS_485325615";
    // 报名短信模板
    private static String signUpTemplateCode = "SMS_486400379";

    /**
     * 发送验证码短信
     */
    public static String sendCodeSMS(String phone, String code) {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessSecret);
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.aliyuncs.com");
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendSms");
        request.putQueryParameter("RegionId", regionId);
        request.putQueryParameter("PhoneNumbers", phone);
        request.putQueryParameter("SignName", signName);
        request.putQueryParameter("TemplateCode", codeTemplateCode);
        request.putQueryParameter("TemplateParam", "{\"code\":\"" + code + "\"}");
        try {
            CommonResponse response = client.getCommonResponse(request);
            return response.getData();
        } catch (ServerException e) {
            e.printStackTrace();
            return "fail";
        } catch (ClientException e) {
            e.printStackTrace();
            return "fail";
        }
    }

    /**
     * 发送密码短信
     */
    public static String sendPwdSMS(String phone, String code) {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessSecret);
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.aliyuncs.com");
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendSms");
        request.putQueryParameter("RegionId", regionId);
        request.putQueryParameter("PhoneNumbers", phone);
        request.putQueryParameter("SignName", signName);
        request.putQueryParameter("TemplateCode", pwdTemplateCode);
        request.putQueryParameter("TemplateParam", "{\"code\":\"" + code + "\"}");
        try {
            CommonResponse response = client.getCommonResponse(request);
            return response.getData();
        } catch (ServerException e) {
            e.printStackTrace();
            return "fail";
        } catch (ClientException e) {
            e.printStackTrace();
            return "fail";
        }
    }


    public  static  boolean checkVerificationCode(String phoneNumber,String verificationCode){
        String value = RedisUtils.getCacheObject(VERIFICATION_CODE + phoneNumber);
        return StringUtils.equals(verificationCode, value);
    }


    public static String sendSignUpSMS(String phone, Map<String,String> map) {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessSecret);
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.aliyuncs.com");
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendSms");
        request.putQueryParameter("RegionId", regionId);
        request.putQueryParameter("PhoneNumbers", phone);
        request.putQueryParameter("SignName", signName);
        request.putQueryParameter("TemplateCode", signUpTemplateCode);
        request.putQueryParameter("TemplateParam", JSON.toJSONString(map));
        try {
            CommonResponse response = client.getCommonResponse(request);
            return response.getData();
        } catch (ServerException e) {
            e.printStackTrace();
            return "fail";
        } catch (ClientException e) {
            e.printStackTrace();
            return "fail";
        }
    }

}
