package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import com.exam.common.helper.LoginHelper;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 考试视图对象 st_exam
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Data
@ExcelIgnoreUnannotated
public class StExamVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "")
    private Long id;
    /**
     *  考试类别
     */
    private String examType;

    /**
     * 考试名称
     */
    @ExcelProperty(value = "")
    private String examName;

    /**
     * 组ID
     */
    @ExcelProperty(value = "")
    private Long groupId;

    /**
     * 课程类别id集合
     */
    @ExcelProperty(value = "课程类别id集合")
    private String questionTypeIds;

    /**
     * 试卷类型（1：随机组卷，2：自定义组卷）
     */
    @ExcelProperty(value = "试卷类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=：随机组卷，2：自定义组卷")
    private String paperType;

    /**
     * 入场密码（现场公布不可泄露）
     */
    private String admissionPassword;

    /**
     * 考试人数
     */
    private int personCount;
    /**
     * 考试状态（未开始-1,进行中0,已结束1）
     */
    private String examStatus;
    /**
     *  报名开始时间
     */
    private Date applyStartTime;
    /**
     * 报名结束时间
     */
    private Date applyEndTime;
    /**
     * 是否全程录像(1.是；0.否)
     */
    private String isWholeVideo;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long examLength;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Double examScore;

    /**
     * 及格分数
     */
    @ExcelProperty(value = "及格分数")
    private Double passScore;

    /**
     * 允许补考次数
     */
    @ExcelProperty(value = "允许补考次数")
    private Long allowedTimes;

    /**
     * 是否需要培训(1.是；0.否)
     */
    @ExcelProperty(value = "是否需要培训(1.是；0.否)")
    private String isNeedTrain;

    /**
     * 是否设置等级(1.是；0.否)
     */
    @ExcelProperty(value = "是否设置等级(1.是；0.否)")
    private String isSetLevel;

    /**
     * 是否查看答案解析(1.是；0.否)
     */
    @ExcelProperty(value = "是否查看答案解析(1.是；0.否)")
    private String isAnswerAnalysis;

    /**
     * 是否开启纠纷申诉(1.是；0.否)
     */
    @ExcelProperty(value = "是否开启纠纷申诉(1.是；0.否)")
    private String isDisputeComplaint;

    /**
     * 是否开启强制交卷申诉（1.是；0：否）
     */
    @ExcelProperty(value = "是否开启强制交卷申诉", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=.是；0：否")
    private String isCompulsionComplaint;

    /**
     * 可申诉时间
     */
    @ExcelProperty(value = "可申诉时间")
    private String canAppealTime;

    /**
     * 申诉处理时间
     */
    @ExcelProperty(value = "申诉处理时间")
    private String appealHandleTime;

    /**
     * 申诉继续考试时间
     */
    @ExcelProperty(value = "申诉继续考试时间")
    private String appealExamTime;

    /**
     * 是否启用人脸识别(1.是；0.否)
     */
    @ExcelProperty(value = "是否启用人脸识别(1.是；0.否)")
    private String isFaceRecognition;

    /**
     * 是否视频监控(1.是；0.否)
     */
    @ExcelProperty(value = "是否视频监控(1.是；0.否)")
    private String isVideoSurveillance;

    /**
     * 是否开启客户端考试(1.是；0.否)
     */
    @ExcelProperty(value = "是否开启客户端考试(1.是；0.否)")
    private String isClient;

    /**
     * 是否多屏禁用(1.是；0.否)
     */
    @ExcelProperty(value = "是否多屏禁用(1.是；0.否)")
    private String isMultiScreen;

    /**
     * 是否动态监控(1.是；0.否)
     */
    @ExcelProperty(value = "是否动态监控(1.是；0.否)")
    private String isDynamicSupervisory;

    /**
     * 是否强制全屏(1.是；0.否)
     */
    @ExcelProperty(value = "是否强制全屏(1.是；0.否)")
    private String isForceFullScreen;

    /**
     * 是否切换页面(1.是；0.否)
     */
    @ExcelProperty(value = "是否切换页面(1.是；0.否)")
    private String isSwitchPages;

    /**
     * 作弊动作提醒次数
     */
    @ExcelProperty(value = "作弊动作提醒次数")
    private String cheatCnt;

    /**
     * 多个人脸提醒次数
     */
    @ExcelProperty(value = "多个人脸提醒次数")
    private String morePeopleCnt;

    /**
     * 无人脸提醒次数
     */
    @ExcelProperty(value = "无人脸提醒次数")
    private String noOneCnt;

    /**
     * 非本人提醒次数
     */
    @ExcelProperty(value = "非本人提醒次数")
    private String ordersCnt;

    /**
     * 抓拍次数
     */
    @ExcelProperty(value = "抓拍次数")
    private String captureCnt;

    /**
     * 识别错误数阈值
     */
    @ExcelProperty(value = "识别错误数阈值")
    private String recognitionErrorCnt;

    /**
     * 切换页面数阈值
     */
    @ExcelProperty(value = "切换页面数阈值")
    private String switchPagesCnt;

    /**
     * 补考开始时间
     */
    @ExcelProperty(value = "补考开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date resitStartTime;

    /**
     * 补考结束时间
     */
    @ExcelProperty(value = "补考结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date resitEndTime;

    /**
     * 允许考试的最少培训时间（秒）
     */
    @ExcelProperty(value = "允许考试的最少培训时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "秒=")
    private Long allowExamTrainDuration;

    /**
     * 是否手动阅卷(1.是；0.否)
     */
    @ExcelProperty(value = "是否手动阅卷(1.是；0.否)")
    private String isManualReview;

    /**
     * 是否安管人员(1.是；0.否)
     */
    @ExcelProperty(value = "是否安管人员(1.是；0.否)")
    private Long isSafety;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人 ID
     */
    private Long createBy;

    /**
     * 创建人 姓名
     */
    private String userName;

    /**
     * 用户组群名称
     */
    private String groupName;

    /**
     * 用户组用户数量
     */
    private String userCount;

    /**
     * 用户组群id
     */
    private String groupFatherId;

    /**
     * 试题类别id集合(树型结构)
     */
    private List<List<String>> questionTypeList;

    /**
     * 考试试题类型集合
     */
    private List<StExamQuestionGenreVo> stExamQuestionGenreVOList;

    /**
     * 考试试题集合
     */
    private List<StExamQuestionVo> stExamQuestionlist;

    /**
     * 自定义考试试题类型集合Map
     */
    private Map<String, List<StExamQuestionGenreVo>> stExamQuestionGenreVOListMap;

    /**
     * 成绩等级集合
     */
    private List<StScoreLevelVo> stScoreLevelVOList;

    /**
     * 强制申诉超时时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime compulsionComplaintTimeout;
}
