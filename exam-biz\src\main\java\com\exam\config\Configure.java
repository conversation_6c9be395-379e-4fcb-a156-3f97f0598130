package com.exam.config;


import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * <AUTHOR>
 * @date 2021/05/12
 */

@Component
@Setter
@Getter
public class Configure {
    //图片存放位置
    @Value("${train.image.path}")
    private String path;
    //图片上传路径
    @Value("${train.image.upload.path}")
    private String uploadPath;

    //图片服务器
    @Value("${train.image.server}")
    private String imageServerPath;
    @Value("${train.video.server}")
    private String videoServerPath;
    @Value("${train.file.server}")
    private String fileServerPath;

    @Value("${train.file.client}")
    private String clientPath;

    //返回页面路径
    @Value("${train.image.domain}")
    private String imageDomain;

    //视频存放位置
    @Value("${train.video.upload.path}")
    private String saveVideoPaths;
    //视频返回页面路径
    @Value("${train.video.domain}")
    private String videoDomain;

    //文件存放位置
    @Value("${train.file.upload.path}")
    private String saveFilePaths;
    //文件返回页面路径
    @Value("${train.file.domain}")
    private String fileDomain;

    @Value("${train.file.zip.path}")
    private String zipPath;

    @Value("${train.mockQuestionSize:25}")
    private Integer mockQuestionSize;
//	//图片上传路径
//	@Value("${train.image.upload.path}")
//	private String webPaths;

//    //程序实例数量
//    @NacosValue(value = "${instances-number:1}", autoRefreshed = true)
//    private String instancesNumber;
        private  String instancesNumber = "1";

    @Value("${aliface.isMockByAliFace:0}")
    private String isMockByAliFace;

}
