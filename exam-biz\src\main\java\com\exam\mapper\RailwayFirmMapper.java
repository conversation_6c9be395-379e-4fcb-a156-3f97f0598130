package com.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.exam.common.core.domain.PageQuery;
import com.exam.domain.RailwayFirm;
import com.exam.domain.vo.RailwayFirmVo;
import org.apache.ibatis.annotations.Param;

public interface RailwayFirmMapper extends BaseMapper<RailwayFirm> {

    IPage<RailwayFirmVo> queryPage(IPage<RailwayFirmVo> pageQuery,
                                   @Param("rfName") String rfName,
                                   @Param("rfContact") String rfContact,
                                   @Param("rfPhone") String rfPhone,
                                   @Param("rfApprove") Integer rfApprove);

    RailwayFirm getById(@Param("tenantId") Long tenantId);
}
