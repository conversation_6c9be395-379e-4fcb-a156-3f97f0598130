package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 答题卡详细业务对象 st_mock_answer_card_detail
 *
 * <AUTHOR>
 * @date 2023-11-14
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StMockAnswerCardDetailBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 答题卡id
     */
    @NotNull(message = "答题卡id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long answerCardId;

    /**
     * 试题id
     */
    @NotNull(message = "试题id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long questionId;

    /**
     * 试题类型
     */
    @NotNull(message = "试题类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long questionGenre;

    /**
     * 答案
     */
    @NotBlank(message = "答案不能为空", groups = {AddGroup.class, EditGroup.class})
    private String answer;

    /**
     * 题顺序
     */
    @NotNull(message = "题顺序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long questionSn;

    /**
     * 此题得分
     */
    @NotNull(message = "此题得分不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal singleScore;

    /**
     * 判定结果（0：错误，1：正确）
     */
    @NotNull(message = "判定结果（0：错误，1：正确）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long reviewResult;

    /**
     * 详细json
     */
    @NotBlank(message = "详细json不能为空", groups = {AddGroup.class, EditGroup.class})
    private String detailJson;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;


}
