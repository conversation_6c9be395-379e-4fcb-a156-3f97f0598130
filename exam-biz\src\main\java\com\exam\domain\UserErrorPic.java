package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 【请填写功能名称】对象 user_error_pic
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_error_pic")
public class UserErrorPic extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 身份证正面照片地址
     */
    private String idImageUrl;
    /**
     * 错误图片base64编码
     */
    private String errorPicBase;
    /**
     * 注册时间(显示使用)
     */
    private String signTimeStr;
    /**
     * 编辑时间(显示使用)
     */
    private String updateTimeStr;
    /**
     * 创建人id
     */
    private Long signUserId;
    /**
     * 编辑人id
     */
    private Long updateUserId;
    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    private Long dataStatus;
    /**
     * 错误信息
     */
    private String errorMessage;
    /**
     * 错误编码
     */
    private String errorCode;
    /**
     * 姓名
     */
    private String userName;

}
