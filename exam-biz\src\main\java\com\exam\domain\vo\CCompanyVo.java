package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 公司视图对象 c_company
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class CCompanyVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 公司全名称
     */
    @ExcelProperty(value = "公司全名称")
    private String name;

    /**
     * 公司简称
     */
    @ExcelProperty(value = "公司简称")
    private String simpleName;

    /**
     * 产权单位（简）
     */
    @ExcelProperty(value = "产权单位", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "简=")
    private String firm;

    /**
     * 公司级别（0：局级；1：处级；2：分公司；3:外部单位）
     */
    @ExcelProperty(value = "公司级别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=：局级；1：处级；2：分公司；3:外部单位")
    private Long level;

    /**
     * 父级ID(-1为顶级)
     */
    @ExcelProperty(value = "父级ID(-1为顶级)")
    private Long fatherId;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    private String linkman;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String tel;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Long longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Long latitude;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Long sn;

    /**
     * 公司编码(系统编码)
     */
    @ExcelProperty(value = "公司编码(系统编码)")
    private String companyCode;

    /**
     * 是否为内部单位，0为是，1为否
     */
    @ExcelProperty(value = "是否为内部单位，0为是，1为否")
    private Long isInternal;

    /**
     * 法人代表名称
     */
    @ExcelProperty(value = "法人代表名称")
    private String legalPerson;

    /**
     * 法人代表身份证号
     */
    @ExcelProperty(value = "法人代表身份证号")
    private String legalPersonIdCard;

    /**
     * 营业执照号码
     */
    @ExcelProperty(value = "营业执照号码")
    private String businessNo;

    /**
     * 组织机构代码
     */
    @ExcelProperty(value = "组织机构代码")
    private String organizationNo;

    /**
     * 税务登记证号
     */
    @ExcelProperty(value = "税务登记证号")
    private String taxRegistrationNo;

    /**
     * 安全生产许可证编号
     */
    @ExcelProperty(value = "安全生产许可证编号")
    private String workSafetyNo;

    /**
     * 纳税人识别号
     */
    @ExcelProperty(value = "纳税人识别号")
    private String taxpayerIdentificationNumber;

    /**
     * 银行开户户名
     */
    @ExcelProperty(value = "银行开户户名")
    private String accountName;

    /**
     * 银行账号
     */
    @ExcelProperty(value = "银行账号")
    private String accountNo;

    /**
     * 单位地址
     */
    @ExcelProperty(value = "单位地址")
    private String address;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String concatTel;

    /**
     * 资质证书编号
     */
    @ExcelProperty(value = "资质证书编号")
    private String qualificationCertificateNo;

    /**
     * 公司编码(显示使用)
     */
    @ExcelProperty(value = "公司编码(显示使用)")
    private String companyNo;

    /**
     * 注册时间(显示使用)
     */
    @ExcelProperty(value = "注册时间(显示使用)")
    private String signTimeStr;

    /**
     * 编辑时间(显示使用)
     */
    @ExcelProperty(value = "编辑时间(显示使用)")
    private String updateTimeStr;

    /**
     * 创建人id
     */
    @ExcelProperty(value = "创建人id")
    private Long signUserId;

    /**
     * 编辑人id
     */
    @ExcelProperty(value = "编辑人id")
    private Long updateUserId;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @ExcelProperty(value = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))")
    private Long dataStatus;

    /**
     * 开户银行
     */
    @ExcelProperty(value = "开户银行")
    private String accountBank;

    /**
     * 上级节点编码
     */
    @ExcelProperty(value = "上级节点编码")
    private String parentOrgCode;

    /**
     * 节点编码
     */
    @ExcelProperty(value = "节点编码")
    private String orgCode;

    /**
     * 联系电话 区号
     */
    @ExcelProperty(value = "联系电话 区号")
    private String phoneNumberCode;

    /**
     * 国外区域：1      国内区域：0
     */
    @ExcelProperty(value = "国外区域：1      国内区域：0")
    private Long isOverseas;

    /**
     * 所在区县ID
     */
    @ExcelProperty(value = "所在区县ID")
    private Long countyId;


}
