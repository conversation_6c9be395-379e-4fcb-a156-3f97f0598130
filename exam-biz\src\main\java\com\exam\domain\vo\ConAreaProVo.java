package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 区域的子，区域与省的关联视图对象 con_area_pro
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class ConAreaProVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * area表主键id
     */
    @ExcelProperty(value = "area表主键id")
    private Long areaId;

    /**
     * 省表主键id
     */
    @ExcelProperty(value = "省表主键id")
    private Long provinceId;


}
