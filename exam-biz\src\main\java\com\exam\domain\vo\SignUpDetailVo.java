package com.exam.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class SignUpDetailVo {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("所属公司")
    private String projectName;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("手机号")
    private String phonenumber;

    @ApiModelProperty("身份证号")
    private String idNumber;

    @ApiModelProperty("考场名称")
    private String examRoomName;

    @ApiModelProperty("考号")
    private String examNum;

    @ApiModelProperty("考场地址")
    private String examRoomAddress;

    @ApiModelProperty("是否参加考试（0未参加，1参加）")
    private Integer joinFlag;

    @ApiModelProperty("是否参加考试")
    private String joinFlagName;

    public String getJoinFlagName() {
        if(joinFlag==0){
            return "未参加";
        }else if(joinFlag==1){
            return "参加";
        }
        return joinFlagName;
    }
}
