package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 用户文件对象 st_user_file
 *
 * <AUTHOR>
 * @date 2023-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_user_file")
@Builder
public class StUserFile extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户id
     */
    private Long userInfoId;
    /**
     * 文件相对路径
     */
    private String path;
    /**
     * 图片类型  （0：身份证正面， 1：身份证反面）
     */
    private Long type;
    /**
     * 人脸识别失败照片  文件相对路径
     */
    private String faceFailPath;

}
