package com.exam.domain.qo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

/**
 * @Classname StCourseQo
 * @Description TODO
 * <AUTHOR>
 * @Version 1.0
 * @Date 2023/10/31 10:19
 */
@Data
public class StCourseQo {
    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long groupId;

    /**
     * 所属类别
     */
    private Long categoryId;

    /**
     * 是否安管人员(1.是；0.否)
     */
    private Long isSafety;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 是否为精品课程
     */
    private Long isBoutique;

    /**
     * 安管是否分类(1.已分类；0.未分类)
     */
    private Long isSafetyCategory;


}
