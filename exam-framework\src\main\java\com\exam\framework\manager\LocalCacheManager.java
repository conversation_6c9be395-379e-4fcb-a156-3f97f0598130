package com.exam.framework.manager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 本地缓存管理器
 * 用于在集群环境中通过Redis同步各节点的本地缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LocalCacheManager {

    /**
     * 本地缓存，使用ConcurrentHashMap保证线程安全
     */
    private final Map<String, Object> localCache = new ConcurrentHashMap<>();

    /**
     * 获取缓存值
     *
     * @param key 缓存键
     * @return 缓存值
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        return (T) localCache.get(key);
    }

    /**
     * 设置缓存值
     *
     * @param key   缓存键
     * @param value 缓存值
     */
    public <T> void put(String key, T value) {
        localCache.put(key, value);
        log.debug("Local cache updated: key={}", key);
    }

    /**
     * 获取缓存值，如果不存在则通过提供的函数计算并缓存结果
     *
     * @param key      缓存键
     * @param function 计算函数
     * @return 缓存值
     */
    @SuppressWarnings("unchecked")
    public <T> T computeIfAbsent(String key, Function<String, T> function) {
        return (T) localCache.computeIfAbsent(key, function);
    }

    /**
     * 删除缓存
     *
     * @param key 缓存键
     * @return 被删除的缓存值
     */
    @SuppressWarnings("unchecked")
    public <T> T remove(String key) {
        T value = (T) localCache.remove(key);
        log.debug("Local cache removed: key={}", key);
        return value;
    }

    /**
     * 清空所有缓存
     */
    public void clear() {
        localCache.clear();
        log.debug("Local cache cleared");
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存大小
     */
    public int size() {
        return localCache.size();
    }

    /**
     * 判断是否包含指定键
     *
     * @param key 缓存键
     * @return 是否包含
     */
    public boolean containsKey(String key) {
        return localCache.containsKey(key);
    }

    /**
     * 获取所有缓存项
     *
     * @return 所有缓存项
     */
    public Map<String, Object> getAll() {
        return new ConcurrentHashMap<>(localCache);
    }
}
