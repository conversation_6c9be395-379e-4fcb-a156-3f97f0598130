package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Classname AssignCoursesVo
 * @Description TODO
 * <AUTHOR>
 * @Version 1.0
 * @Date 2023/11/6 9:10
 */
@Data
@ExcelIgnoreUnannotated
public class AssignCoursesVo {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "课程id集合")
    private List<Long> courseIds;

    @ExcelProperty(value = "用户组群id集合")
    private List<Long> userGroupIds;
}
