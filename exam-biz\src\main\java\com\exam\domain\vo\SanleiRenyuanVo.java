package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 临时处理数据视图对象 sanlei_renyuan
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class SanleiRenyuanVo {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String companyCode;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String projectName;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String name;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String phoneNum;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String idNum;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String deptName;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String jobName;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String nation;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String sex;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String email;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String userType;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String isLeader;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String insertResult;

    /**
     * 批量标识
     */
    @ExcelProperty(value = "批量标识")
    private String flag;


}
