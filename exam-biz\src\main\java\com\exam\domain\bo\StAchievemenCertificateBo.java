package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 成绩证书业务对象 st_achievemen_certificate
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StAchievemenCertificateBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 答题卡id
     */
    @NotNull(message = "答题卡id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long answerCardId;

    /**
     * 证书编号
     */
    @NotBlank(message = "证书编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String certificateNo;

    /**
     * 单选题得分
     */
    @NotNull(message = "单选题得分不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal singleChoiceScore;

    /**
     * 多选题得分
     */
    @NotNull(message = "多选题得分不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal multipleChoiceScore;

    /**
     * 判断题得分
     */
    @NotNull(message = "判断题得分不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal judgeScore;

    /**
     * 填空题得分
     */
    @NotNull(message = "填空题得分不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal completionScore;

    /**
     * 简答题得分
     */
    @NotNull(message = "简答题得分不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal shortAnswerScore;

    /**
     * 案例题得分
     */
    @NotNull(message = "案例题得分不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal caseScore;

    /**
     * 总分数
     */
    @NotNull(message = "总分数不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal totalScore;

    /**
     * 成绩级别
     */
    @NotBlank(message = "成绩级别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String achievemenLevel;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date createTime;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createBy;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateBy;


}
