package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StMessageUserShieldBo;
import com.exam.domain.vo.StMessageUserShieldVo;
import com.exam.service.IStMessageUserShieldService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 用户屏蔽消息
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/messageUserShield")
public class StMessageUserShieldController extends BaseController {

    private final IStMessageUserShieldService iStMessageUserShieldService;

    @GetMapping(value = "/shield")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "status", value = "屏蔽消息（0：不，1：是）", paramType = "query", dataType = "String")
    })
    @ResponseBody
    public R<Void> shield(String status) {
        iStMessageUserShieldService.shield(status);
        return R.ok();
    }

    /**
     * 获取屏蔽消息
     */
    @GetMapping(value = "/getShield")
    @ApiOperation(value = "1.1 获取屏蔽消息")
    @ResponseBody
    public R<String> getShield(){
        return R.ok(iStMessageUserShieldService.getShield());
    }

    /**
     * 查询用户屏蔽消息列表
     */
    @SaCheckPermission("exam:messageUserShield:list")
    @GetMapping("/list")
    public TableDataInfo<StMessageUserShieldVo> list(StMessageUserShieldBo bo, PageQuery pageQuery) {
        return iStMessageUserShieldService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户屏蔽消息列表
     */
    @SaCheckPermission("exam:messageUserShield:export")
    @Log(title = "用户屏蔽消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StMessageUserShieldBo bo, HttpServletResponse response) {
        List<StMessageUserShieldVo> list = iStMessageUserShieldService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户屏蔽消息", StMessageUserShieldVo.class, response);
    }

    /**
     * 获取用户屏蔽消息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:messageUserShield:query")
    @GetMapping("/{id}")
    public R<StMessageUserShieldVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(iStMessageUserShieldService.queryById(id));
    }

    /**
     * 新增用户屏蔽消息
     */
    @SaCheckPermission("exam:messageUserShield:add")
    @Log(title = "用户屏蔽消息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StMessageUserShieldBo bo) {
        return toAjax(iStMessageUserShieldService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改用户屏蔽消息
     */
    @SaCheckPermission("exam:messageUserShield:edit")
    @Log(title = "用户屏蔽消息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StMessageUserShieldBo bo) {
        return toAjax(iStMessageUserShieldService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户屏蔽消息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:messageUserShield:remove")
    @Log(title = "用户屏蔽消息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(iStMessageUserShieldService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
