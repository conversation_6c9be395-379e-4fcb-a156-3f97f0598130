package com.exam.domain.vo;

import java.util.Date;
import java.util.List;

import com.exam.common.core.domain.entity.SysUser;
import com.exam.domain.StUserIdentityPic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 用户视图对象 c_user
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class CUserVo extends SysUser {

    @ApiModelProperty(value = "身份证图片集合")
    private List<StUserIdentityPic> stUserIdentityPicList;

    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 岗位名称
     */
    private String jobName;
    /**
     * 证件类别
     */
    private String certificateType;

    /**
     * 证件类型名称
     */
    private String certificateTypeName;
}
