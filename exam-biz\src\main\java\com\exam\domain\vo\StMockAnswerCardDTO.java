package com.exam.domain.vo;

import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 模拟答题卡表
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StMockAnswerCardDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 课程类别id集合
     */
    private String questionTypeIds;

    /**
     * 试题总分
     */
    private Integer totalScore;

    /**
     * 模式答题卡id
     */
    private Long id;

    /**
     * 模拟答题详细卡
     */
    private List<StMockAnswerCardDetailDTO> stMockAnswerCardDetailDTOList;

}


