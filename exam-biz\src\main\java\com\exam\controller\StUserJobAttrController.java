package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StUserJobAttrBo;
import com.exam.domain.vo.StUserJobAttrVo;
import com.exam.service.IStUserJobAttrService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 用户岗位属性关系
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/userJobAttr")
public class StUserJobAttrController extends BaseController {

    private final IStUserJobAttrService iStUserJobAttrService;

    /**
     * 查询用户岗位属性关系列表
     */
    @SaCheckPermission("exam:userJobAttr:list")
    @GetMapping("/list")
    public TableDataInfo<StUserJobAttrVo> list(StUserJobAttrBo bo, PageQuery pageQuery) {
        return iStUserJobAttrService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户岗位属性关系列表
     */
    @SaCheckPermission("exam:userJobAttr:export")
    @Log(title = "用户岗位属性关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StUserJobAttrBo bo, HttpServletResponse response) {
        List<StUserJobAttrVo> list = iStUserJobAttrService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户岗位属性关系", StUserJobAttrVo.class, response);
    }

    /**
     * 获取用户岗位属性关系详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:userJobAttr:query")
    @GetMapping("/{id}")
    public R<StUserJobAttrVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStUserJobAttrService.queryById(id));
    }

    /**
     * 新增用户岗位属性关系
     */
    @SaCheckPermission("exam:userJobAttr:add")
    @Log(title = "用户岗位属性关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StUserJobAttrBo bo) {
        return toAjax(iStUserJobAttrService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改用户岗位属性关系
     */
    @SaCheckPermission("exam:userJobAttr:edit")
    @Log(title = "用户岗位属性关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StUserJobAttrBo bo) {
        return toAjax(iStUserJobAttrService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户岗位属性关系
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:userJobAttr:remove")
    @Log(title = "用户岗位属性关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStUserJobAttrService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
