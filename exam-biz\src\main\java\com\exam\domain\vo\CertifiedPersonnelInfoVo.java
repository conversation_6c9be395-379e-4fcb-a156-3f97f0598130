package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 持证人员培训考试
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Data
@ExcelIgnoreUnannotated
public class CertifiedPersonnelInfoVo implements Serializable {

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String userName;
    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private String userInfoId;
    /**
     * 证件号码
     */
    @ExcelProperty(value = "证件号码")
    private String idNumber;
    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码")
    private String phoneNumber;
    /**
     * 单位名称
     */
    @ExcelProperty(value = "单位名称")
    private String work;
    /**
     * 所属公司
     */
    @ExcelProperty(value = "所属公司")
    private String companyName;
    /**
     * 行业
     */
    @ExcelProperty(value = "行业")
    private String industry;
    /**
     * 岗位类别
     */
    @ExcelProperty(value = "岗位类别")
    private String duties;
    /**
     * 岗位工种
     */
    @ExcelProperty(value = "岗位工种")
    private String technical;
    /**
     * 证书编号
     */
    @ExcelProperty(value = "证书编号")
    private String certificateNo;
    /**
     * 发证日期
     */
    @ExcelProperty(value = "发证日期")
    private String issueDate;
    /**
     * 有效期
     */
    @ExcelProperty(value = "有效期")
    private String validityDate;

    /**
     * 培训时间
     */
    @ExcelProperty(value = "培训时间")
    private String trainDate;

    /**
     * 培训课时数
     */
    @ExcelProperty(value = "培训课时数")
    private int classhourCount;
    /**
     * 考核成绩
     */
    @ExcelProperty(value = "考核成绩")
    private String score;


}
