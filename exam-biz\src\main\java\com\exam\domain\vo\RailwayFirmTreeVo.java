package com.exam.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 铁路局——企业管理
 */
@ApiModel(description = "铁路局——企业管理")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RailwayFirmTreeVo {
    /**
     * 企业管理Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "Id")
    private Long id;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "人数")
    private Integer count;

    private List<RailwayFirmTreeVo>firmTreeVoList;
}
