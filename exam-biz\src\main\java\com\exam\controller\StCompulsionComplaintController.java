package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.exam.domain.StCompulsionComplaintDTO;
import com.exam.domain.bo.StCompulsionComplaintBo;
import com.exam.domain.qo.StCompulsionComplaintQo;
import com.exam.domain.vo.StCompulsionComplaintVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.service.IStCompulsionComplaintService;
import com.exam.common.core.page.TableDataInfo;

/**
 * 强制交卷申诉
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/compulsionComplaint")
public class StCompulsionComplaintController extends BaseController {

    private final IStCompulsionComplaintService iStCompulsionComplaintService;

    /**
     * 导出强制交卷申诉列表
     */
    @SaCheckPermission("exam:compulsionComplaint:export")
    @Log(title = "强制交卷申诉", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StCompulsionComplaintBo bo, HttpServletResponse response) {
        List<StCompulsionComplaintVo> list = iStCompulsionComplaintService.queryList(bo);
        ExcelUtil.exportExcel(list, "强制交卷申诉", StCompulsionComplaintVo.class, response);
    }

    /**
     * 获取强制交卷申诉详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:compulsionComplaint:query")
    @GetMapping("/{id}")
    public R<StCompulsionComplaintVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStCompulsionComplaintService.queryById(id));
    }

    /**
     * 新增强制交卷申诉
     */
    @SaCheckPermission("exam:compulsionComplaint:add")
    @Log(title = "强制交卷申诉", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StCompulsionComplaintBo bo) {
        return toAjax(iStCompulsionComplaintService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改强制交卷申诉
     */
    @SaCheckPermission("exam:compulsionComplaint:edit")
    @Log(title = "强制交卷申诉", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StCompulsionComplaintBo bo) {
        return toAjax(iStCompulsionComplaintService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除强制交卷申诉
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:compulsionComplaint:remove")
    @Log(title = "强制交卷申诉", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStCompulsionComplaintService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 发起强制申诉
     * @param stCompulsionComplaintDetailDTO
     * @return
     */
    @ApiOperationSupport(order = 1)
    @SaCheckPermission("exam:compulsionComplaint:saveByInitiate")
    @PostMapping(value = "/saveByInitiate")
    @ApiOperation(value = "1.1 发起强制申诉")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> saveByInitiate(@ApiParam @RequestBody StCompulsionComplaintDTO stCompulsionComplaintDetailDTO) {
        iStCompulsionComplaintService.saveByInitiate(stCompulsionComplaintDetailDTO);
        return R.ok();
    }

    /**
     * 查询强制交卷申诉列表
     * @param stCompulsionComplaintQO
     * @param pageQuery
     * @return
     */
    @SaCheckPermission("exam:compulsionComplaint:pageCompulsionComplaint")
    @GetMapping(value = "/pageCompulsionComplaint")
    @ResponseBody
    public R<IPage<StCompulsionComplaintVo>> pageCompulsionComplaint( StCompulsionComplaintQo stCompulsionComplaintQO, PageQuery pageQuery) {
        return R.ok(iStCompulsionComplaintService.selectPage(stCompulsionComplaintQO,pageQuery));
    }

    /**
     * 提交强制申诉
     * @param stCompulsionComplaintDTO
     * @return
     */
    @ApiOperationSupport(order = 3)
    @SaCheckPermission("exam:compulsionComplaint:submitCompulsionComplaint")
    @PostMapping(value = "/submitCompulsionComplaint")
    @ApiOperation(value = "1.3 提交强制申诉")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<IPage<StCompulsionComplaintVo>> submitCompulsionComplaint(@RequestBody StCompulsionComplaintDTO stCompulsionComplaintDTO) {
        iStCompulsionComplaintService.submitCompulsionComplaint(stCompulsionComplaintDTO);
        return R.ok();
    }

    /**
     * 手动提交缓存试卷
     * @param examId
     * @param userIds
     * @return
     */
    @ApiOperationSupport(order = 4)
    @SaCheckPermission("exam:compulsionComplaint:guestCommmit")
    @GetMapping(value = "/guestCommmit")
    @ApiOperation(value = "1.4 手动提交缓存试卷")
    @ApiImplicitParams({@ApiImplicitParam(name = "examId", value = "examId", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "userIds", value = "用户id多条逗号分割", paramType = "query", required = false, dataType = "String")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R guestCacheCommmit( String examId,String userIds) {
        iStCompulsionComplaintService.guestCacheCommmit(examId,userIds);
        return R.ok();
    }

}
