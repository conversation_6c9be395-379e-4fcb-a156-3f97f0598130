package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StCourseCategoryBo;
import com.exam.domain.vo.StCourseCategoryVo;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.service.IStCourseCategoryService;
import com.exam.common.core.page.TableDataInfo;

/**
 * 课程类别
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/courseCategory")
public class StCourseCategoryController extends BaseController {

    private final IStCourseCategoryService iStCourseCategoryService;

    /**
     * 查询课程类别列表
     */
    @SaCheckPermission("exam:courseCategory:list")
    @GetMapping("/list")
    public TableDataInfo<StCourseCategoryVo> list(StCourseCategoryBo bo, PageQuery pageQuery) {
        return iStCourseCategoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出课程类别列表
     */
    @SaCheckPermission("exam:courseCategory:export")
    @Log(title = "课程类别", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StCourseCategoryBo bo, HttpServletResponse response) {
        List<StCourseCategoryVo> list = iStCourseCategoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "课程类别", StCourseCategoryVo.class, response);
    }

    /**
     * 获取课程类别详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:courseCategory:query")
    @GetMapping("/{id}")
    public R<StCourseCategoryVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStCourseCategoryService.queryById(id));
    }

    /**
     * 新增课程类别
     */
    @SaCheckPermission("exam:courseCategory:add")
    @Log(title = "课程类别", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StCourseCategoryBo bo) {
        return toAjax(iStCourseCategoryService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改课程类别
     */
    @SaCheckPermission("exam:courseCategory:edit")
    @Log(title = "课程类别", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StCourseCategoryBo bo) {
        return toAjax(iStCourseCategoryService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除课程类别
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:courseCategory:remove")
    @Log(title = "课程类别", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStCourseCategoryService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
