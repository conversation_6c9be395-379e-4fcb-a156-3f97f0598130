package com.exam.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.exam.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@ApiModel(description="铁路局-企业管理-消息pagevo")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RailwayMessagePageVo extends BaseEntity {

    /**
     * 消息Id
     */
    @TableId(value = "rm_id", type = IdType.AUTO)
    @ApiModelProperty(value="消息Id")
    private Long rmId;

    /**
     * 消息标题
     */
    @TableField(value = "rm_title")
    @ApiModelProperty(value="消息标题")
    private String rmTitle;

    /**
     * 消息内容
     */
    @TableField(value = "rm_content")
    @ApiModelProperty(value="消息内容")
    private String rmContent;

    /**
     * 状态1已读｜0未读
     */
    @TableField(value = "rm_status")
    @ApiModelProperty(value="状态1已读｜0未读")
    private Boolean rmStatus;

    /**
     * 已读时间
     */
    @TableField(value = "rm_read")
    @ApiModelProperty(value="已读时间")
    private LocalDateTime rmRead;

    /**
     * 发送用户
     */
    @TableField(value = "rm_send_user")
    @ApiModelProperty(value="发送用户")
    private Long rmSendUser;
}
