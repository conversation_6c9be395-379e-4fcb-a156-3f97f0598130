package com.exam.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.core.domain.entity.SysUser;
import com.exam.common.helper.LoginHelper;
import com.exam.common.utils.ThreadPoolUtil;
import com.exam.common.utils.redis.RedisUtils;
import com.exam.constant.*;
import com.exam.domain.*;
import com.exam.domain.vo.*;
import com.exam.exception.BizException;
import com.exam.mapper.*;
import com.exam.service.*;
import com.exam.system.service.ISysUserService;
import com.exam.utils.*;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.lang.Math.round;

/**
 * <p>
 * 考试用户端 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Service
@Transactional
@RequiredArgsConstructor
public class ExamServiceImpl implements IExamService {

    private static final Logger logger = LoggerFactory.getLogger(ExamServiceImpl.class);
    private  final
    IStQuestionService stQuestionService;
    private  final
    IStExamService stExamService;
    private  final
    IStAnswerCardService stAnswerCardService;
    private  final
    IStExamQuestionService stExamQuestionService;
    private  final
    IStAnswerCardDetailService stAnswerCardDetailService;
    private  final
    AccountMapper accountMapper;
    private  final
    ExamClientMapper examMapper;
    private  final
    IStAchievemenCertificateService stAchievemenCertificateService;
    private  final
    IStExamQuestionGenreService stExamQuestionGenreService;
    private  final
    IStScoreLevelService stScoreLevelService;
    private  final
    IStUserWrongActService stUserWrongActService;
    private  final
    StUserWrongActMapper stUserWrongActMapper;
    private  final
    StUserExamMapper stUserExamMapper;
    //    private  final
    //     ExamManageService examManageService;
    private  final
    IStPlayProgresOptionalService stPlayProgresOptionalService;
    private  final
    StDisputeComplaintDetailMapper disputeComplaintDetailMapper;
    private  final
    IStQuestionKeyWordService stQuestionKeyWordService;
    private  final
    IStUserGroupService stUserGroupService;
    private  final
    IStCompulsionComplaintService compulsionComplaintService;
    private  final
    StQuestionMapper stQuestionMapper;

    @Autowired
    @Lazy
    ISysUserService userService;
//    private final
//    ProducerExam producerExam;

    /**
     * 提交试卷
     *
     * @param stAnswerCardVO 答题卡信息
     * @return 答题卡Id
     */
    @Override
    public StAnswerCardVo submitExamPapers(StAnswerCardVo stAnswerCardVO) {
        RateLimiterUtils.exec();
        Long userId;
        if(GyUtils.isNull(stAnswerCardVO.getUserId())){
            userId = LoginHelper.getUserId();
            stAnswerCardVO.setUserId(userId);
        }else{
            userId = stAnswerCardVO.getUserId();
        }
        if(GyUtils.isNull(stAnswerCardVO.getProjectId())){
            stAnswerCardVO.setProjectId(LoginHelper.getProjectId());
        }
        if(GyUtils.isNull(stAnswerCardVO.getTenantId())){
            stAnswerCardVO.setTenantId(LoginHelper.getTenantId());
        }
        //自动阅卷
        reviewExamPapers(stAnswerCardVO);
        String answercardKey = RedisConsts.ANSWERCARD_KEY + stAnswerCardVO.getExamId() + ":" + userId;
        RedisUtils.setCacheObject(answercardKey, JSONObject.toJSONString(stAnswerCardVO), 60 * 60 * 36L);

        // 获取单例线程池实例
        ThreadPoolUtil.execute(() -> {
            logger.info("保存试卷，用户ID = " + userId  + ",考试id = " + stAnswerCardVO);
            saveAnswerCard(stAnswerCardVO);
        });
        return stAnswerCardVO;
    }

    @Transactional
    public void saveAnswerCard(StAnswerCardVo stAnswerCardVO) throws BizException {
        //插入答题卡表
        saveExamPapers(stAnswerCardVO);
        // 删除缓存
        removeChache(stAnswerCardVO);
    }

    private void saveCreateCertificate(StAchievemenCertificateVo certificateVo) {

        StAchievemenCertificate stAchievemenCertificate = new StAchievemenCertificate();
        CopyUtils.copyPropertiesEtyToVo(certificateVo, stAchievemenCertificate);
        stAchievemenCertificate.setAnswerCardId(certificateVo.getAnswerCardId());
        stAchievemenCertificate.setId(SequenceBean.getSequence());
        stAchievemenCertificate.setDelFlag(CommonDataBaseConst.YES_OR_NO.NO.getCode());
        stAchievemenCertificate.setCreateTime(new Date());
        stAchievemenCertificate.setCreateBy(Long.parseLong(certificateVo.getUserId()));
//            stAchievemenCertificateService.save(stAchievemenCertificate);
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.eq("del_flag", CommonDataBaseConst.YES_OR_NO.NO.getCode());
        updateWrapper.eq("answer_card_id", certificateVo.getAnswerCardId());
        stAchievemenCertificateService.saveOrUpdate(stAchievemenCertificate, updateWrapper);
        certificateVo.setId(stAchievemenCertificate.getId());
    }

    private void removeChache(StAnswerCardVo stAnswerCardVO) {
        // 获取考试id
        String userExamKey = RedisConsts.USER_EXAM_KEY + stAnswerCardVO.getUserId();
        // 获取答题卡
        String answercardKey = RedisConsts.ANSWERCARD_KEY + stAnswerCardVO.getExamId() + ":" + stAnswerCardVO.getUserId();
        RedisUtils.deleteObject(userExamKey);
        RedisUtils.deleteObject(answercardKey);
    }

    private StAchievemenCertificateVo createCertificate(StAnswerCardVo stAnswerCardVO) {
        StAchievemenCertificateVo stAchievemenCertificateVO = new StAchievemenCertificateVo();

        List<StAnswerCardDetailVo> answerCardDetailVOList = stAnswerCardVO.getAnswerCardDetailVOList();
        if (answerCardDetailVOList == null || answerCardDetailVOList.size() == 0) {
            return null;
        }
        Map<Integer, List<StAnswerCardDetailVo>> questionData = answerCardDetailVOList.stream().collect(Collectors.groupingBy(StAnswerCardDetailVo::getQuestionGenre));
        //单选题分数
        double singleChoiceScore = 0;
        if (questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.SINGLE_CHOICE.getCode()) != null) {
            singleChoiceScore = questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.SINGLE_CHOICE.getCode()).stream().mapToDouble(StAnswerCardDetailVo::getSingleScore).sum();
        }
        //多选题分数
        double multipleChoiceScore = 0;
        if (questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.MULTIPLE_CHOICE.getCode()) != null) {
            multipleChoiceScore = questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.MULTIPLE_CHOICE.getCode()).stream().mapToDouble(StAnswerCardDetailVo::getSingleScore).sum();
        }
        //判断题分数
        double judgeScore = 0;
        if (questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.JUDGE.getCode()) != null) {
            judgeScore = questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.JUDGE.getCode()).stream().mapToDouble(StAnswerCardDetailVo::getSingleScore).sum();
        }

        //填空题分数
        double completionScore = 0;
        if (questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.COMPLETION.getCode()) != null) {
            completionScore = questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.COMPLETION.getCode()).stream().mapToDouble(StAnswerCardDetailVo::getSingleScore).sum();
        }
        //简答题分数
        double shortAnswerScore = 0;
        if (questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.SHORTANSWER.getCode()) != null) {
            shortAnswerScore = questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.SHORTANSWER.getCode()).stream().mapToDouble(StAnswerCardDetailVo::getSingleScore).sum();
        }
        //案例题得分
        double caseScore = 0;
        if (questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.CASE.getCode()) != null) {
            caseScore = questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.CASE.getCode()).stream().mapToDouble(StAnswerCardDetailVo::getSingleScore).sum();
        }
        //成绩级别
        double score = stAnswerCardVO.getScore();
        //查看成绩级别
        StExam stExam = stExamService.getById(stAnswerCardVO.getExamId());
        //成绩级别
        String achievemenLevel = "";
        //查看是否及格
        if (score >= stExam.getPassScore() && stExam.getIsSetLevel().equals(String.valueOf(CommonDataBaseConst.YES_OR_NO.YES.getCode()))) {
            //及格并且设置了成绩级别的情况下
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("exam_id", stAnswerCardVO.getExamId());
            List<StScoreLevel> scoreLevelList = stScoreLevelService.list(queryWrapper);
            for (StScoreLevel stScoreLevel : scoreLevelList) {
                if (stScoreLevel.getLowestScore() <= score && score <= stScoreLevel.getHighestScore()) {
                    achievemenLevel = stScoreLevel.getScoreLevelName();
                }
            }
            if (GyUtils.isNull(achievemenLevel)) {
                achievemenLevel = CommonConsts.SCORE_LEVEL_PASS;
            }
        } else if (score >= stExam.getPassScore()) {
            achievemenLevel = CommonConsts.SCORE_LEVEL_PASS;
        } else {
            achievemenLevel = CommonConsts.SCORE_LEVEL_FAIL;
        }
        stAchievemenCertificateVO.setTotalScore(stAnswerCardVO.getScore());
        stAchievemenCertificateVO.setSingleChoiceScore(singleChoiceScore);
        DecimalFormat df = new DecimalFormat("0.00");
        stAchievemenCertificateVO.setMultipleChoiceScore(Double.valueOf(df.format(multipleChoiceScore)));
        stAchievemenCertificateVO.setJudgeScore(judgeScore);
        stAchievemenCertificateVO.setCompletionScore(completionScore);
        stAchievemenCertificateVO.setShortAnswerScore(shortAnswerScore);
        stAchievemenCertificateVO.setCaseScore(caseScore);
        stAchievemenCertificateVO.setAchievemenLevel(achievemenLevel);
        stAchievemenCertificateVO.setUserId(String.valueOf(stAnswerCardVO.getUserId()));
        SysUser sysUser = userService.selectUserById(stAnswerCardVO.getUserId());
        if (sysUser!=null) {
            stAchievemenCertificateVO.setUserName(sysUser.getNickName());
        }
        stAchievemenCertificateVO.setExamName(stExam.getExamName());
        stAchievemenCertificateVO.setAllowedTimes(Integer.valueOf(String.valueOf(stExam.getAllowedTimes())));
        return stAchievemenCertificateVO;
    }

    /**
     * 保存答题卡
     *
     * @param stAnswerCardVO 答题卡信息
     * @return 答题卡Id
     */
    public long saveExamPapers(StAnswerCardVo stAnswerCardVO) {
        StAnswerCard stAnswerCard = new StAnswerCard();
        //把其他次考试是否为最后一次置为否
        //成绩级别
        double score = stAnswerCardVO.getScore();
        //查看是否及格
        if (score >= stAnswerCardVO.getPassScore()) {
            stAnswerCard.setIsPass(CommonConsts.YES);
        } else {
            stAnswerCard.setIsPass(CommonConsts.NO);
        }
        long currentUserId = stAnswerCardVO.getUserId();
        //维护用户考试关系
        StUserExam stUserExam = new StUserExam();
        stUserExam.setJoinFlag(CommonDataBaseConst.YES_OR_NO.YES.getCode());
        UpdateWrapper userExamWrapper = new UpdateWrapper();
        userExamWrapper.eq("exam_id",stAnswerCardVO.getExamId());
        userExamWrapper.eq("user_id",stAnswerCardVO.getUserId());
        stUserExamMapper.update(stUserExam,userExamWrapper);
        //插入答题卡表
        stAnswerCard.setExamId(stAnswerCardVO.getExamId());
        if (!"0".equals(stAnswerCardVO.getUserId())) {
            stAnswerCard.setUserId(stAnswerCardVO.getUserId());
        }
        stAnswerCard.setAnswerCardStatus(Long.valueOf(CommonConsts.ANSWER_CARD_STATUS_1));
        stAnswerCard.setReviewStatus(stAnswerCardVO.getReviewStatus());
        stAnswerCard.setDelFlag(CommonDataBaseConst.YES_OR_NO.NO.getCode());
        stAnswerCard.setIsForced(stAnswerCardVO.getIsForced());
        stAnswerCard.setIsLatest(CommonConsts.YES);
        stAnswerCard.setScore(stAnswerCardVO.getScore());
        stAnswerCard.setTerminal(stAnswerCardVO.getTerminal());
        stAnswerCard.setProjectId(stAnswerCardVO.getProjectId());
        stAnswerCard.setTenantId(stAnswerCardVO.getTenantId());
        stAnswerCard.setCreateBy(currentUserId);
        stAnswerCard.setCreateTime(stAnswerCardVO.getCreateTime());
        stAnswerCard.setUpdateTime(new Date());
//        stAnswerCard.setDomainCode(stAnswerCardVO.getDomainCode());
        stAnswerCard.setSubmitReason(stAnswerCardVO.getSubmitReason());
        stAnswerCard.setSingleChoiceScore(stAnswerCardVO.getSingleChoiceScore());
        stAnswerCard.setMultipleChoiceScore(stAnswerCardVO.getMultipleChoiceScore());
        stAnswerCard.setJudgeScore(stAnswerCardVO.getJudgeScore());
//        UpdateWrapper  updateWrapper = new UpdateWrapper();
//        updateWrapper.eq("exam_id",stAnswerCardVO.getUserId());
//        updateWrapper.eq("user_id",stAnswerCardVO.getUserId() );
//        updateWrapper.eq("del_flag",CommonDataBaseConst.YES_OR_NO.NO.getCode() );
//        stAnswerCardService.saveOrUpdate(stAnswerCard,updateWrapper);
        stAnswerCardService.save(stAnswerCard);
        if (GyUtils.isNull(stAnswerCardVO.getAnswerCardDetailVOList())) {
            return Long.valueOf(stAnswerCard.getId());
        }
        //插入答题卡详细表
        List<StAnswerCardDetailVo> answerCardDetailVOList = stAnswerCardVO.getAnswerCardDetailVOList();
        String detailJson = JSON.toJSONString(answerCardDetailVOList);
        StAnswerCardDetail stAnswerCardDetail = new StAnswerCardDetail();
        stAnswerCardDetail.setId(IdUtil.getSnowflakeNextId());
        stAnswerCardDetail.setAnswerCardId(stAnswerCard.getId());
        stAnswerCardDetail.setCreateTime(new Date());
        stAnswerCardDetail.setCreateBy(currentUserId);
        stAnswerCardDetail.setDetailJson(detailJson);
        stAnswerCardDetailService.save(stAnswerCardDetail);
        return Long.valueOf(stAnswerCard.getId());
    }

    /**
     * 查询考试对应题型分值
     *
     * @param examId 考试id
     */
    public Map<String, Double> getQuestionGenreScore(String examId) {
        Map<String, Double> retData;
        if (RedisUtils.hasKey(RedisKeyConstant.QUESTION_GENRE_SCORE_KEY + examId)) {
            retData = (Map<String, Double>) RedisUtils.getCacheObject(RedisKeyConstant.QUESTION_GENRE_SCORE_KEY + examId);
        } else {
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("exam_id", examId);
            queryWrapper.eq("del_flag", CommonDataBaseConst.YES_OR_NO.NO.getCode());
            List<StExamQuestionGenre> stExamQuestionGenres = stExamQuestionGenreService.list(queryWrapper);
            retData = stExamQuestionGenres.stream().collect(Collectors.toMap(s -> String.valueOf(s.getQuestionGenreId()), s -> s.getQuestionGenreScore(), (v1, v2) -> v1));
            RedisUtils.setCacheObject(RedisKeyConstant.QUESTION_GENRE_SCORE_KEY + examId, retData, 60L);
        }
        return retData;
    }

    /**
     * 阅卷
     *
     * @param stAnswerCardVO
     */
    public void reviewExamPapers(StAnswerCardVo stAnswerCardVO) {
        //查询考试对应题型分值
        Map<String, Double> genreScoreMap = getQuestionGenreScore(String.valueOf(stAnswerCardVO.getExamId()));
        double score = 0;
        //单选题得分
        double singleChoiceScore = 0;
        //多选题得分
        double multipleChoiceScore = 0;
        //判断题得分
        double judgeScore = 0;
        DecimalFormat df = new DecimalFormat("0.00");
        //答题卡不为空
        if (GyUtils.isNotNull(stAnswerCardVO.getAnswerCardDetailVOList())) {
            //是否手动阅卷
            for (StAnswerCardDetailVo stAnswerCardDetailVO : stAnswerCardVO.getAnswerCardDetailVOList()) {
                //判断答案是否正确 默认为错误
                int reviewResult = CommonConsts.REVIEW_RESULT_ERROR;
                //正确答案
                String rightKey = stAnswerCardDetailVO.getRightKey();

                //根据题型，取得取得该道题分数
                double singleScore = 0;
                //答案
                String answer = stAnswerCardDetailVO.getAnswer();
                //该题型分数
                double questionScore = genreScoreMap.get(String.valueOf(stAnswerCardDetailVO.getQuestionGenre()));
                if(GyUtils.isNotNull(answer) && GyUtils.isNotNull(rightKey)){
                    switch (stAnswerCardDetailVO.getQuestionGenre()) {
                        //单选
                        case CommonDataBaseConst.CONST_SINGLE_CHOICE:
                            if (!StringUtils.isBlank(answer) && answer.equals(rightKey)) {
                                singleScore = questionScore;
                                reviewResult = CommonConsts.REVIEW_RESULT_RIGHT;
                            }
                            singleChoiceScore =Double.valueOf(df.format(singleChoiceScore + singleScore)) ;
                            break;
                        //多选
                        case CommonDataBaseConst.CONST_MULTIPLE_CHOICE:
                            if (StringUtils.isNotBlank(answer) && answer.length() <= rightKey.length()) {
                                int flag = unorderedEquals(rightKey,answer);
                                if (flag == 1) {
                                    singleScore = questionScore;
                                    reviewResult = CommonConsts.REVIEW_RESULT_RIGHT;
                                } else if (flag == 2) {
                                    singleScore = questionScore / 2;
                                }
                            }
                            multipleChoiceScore =Double.valueOf(df.format(multipleChoiceScore + singleScore)) ;
                            break;
                        //判断
                        case CommonDataBaseConst.CONST_JUDGE:
                            if (!StringUtils.isBlank(answer) && answer.equals(rightKey)) {
                                singleScore = questionScore;
                                reviewResult = CommonConsts.REVIEW_RESULT_RIGHT;
                            }
                            judgeScore =Double.valueOf(df.format(judgeScore + singleScore)) ;
                            break;
                    }
                }
                if (singleScore > questionScore) {
                    singleScore = questionScore;
                }
                stAnswerCardDetailVO.setSingleScore(singleScore);
                stAnswerCardDetailVO.setReviewResult(Long.valueOf(reviewResult));
                score =Double.valueOf(df.format(score + singleScore)) ;
            }
        }
        stAnswerCardVO.setScore(score);
        stAnswerCardVO.setSingleChoiceScore(singleChoiceScore);
        stAnswerCardVO.setMultipleChoiceScore(multipleChoiceScore);
        stAnswerCardVO.setJudgeScore(judgeScore);
    }

    /**
     * 位运算对比
     *
     * @param s1
     * @param s2
     * @return 完全相等返回1；s1包含s2 返回2；不相等也不包含返回0
     */
    public static int unorderedEquals(String s1, String s2) {
        if (s1 == null || s2 == null){
            return 0;
        }
        long mask1 = buildBitMask(s1);
        long mask2 = buildBitMask(s2);
        if(mask1 == mask2){
            return 1;
        } else if ((mask1 & mask2) == mask2) {
            return 2;
        }
        return 0;
    }

    private static long buildBitMask(String s) {
        long mask = 0;
        if (s.isEmpty()) return mask;
        String[] parts = s.split(",");
        for (String part : parts) {
            int num = Integer.parseInt(part.trim());
            if (num < 0 || num > 63) {
                throw new IllegalArgumentException("Number out of range (0-63): " + num);
            }
            mask |= 1L << num; // 将第num位置为1
        }
        return mask;
    }
    /**
     * 对比数字类型答案
     * 当正确答案，含有数字大于总长度一半时，认定为数字型答案，
     * 此类答案因为单位偏差容易误判，所以提取纯数字作为对比
     *
     * @param answer
     * @param rightKey
     */
    public boolean compareNumberAnswer(String answer, String rightKey) {
        if(GyUtils.isNull(answer) || GyUtils.isNull(rightKey)){
            return false;
        }
        String extRightKey = extractPureNumber(rightKey);
        if((double)extRightKey.length()/rightKey.length()>0.4){
            String extAnswer = extractPureNumber(answer);
            if(extAnswer.contains(extRightKey)){
                return true;
            }
        }
        return false;
    }
    public  String extractPureNumber(String input) {
        // 正则表达式仅匹配数字
        return input.replaceAll("[^0-9\\.]", "");
    }

    /**
     * 处理手动阅卷数据
     *
     * @param stAnswerCardVO
     */
    public void manualReviewPapers(StAnswerCardVo stAnswerCardVO) {
        //查询考试对应题型分值
        Map<String, Double> genreScoreMap = getQuestionGenreScore(String.valueOf(stAnswerCardVO.getExamId()));
        double score = 0;
        if (GyUtils.isNotNull(stAnswerCardVO.getAnswerCardDetailVOList())) {
            for (StAnswerCardDetailVo stAnswerCardDetailVO : stAnswerCardVO.getAnswerCardDetailVOList()) {
                //根据题型，取得取得该道题分数
                double questionScore = genreScoreMap.get(String.valueOf(stAnswerCardDetailVO.getQuestionGenre()));
                DecimalFormat df = new DecimalFormat("0.00");
                double singleScore = 0;
                if (CommonDataBaseConst.SHORT_ANSWER  == stAnswerCardDetailVO.getQuestionGenre()) {
                    int reviewResult = CommonConsts.REVIEW_RESULT_ERROR;
                    if(CommonDataBaseConst.YES_OR_NO.NO.getCode() == stAnswerCardVO.getIsAutoManualReview()){
                        //简答
                        singleScore = stAnswerCardDetailVO.getSingleScore();
                        if (singleScore > questionScore * 0.6) {
                            reviewResult = CommonConsts.REVIEW_RESULT_RIGHT;
                        }
                    }else {
                        String answer = SynonymUtil.replaceSynonym(filterStr(stAnswerCardDetailVO.getAnswer()));
                        String rightKey = SynonymUtil.replaceSynonym(filterStr(stAnswerCardDetailVO.getRightKey()));
                        if (!StringUtils.isBlank(answer)) {
                            if (stAnswerCardDetailVO.getIsKeyWord() == null || stAnswerCardDetailVO.getIsKeyWord() == 0) {
                                //完全一样
                                if (answer.equals(rightKey) || answer.indexOf(rightKey) > -1 || StringUtil.equals(answer, rightKey)) {
                                    singleScore = questionScore;
                                    reviewResult = CommonConsts.REVIEW_RESULT_RIGHT;
                                } else {
                                    //匹配相似度
                                    float flScore = SimilarityRatio.getSimilarityRatio(rightKey, answer);
                                    singleScore = round(questionScore * flScore / 100);
                                    if (singleScore > questionScore * 0.6) {
                                        reviewResult = CommonConsts.REVIEW_RESULT_RIGHT;
                                    }
                                }
                            } else {
                                QueryWrapper queryWrapper = new QueryWrapper();
                                queryWrapper.eq("question_id", stAnswerCardDetailVO.getQuestionId());
                                List<StQuestionKeyWord> keyWordList = stQuestionKeyWordService.list(queryWrapper);
                                for (StQuestionKeyWord questionKeyWord : keyWordList) {
                                    String keyWordContent = SynonymUtil.replaceSynonym(filterStr(questionKeyWord.getKeyWordContent()));
                                    double scoreProportion = questionKeyWord.getScoreProportion();
                                    if (answer.contains(keyWordContent)) {
                                        singleScore += (questionScore * scoreProportion / 100);
                                    } else {
                                        //匹配相似度
                                        float flScore = getSameProportion(keyWordContent, answer);
                                        singleScore += (questionScore * flScore * scoreProportion / 100);
                                    }
                                }
                                singleScore = (double) Math.round(singleScore * 100) / 100;
                                if (singleScore > questionScore) {
                                    singleScore = questionScore;
                                }
                                if (singleScore > questionScore * 0.6) {
                                    reviewResult = CommonConsts.REVIEW_RESULT_RIGHT;
                                }
                            }
                        }
                    }
                    stAnswerCardDetailVO.setSingleScore(singleScore);
                    stAnswerCardDetailVO.setReviewResult(Long.valueOf(reviewResult));
                }else if(CommonDataBaseConst.CASE == stAnswerCardDetailVO.getQuestionGenre()){
                    int reviewResult = CommonConsts.REVIEW_RESULT_ERROR;
                    if(CommonDataBaseConst.YES_OR_NO.NO.getCode() == stAnswerCardVO.getIsAutoManualReview()){
                        //案例题
//                            singleScore = stAnswerCardDetailVO.getRightKey();
                        List<JsonDataDTO> jsonDataDTOS = jsonForMat(stAnswerCardDetailVO.getRightKey());
                        singleScore = jsonDataDTOS.stream().mapToDouble(scoreValue -> scoreValue.getSingleScore()).sum();
                        if (singleScore > questionScore * 0.6) {
                            reviewResult = CommonConsts.REVIEW_RESULT_RIGHT;
                        }
                    }else {
                        //案例题 全部小问答案分数 初始化
                        List<Double> questionScoreList = new ArrayList<>();
//                            //答案(包含正确答案和用户答案)
//                            String rightKey = SynonymUtil.replaceSynonym(filterStr(stAnswerCardDetailVO.getRightKey()));
//                            String rightKey = caseDecryBase64(stAnswerCardDetailVO.getRightKey());
                        String rightKey = stAnswerCardDetailVO.getRightKey();
                        //答案对象
                        List<JsonDataDTO> rightKeyList = jsonForMat(rightKey);
                        //计算每一小题分 放入questionScoreList
                        if(rightKeyList != null && !rightKeyList.isEmpty()){
                            if(0.0  == rightKeyList.get(0).getProportion()){
                                //没有设置分数比例的情况下
                                //没有余数 分数平均
                                if(questionScore % rightKeyList.size() == 0){
                                    for (JsonDataDTO jsonDataDTO : rightKeyList) {
                                        questionScoreList.add(questionScore / rightKeyList.size());
                                    }
                                }
                                //有余数 余数分放在最后一题
                                if(questionScore % rightKeyList.size() != 0){
                                    for (int i = 1; i <= rightKeyList.size(); i++) {
                                        if(i == rightKeyList.size()){
                                            //最后一题
                                            questionScoreList.add(Math.floor((questionScore / rightKeyList.size())) + (questionScore % rightKeyList.size()));
                                        }else {
                                            //其他
                                            questionScoreList.add(Math.floor(questionScore / rightKeyList.size()));
                                        }
                                    }
                                }
                            } else{
                                //设置分数比例的情况下
                                double proportionTotal = rightKeyList.stream().mapToDouble(jsonDataDTO -> jsonDataDTO.getProportion()).sum();
                                for (JsonDataDTO jsonDataDTO : rightKeyList) {
                                    questionScoreList.add(questionScore * (jsonDataDTO.getProportion()/proportionTotal));
                                }
                            }

                        }
                        //有答案
                        if (rightKeyList != null && !rightKeyList.isEmpty()) {
                            //没有关键词
                            if (stAnswerCardDetailVO.getIsKeyWord() == null || stAnswerCardDetailVO.getIsKeyWord() == 0) {
                                for (int i = 0; i < rightKeyList.size(); i++) {
                                    //第i个正确答案与我的答案
                                    String answerI = SynonymUtil.replaceSynonym(filterStr(rightKeyList.get(i).getAnswerExam()));
                                    String rightKeyI = SynonymUtil.replaceSynonym(filterStr(rightKeyList.get(i).getAnswer()));
                                    //完全一样
                                    if (answerI.equals(rightKeyI) ||
                                        compareNumberAnswer(answerI,rightKeyI)||
                                        answerI.indexOf(rightKeyI) > -1 ||
                                        StringUtil.equals(answerI, rightKeyI)) {
                                    } else {
                                        //匹配相似度
                                        float flScore = SimilarityRatio.getSimilarityRatio(rightKeyI, answerI);
                                        Double scoreI = Double.valueOf(round(questionScoreList.get(i) * flScore / 100));
                                        if(scoreI > questionScoreList.get(i) ){
                                            scoreI = questionScoreList.get(i);
                                        }
                                        questionScoreList.set(i,scoreI);
                                    }
                                }
                            }
                            //计算该案例题所得总分
                            singleScore = questionScoreList.stream().mapToDouble(scoreValue -> scoreValue.doubleValue()).sum();
                            //该题大于分数60%即为正确
                            reviewResult = singleScore >= (questionScore * 0.6) ? CommonConsts.REVIEW_RESULT_RIGHT : CommonConsts.REVIEW_RESULT_ERROR;
                        }
                    }

                    stAnswerCardDetailVO.setSingleScore(singleScore);
                    stAnswerCardDetailVO.setReviewResult(Long.valueOf(reviewResult));
                }else {
                    singleScore = stAnswerCardDetailVO.getSingleScore();
                }
                score =Double.valueOf(df.format(score + singleScore)) ;
            }
            //手动阅卷的情况置为已阅卷
            stAnswerCardVO.setReviewStatus(Long.valueOf(CommonDataBaseConst.YES_OR_NO.YES.getCode()));
        }
        stAnswerCardVO.setScore(score);
    }
    /**
     * 去除标点和空格
     *
     * @param orgstr
     */
    public String filterStr(String orgstr) {
        if(ObjectUtil.isEmpty(orgstr)){
            return "";
        }
        return orgstr.replaceAll("[\\pP\\p{Punct}]", "").replaceAll("\\s*", "");
    }

//    @Test
    public void test1() {
        String keyWord1 = "asdf";
        String keyWord2 = "45645";
        String answer = "as45";
        System.out.println(getSameProportion(keyWord1, answer));
    }

    public float getSameProportion(String keyWord, String answer) {
        if (GyUtils.isNull(keyWord) || GyUtils.isNull(answer)) {
            return 0;
        }
        char[] arr1 = keyWord.toCharArray();
        char[] arr2 = answer.toCharArray();
        StringBuffer sb = new StringBuffer();

        for (int i = 0; i < arr2.length; i++) {

            for (int j = 0; j < arr1.length; j++) {

                if (arr1[j] == (arr2[i])) {

                    sb.append(arr1[j]);
                }
            }
        }

        return (float) sb.length() / (float) keyWord.length();

    }

    //纠纷申诉
    private  final
    StDisputeComplaintMapper disputeComplaintMapper;

    /**
     * 补考次数
     *
     * @param examId 考试id
     * @param userId 用户id
     * @return 补考次数
     */
    @Override
    public Long getRetestTimes(long examId, long userId) {
        return examMapper.getRetestTimes(examId, userId);
    }

    /**
     * 查询成绩列表
     *
     * @return 查询成绩
     */
    @Override
    public IPage<AchievemenListVo> selectAchievemenList(Page page, String startDate) {
        Map<String, Long> retestTime = new HashMap<>();
        Map conditionParam = new HashMap();
        conditionParam.put("userId", LoginHelper.getUserId());
//        conditionParam.put("tenantId", LoginHelper.getTenantId());
            conditionParam.put("startDate", startDate);
        IPage<AchievemenListVo> data = examMapper.selectAchievemenList(page, conditionParam);
        //查询在同一公司得排名
        for (AchievemenListVo achievemenVO : data.getRecords()) {
            conditionParam.put("examId", achievemenVO.getExamId());
            conditionParam.put("projectId", LoginHelper.getUserId());
//            conditionParam.put("tenantId", LoginHelper.getTenantId());
            AchievemenVo achievemenVO1 = examMapper.getUserRank(conditionParam);
            if (achievemenVO1 == null) {
                achievemenVO.setRowno(0);
            } else {
                achievemenVO.setRowno(achievemenVO1.getRowno());
            }
//                retest_times
            if (achievemenVO.getRetestTimes()!=null && achievemenVO.getRetestTimes() != 0) {
                achievemenVO.setExamName(achievemenVO.getExamName() + "(补考" + achievemenVO.getRetestTimes().toString() + ")");
            }
            String reTestKey = achievemenVO.getExamId() + "-" + achievemenVO.getUserId();
            if (!retestTime.containsKey(reTestKey)) {
                Long retestTimes = getRetestTimes(Long.valueOf(achievemenVO.getExamId()), Long.valueOf(achievemenVO.getUserId()));
                if (retestTimes == null) retestTimes = 0L;
                retestTime.put(reTestKey, retestTimes);

            }
            achievemenVO.setRetestTimes(Math.toIntExact(retestTime.get(reTestKey)));
        }
//        disputeStatus
        List<AchievemenListVo> records = data.getRecords();
        if (records != null && records.size() > 0) {
            List<StDisputeComplaint> disputeComplaint = disputeComplaintMapper.selectList(
                new QueryWrapper<StDisputeComplaint>().in("answer_id",
                    records.stream().map(item -> item.getAnswerCardId()).collect(Collectors.toList()))
                    .and(qw -> qw.eq("dispute_status", 0).or().eq("dispute_status", 1)
                    ).eq("del_flag", 0));
            records.forEach(item -> {
                if (disputeComplaint != null && disputeComplaint.stream().filter(item1 -> String.valueOf(item1.getAnswerId()).equals(String.valueOf(item.getAnswerCardId()))).count() > 0) {
                    item.setDisputeStatus(0);
                } else {
                    item.setDisputeStatus(1);
                }
            });
        }
        return data;
    }

    /**
     * 查看证书
     *
     * @param achievemenCertificateId 成绩证书id
     * @return 查看证书
     */
    @Override
    public StAchievemenCertificateVo selectCertificate(long achievemenCertificateId) {
        Map conditionParam = new HashMap();
        conditionParam.put("userId", LoginHelper.getUserId());
        conditionParam.put("achievemenCertificateId", achievemenCertificateId);
        return examMapper.selectCertificate(conditionParam);
    }

    /**
     * 查询所在公司同一次考试的成绩列表
     *
     * @return 查询所在公司同一次考试的成绩列表
     */
    @Override
    public IPage<AchievemenVo> selectGroupAchievemenList(Page page, String examId, String userName, String projectId, String tenantId) {
        Map conditionParam = new HashMap();
        conditionParam.put("examId", examId);
        conditionParam.put("userName", userName);
        if (projectId == null) {
            conditionParam.put("projectId", LoginHelper.getProjectId());
        } else {
            conditionParam.put("projectId", projectId);
        }
//        if (tenantId == null) {
//            conditionParam.put("tenantId", LoginHelper.getTenantId());
//        } else {
//            conditionParam.put("tenantId", tenantId);
//        }
        IPage<AchievemenVo> result = null;
        StExam stExam = stExamService.getById(examId);
        if (CommonDataBaseConst.TRAIN_METHOD.TRAIN_METHOD_3.getCode().equals(stExam.getIsNeedTrain())) {
            //自选考试的情况
            result = examMapper.selectOptionalAchievemenList(page, conditionParam);
        } else {
            //分配考试的情况
            result = examMapper.selectGroupAchievemenList(page, conditionParam);
        }
        return result;
    }

    /**
     * 查看是否可以考试
     *
     * @return 状态
     */
    @Override
    public String checkExamStatus(StExamUserVO stExamUserVO) {
        //用户没有考试
        if (stExamUserVO == null || GyUtils.isNull(stExamUserVO.getExamId())) {
            return CommonConsts.EXAM_NOT_STARTED;
        }
        //判断是否还未到考试时间
        if (LocalDateTime.now().isBefore(stExamUserVO.getStartTime())) {
            return CommonConsts.EXAM_NOT_STARTED;
        }
        //判断是否已经参加考试
        //已经参考
        if (GyUtils.isNotNull(stExamUserVO.getAnswerCardId())) {
            return CommonConsts.EXAM_COMPLETED;
        }
        //判断是否还已超过考试时间
        if (LocalDateTime.now().isAfter(stExamUserVO.getEndTime())) {
            return CommonConsts.EXAM_OVERTIME;
        }
        return CommonConsts.EXAM_STARTE;
    }


    /**
     * 通过考试id查询培训是否完成
     *
     * @return （1，是；0，否）
     */
    public Boolean checkTrainFinishByExamId(String examId, String isSafety) {
        long userId = LoginHelper.getUserId();
        Map conditionParam = new HashMap();
        conditionParam.put("userId", userId);
        conditionParam.put("examId", examId);
//        conditionParam.put("tenantId", LoginHelper.getTenantId());

        //安管课程 单独查询进度
        List<UserCourseProgresVo> userCourseProgresVOS;

        if (String.valueOf(CommonDataBaseConst.YES_OR_NO.YES.getCode()).equals(isSafety)) {

            userCourseProgresVOS = examMapper.checkSafetyTrainFinishByExamId(conditionParam);

        }else {

            userCourseProgresVOS = examMapper.checkTrainFinishByExamId(conditionParam);
        }

        for (UserCourseProgresVo userCourseProgresVO : userCourseProgresVOS) {
            if (userCourseProgresVO.getProgres() < 99) {
                return false;
            }
        }
        return true;
    }

    /**
     * 查看个人考试列表
     *
     * @return 状态
     */
    @Override
    public List<StExamUserVO> getUserExamList(Integer size, Integer current, String startTime, String endTime,String examName) {
        RateLimiterUtils.exec();
        Map conditionParam = new HashMap();
        conditionParam.put("userId", LoginHelper.getUserId());
        conditionParam.put("startTime", startTime);
        conditionParam.put("endTime", endTime);
        conditionParam.put("examName", examName);
//        conditionParam.put("tenentId", LoginHelper.getTenantId());
        List<StExamUserVO> stExamUserVOs = examMapper.getUserExamList(new Page<>(current, size),conditionParam);
        //判断用户考试状态
        for (StExamUserVO stExamUserVO : stExamUserVOs) {
            String examStatus = checkExamStatus(stExamUserVO);
            stExamUserVO.setExamStatus(examStatus);
        }
        return stExamUserVOs;
    }

//    /**
//     * 提交试卷
//     *
//     * @param stAnswerCardVO 答题卡信息
//     * @return 答题卡Id
//     */
//    @Override
//    public Map submitExamPapersApp(StAnswerCardVo stAnswerCardVO) {
//        StAchievemenCertificateVo stAchievemenCertificateVO =  submitExamPapers(stAnswerCardVO);
//        //生成成绩证书并保存
//        return createExamScoresVO(stAnswerCardVO,stAchievemenCertificateVO);
//    }

    /**
     * 开始考试(App)
     *
     * @param stAnswerCardVO 答题卡信息
     * @return 答题卡
     */
    @Override
    public String startExamApp(StAnswerCardVo stAnswerCardVO) {
        stAnswerCardVO.setUserId(LoginHelper.getUserId());
        startExam(stAnswerCardVO);
        StExam stExam = stExamService.getById(stAnswerCardVO.getExamId());
        //启动一个新的线程
        new Thread() {
            @SneakyThrows
            public void run(){
                //根据考试时长开始计时
                Long examLength =stExam.getExamLength();
                TimeUnit.SECONDS.sleep(examLength * 60);
//                TimeUnit.SECONDS.sleep(20);
                StAnswerCard stAnswerCard1 = stAnswerCardService.getById(stAnswerCardVO.getId());
                if(stAnswerCard1 !=null && stAnswerCard1.getAnswerCardStatus() == 0){
                    //如果考试时间结束，未提交试卷自动按照空白卷处理
                    autoCommmit(stAnswerCard1.getExamId(), stAnswerCard1.getUserId());
                }
            }
        }.start();
        return String.valueOf(stAnswerCardVO.getId());
    }

    private Map createExamScoresVO(StAnswerCardVo stAnswerCardVO,StAchievemenCertificateVo stAchievemenCertificateVO) {
        Map map = new HashMap();
        //返回页面显示用
        ExamScoresVO examScoresVO = new ExamScoresVO();
        List<StAnswerCardDetailVo> answerCardDetailVOList = stAnswerCardVO.getAnswerCardDetailVOList();
        if (answerCardDetailVOList == null || answerCardDetailVOList.size() == 0) {
            return null;
        }
        StExam stExam = stExamService.getById(stAnswerCardVO.getExamId());

        //成绩级别
        String achievemenLevel = "";
        double score = Double.valueOf(String.valueOf(stAnswerCardVO.getScore()));
        //查看是否及格
        if (score >= stExam.getPassScore() && stExam.getIsSetLevel().equals(String.valueOf(CommonDataBaseConst.YES_OR_NO.YES.getCode()))) {
            //及格并且设置了成绩级别的情况下
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("exam_id", stAnswerCardVO.getExamId());
            queryWrapper.eq("del_flag", CommonDataBaseConst.YES_OR_NO.NO.getCode());
            List<StScoreLevel> scoreLevelList = stScoreLevelService.list(queryWrapper);
            for (StScoreLevel stScoreLevel : scoreLevelList) {
                if (Double.valueOf(String.valueOf(stScoreLevel.getLowestScore())) < score && score < Double.valueOf(String.valueOf(stScoreLevel.getHighestScore()))) {
                    achievemenLevel = stScoreLevel.getScoreLevelName();
                }
            }
        } else if (score >= stExam.getPassScore()) {
            achievemenLevel = CommonConsts.SCORE_LEVEL_PASS;
        } else {
            achievemenLevel = CommonConsts.SCORE_LEVEL_FAIL;
        }
        //考试成绩页面
        examScoresVO.setTotalScore(stAnswerCardVO.getScore());
        examScoresVO.setTotalRightCount(answerCardDetailVOList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_RIGHT).collect(Collectors.toList()).size());
        examScoresVO.setTotalWrongCount(answerCardDetailVOList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_ERROR).collect(Collectors.toList()).size());
        examScoresVO.setRetestTimes(Integer.valueOf(String.valueOf(stAnswerCardVO.getRetestTimes())));
        //查看考试允许次数
        examScoresVO.setAllowedTimes(Integer.valueOf(String.valueOf(stExam.getAllowedTimes())));
        map.put("StAchievemenCertificateVo", stAchievemenCertificateVO);
        examScoresVO.setAchievemenLevel(achievemenLevel);
        map.put("examScoresVO", examScoresVO);
        //查看排名
        Map conditionParam = new HashMap();
        conditionParam.put("examId", stAnswerCardVO.getExamId());
        conditionParam.put("userId", stAnswerCardVO.getUserId());
        AchievemenVo achievemenVO = accountMapper.getUserGroupRank(conditionParam);
        map.put("rowno", achievemenVO.getRowno());
        map.put("certificateId", stAchievemenCertificateVO.getId());
        map.put("isAnswerAnalysis", stExam.getIsAnswerAnalysis());
        return map;
    }

    /**
     * 查看试卷解析
     *
     * @param answerCardId 答题卡id
     * @return 答题卡
     */
    @Override
    public Map<String, Object> selectExamPaper(String answerCardId) {
        Map<String, Object> retMap = new HashMap();
        ExamScoresVO examScoresVO = new ExamScoresVO();
        if (GyUtils.isNull(answerCardId)) {
            return null;
        }
        StAnswerCardDetailVo stAnswerCardDetailVO = examMapper.selectExamPaper(answerCardId);
        if (stAnswerCardDetailVO == null) {
            return null;
        }
        List<StAnswerCardDetailVo> stQuestionVOList = new ArrayList<>();
//        if (GyUtils.isNull(stAnswerCardDetailVO.getDetailJson())) {
//        stQuestionVOList = examMapper.selectExamPaperNoJson(answerCardId);
//        } else {
        stQuestionVOList = JSONObject.parseArray(stAnswerCardDetailVO.getDetailJson(), StAnswerCardDetailVo.class);
        List<Long> questionIds = stQuestionVOList.stream().map(p->p.getId()).collect(Collectors.toList());
        List<StQuestion> questionList = stQuestionMapper.selectBatchIds(questionIds);
        for (StAnswerCardDetailVo detailVO : stQuestionVOList) {
//            StQuestion stQuestion = stQuestionService.getById(detailVO.getQuestionId());
            Optional<StQuestion> stQuestionOpt =  questionList.stream().filter(p-> p.getId().equals(detailVO.getId())).findFirst();
            if(!stQuestionOpt.isPresent()){
                detailVO.setOptionContent("");
                detailVO.setQuestionContent("此题已被删除");
                detailVO.setAnalysis("");
            }else{
                StQuestion stQuestion = stQuestionOpt.get();
                detailVO.setOptionContent(stQuestion.getOptionContent());
                detailVO.setQuestionContent(stQuestion.getQuestionContent());
                detailVO.setAnalysis(stQuestion.getAnalysis());
            }
        }
//        }
        reRandomOptionContent(stQuestionVOList);
        // 查看答案是否纠纷申诉
//        QueryWrapper<StDisputeComplaintDetail> detailQueryWrapper = new QueryWrapper<>();
//        detailQueryWrapper.eq(TableFieldNameConsts.StDisputeComplaintDetailTable.ANSWER_ID, answerCardId);
//        List<StDisputeComplaintDetail> complaintDetailList = disputeComplaintDetailMapper.selectList(detailQueryWrapper);
//        Map<Long, StDisputeComplaintDetail> complaintDetailMap = complaintDetailList.stream().collect(Collectors.toMap(StDisputeComplaintDetail::getQuestionId, StDisputeComplaintDetail -> StDisputeComplaintDetail, (item1, item2) -> item1));
//        for (StAnswerCardDetailVo vo : stQuestionVOList) {
//            if (complaintDetailMap.get(Long.valueOf(vo.getQuestionId())) != null) {
//                vo.setIsDisputeComplaint(CommonConsts.YES);
//            } else {
//                vo.setIsDisputeComplaint(CommonConsts.NO);
//            }
//        }

//        // 查看考生是否提交申诉
//        String isExamCompaint = CommonConsts.NO;
//        if (complaintDetailList.size() != 0) {
//            if (complaintDetailList.get(0).getDisputeComplaintId() != null) {
//                isExamCompaint = CommonConsts.YES;
//            }
//        }

        //根据题型分组
        Map<Integer, List<StAnswerCardDetailVo>> questionData = stQuestionVOList.stream().collect(Collectors.groupingBy(StAnswerCardDetailVo::getQuestionGenre));
        //单选题分数
        double singleChoiceScore = 0;
        List<StAnswerCardDetailVo> singleChoiceList = questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.SINGLE_CHOICE.getCode());
        if (singleChoiceList != null) {
            singleChoiceScore = singleChoiceList.stream().mapToDouble(StAnswerCardDetailVo::getSingleScore).sum();
            examScoresVO.setSingleChoiceList(singleChoiceList);
            examScoresVO.setSingleChoiceScore(singleChoiceScore);
            int singleChoiceRightCount = singleChoiceList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_RIGHT).collect(Collectors.toList()).size();
            examScoresVO.setSingleChoiceRightCount(singleChoiceRightCount);
            examScoresVO.setSingleChoiceWrongCount(singleChoiceList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_ERROR).collect(Collectors.toList()).size());
        }
        //多选题分数
        double multipleChoiceScore = 0;
        List<StAnswerCardDetailVo> multipleChoiceList = questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.MULTIPLE_CHOICE.getCode());
        if (multipleChoiceList != null) {
            multipleChoiceScore = multipleChoiceList.stream().mapToDouble(StAnswerCardDetailVo::getSingleScore).sum();
            examScoresVO.setMultipleChoiceList(multipleChoiceList);
            DecimalFormat df = new DecimalFormat("0.00");
            examScoresVO.setMultipleChoiceScore(Double.valueOf(df.format(multipleChoiceScore)));
            int multipleChoiceRightCount = multipleChoiceList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_RIGHT).collect(Collectors.toList()).size();
            examScoresVO.setMultipleChoiceRightCount(multipleChoiceRightCount);
            examScoresVO.setMultipleChoiceWrongCount(multipleChoiceList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_ERROR).collect(Collectors.toList()).size());
        }
        //判断题分数
        double judgeScore = 0;
        List<StAnswerCardDetailVo> judgeList = questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.JUDGE.getCode());
        if (judgeList != null) {
            judgeScore = judgeList.stream().mapToDouble(StAnswerCardDetailVo::getSingleScore).sum();
            examScoresVO.setJudgeList(judgeList);
            examScoresVO.setJudgeScore(judgeScore);
            int judgeRightCount = judgeList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_RIGHT).collect(Collectors.toList()).size();
            examScoresVO.setJudgeRightCount(judgeRightCount);
            examScoresVO.setJudgeWrongCount(judgeList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_ERROR).collect(Collectors.toList()).size());
        }

        //填空题分数
        double completionScore = 0;
        List<StAnswerCardDetailVo> completionList = questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.COMPLETION.getCode());
        if (completionList != null) {
            completionScore = completionList.stream().mapToDouble(StAnswerCardDetailVo::getSingleScore).sum();
            examScoresVO.setCompletionList(completionList);
            examScoresVO.setCompletionScore(completionScore);
            int completionRightCount = completionList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_RIGHT).collect(Collectors.toList()).size();
            examScoresVO.setCompletionRightCount(completionRightCount);
            examScoresVO.setCompletionWrongCount(completionList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_ERROR).collect(Collectors.toList()).size());
        }
        //简答题分数
        double shortAnswerScore = 0;
        List<StAnswerCardDetailVo> shortAnswerList = questionData.get(CommonDataBaseConst.QUESTION_GENRE_NAME.SHORTANSWER.getCode());
        if (shortAnswerList != null) {
            shortAnswerScore = shortAnswerList.stream().mapToDouble(StAnswerCardDetailVo::getSingleScore).sum();
            examScoresVO.setShortAnswerList(shortAnswerList);
            examScoresVO.setShortAnswerScore(shortAnswerScore);
            int shortAnswerRightCount = shortAnswerList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_RIGHT).collect(Collectors.toList()).size();
            examScoresVO.setShortAnswerRightCount(shortAnswerRightCount);
            examScoresVO.setShortAnswerWrongCount(shortAnswerList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_ERROR).collect(Collectors.toList()).size());
        }


        StAnswerCard stAnswerCard = stAnswerCardService.getById(answerCardId);
//        StExam stExam = stExamService.getById(stAnswerCard.getExamId());
//        //成绩级别
//        String achievemenLevel = "";
//        double score = Double.valueOf(String.valueOf(stAnswerCard.getScore()));
//        //查看是否及格
//        if (score >= stExam.getPassScore() && stExam.getIsSetLevel().equals(String.valueOf(CommonDataBaseConst.YES_OR_NO.YES.getCode()))) {
//            //及格并且设置了成绩级别的情况下
//            QueryWrapper queryWrapper = new QueryWrapper();
//            queryWrapper.eq("exam_id", stAnswerCard.getExamId());
//            queryWrapper.eq("del_flag", CommonDataBaseConst.YES_OR_NO.NO.getCode());
//            List<StScoreLevel> scoreLevelList = stScoreLevelService.list(queryWrapper);
//            for (StScoreLevel stScoreLevel : scoreLevelList) {
//                if (stScoreLevel.getLowestScore() < score && score < stScoreLevel.getHighestScore()) {
//                    achievemenLevel = stScoreLevel.getScoreLevelName();
//                }
//            }
//        } else if (score >= stExam.getPassScore()) {
//            achievemenLevel = CommonConsts.SCORE_LEVEL_PASS;
//        } else {
//            achievemenLevel = CommonConsts.SCORE_LEVEL_FAIL;
//        }
        //考试成绩页面
        examScoresVO.setTotalScore(stAnswerCard.getScore());
        examScoresVO.setTotalRightCount(stQuestionVOList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_RIGHT).collect(Collectors.toList()).size());
        examScoresVO.setTotalWrongCount(stQuestionVOList.stream().filter(s -> s.getReviewResult() == CommonConsts.REVIEW_RESULT_ERROR).collect(Collectors.toList()).size());
//        examScoresVO.setRetestTimes(Integer.valueOf(String.valueOf(stAnswerCard.getRetestTimes())));
        //查看考试允许次数
//        examScoresVO.setAllowedTimes(Integer.valueOf(String.valueOf(stExam.getAllowedTimes())));
        String achievemenLevel = "";
        if ("1".equals(stAnswerCard.getIsPass())) {
            achievemenLevel = CommonConsts.SCORE_LEVEL_PASS;
        } else {
            achievemenLevel = CommonConsts.SCORE_LEVEL_FAIL;
        }
        examScoresVO.setAchievemenLevel(achievemenLevel);

        examScoresVO.setUserId(String.valueOf(stAnswerCard.getUserId()));

//        Map<String,List<StAnswerCardDetailVO>> questionDataNew = new HashMap<>();

        retMap.put("examScoresVO", examScoresVO);

//        QueryWrapper queryWrapper = new QueryWrapper();
//        queryWrapper.eq("del_flag", CommonDataBaseConst.YES_OR_NO.NO.getCode());
//        queryWrapper.eq("answer_card_id", answerCardId);
//        List<StAchievemenCertificate> stAchievemenCertificateList = stAchievemenCertificateService.list(queryWrapper);
//        if (stAchievemenCertificateList != null && stAchievemenCertificateList.size() > 0) {
//            retMap.put("certificateId", String.valueOf(stAchievemenCertificateList.get(0).getId()));
//        }
//        retMap.put("isExamCompaint", isExamCompaint);
        retMap.put("danxuan", questionData.get(CommonDataBaseConst.CONST_SINGLE_CHOICE));
        retMap.put("duoxuan", questionData.get(CommonDataBaseConst.CONST_MULTIPLE_CHOICE));
        retMap.put("tiankong", questionData.get(CommonDataBaseConst.CONST_COMPLETION));
        retMap.put("panduan", questionData.get(CommonDataBaseConst.CONST_JUDGE));
        retMap.put("jianda", questionData.get(CommonDataBaseConst.SHORT_ANSWER));
        return retMap;
    }

    /**
     * 在list集合中随机变换顺序
     *
     * @param list 取元素的集合
     * @return
     */
    public List<StAnswerCardDetailVo> reRandomOptionContent(List<StAnswerCardDetailVo> list) {
        //随机选项待测试
        for (StAnswerCardDetailVo stAnswerCardDetailVO : list) {
            if (stAnswerCardDetailVO.getQuestionGenre() == CommonDataBaseConst.CONST_SINGLE_CHOICE || stAnswerCardDetailVO.getQuestionGenre() == CommonDataBaseConst.CONST_MULTIPLE_CHOICE) {
                com.alibaba.fastjson.JSONArray jSONArray = com.alibaba.fastjson.JSONArray.parseArray(stAnswerCardDetailVO.getOptionContent());
                com.alibaba.fastjson.JSONArray newjSONArray = new com.alibaba.fastjson.JSONArray();
                if (GyUtils.isNotNull(stAnswerCardDetailVO.getRandomOptionMap())) {
                    Map<String, Integer> randomMap = JSONObject.parseObject(stAnswerCardDetailVO.getRandomOptionMap(), Map.class);
                    for (int i = 0; i < jSONArray.size(); i++) {
                        JSONObject jSONObject = (JSONObject) jSONArray.get(randomMap.get(String.valueOf(i)));
                        jSONObject.put("id", i + 1);
                        newjSONArray.add(jSONObject);
                    }
                    stAnswerCardDetailVO.setOptionContent(newjSONArray.toJSONString());
//                    String rightKey = stAnswerCardDetailVO.getRightKey();
//                    String newRightKey = examManageService.convertRandomRightKey(rightKey,randomMap);
//                    stAnswerCardDetailVO.setRightKey(newRightKey);
                }
            }
        }
        return list;
    }

    public void isTimeout(StAnswerCardVo stAnswerCardVO) {
        // 判断超出处理时间
        Long examId = Long.valueOf(stAnswerCardVO.getExamId());
        StExam stExam = stExamService.getById(examId);
        QueryWrapper<StCompulsionComplaint> queryWrapper = new QueryWrapper<StCompulsionComplaint>();
        queryWrapper.eq(TableFieldNameConsts.StCompulsionComplaintTable.EXAM_ID, examId);
        queryWrapper.eq(TableFieldNameConsts.USER_ID, LoginHelper.getUserId());
        StCompulsionComplaint stCompulsionComplaint = compulsionComplaintService.getOne(queryWrapper);
        if ("1".equals(stExam.getIsCompulsionComplaint())) {
            // 判断时间 是否超出
            String appealExamTime = stExam.getAppealExamTime();
            LocalDateTime competeTime = stCompulsionComplaint.getCompleteTime();
            competeTime = competeTime.plusMinutes(Long.valueOf(appealExamTime));
            //获取当前时间
            LocalDateTime nowTime = LocalDateTime.now();
            if (nowTime.isAfter(competeTime)) {
                new BizException(CommonConsts.VERIFT, "超出考试时间");
            }
        }
    }

    /**
     * 开始考试
     *
     * @param stAnswerCardVO 答题卡
     * @return 答题卡
     */
    @Override
    public String startExam(StAnswerCardVo stAnswerCardVO) {

        StExam stExam = stExamService.getById(stAnswerCardVO.getExamId());
        if (!"1".equals(stExam.getIsCompulsionComplaint())) {
            return null;
        }

        // 判断是否为强制考试
        if (stAnswerCardVO.getDisputeStatus() != null && stAnswerCardVO.getDisputeStatus() == 1 && stAnswerCardVO.getExamDisputed() != null && "0".equals(stAnswerCardVO.getExamDisputed())) {
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("user_id", stAnswerCardVO.getUserId());
            queryWrapper.eq("exam_id", stAnswerCardVO.getExamId());
            queryWrapper.eq("is_latest", CommonConsts.YES);
//            queryWrapper.eq("answer_card_status", CommonConsts.ANSWER_CARD_STATUS_0);
            queryWrapper.eq("dispute_status", 0);
            StAnswerCard stAnswerCardTemp = stAnswerCardService.getOne(queryWrapper);
            if(stAnswerCardTemp!=null){
                stAnswerCardTemp.setDisputeStatus(1L);
                stAnswerCardService.updateById(stAnswerCardTemp);
                return stAnswerCardTemp.getId().toString();
            }
        }

        //插入答题卡表
        StAnswerCard stAnswerCard = new StAnswerCard();
        stAnswerCard.setExamId(stAnswerCardVO.getExamId());
        stAnswerCard.setUserId(stAnswerCardVO.getUserId());
        stAnswerCard.setId(SequenceBean.getSequence());
        stAnswerCard.setDelFlag(CommonDataBaseConst.YES_OR_NO.NO.getCode());
        stAnswerCard.setCreateTime(new Date());
        stAnswerCard.setCreateBy(stAnswerCardVO.getUserId());
        stAnswerCard.setProjectId(LoginHelper.getProjectId());
        stAnswerCard.setDisputeStatus(0L);
        stAnswerCard.setTenantId(LoginHelper.getTenantId());
        stAnswerCard.setTerminal(stAnswerCardVO.getTerminal());
        stAnswerCard.setUpdateTime(new Date());
        //        stAnswerCard.setAnswerCardStatus(CommonConsts.ANS WER_CARD_STATUS_0);
        //2021.11.9 张广旭 修改 开始考试算做提交
        stAnswerCard.setAnswerCardStatus(Long.valueOf(CommonConsts.ANSWER_CARD_STATUS_0));

        if (CommonDataBaseConst.EXAM_TYPE.FORMAL_EXAMINATION.getCode() == stAnswerCardVO.getExamType()) {
            //插入消息队列
            //producerExam.sendQueue(JSONObject.toJSONString(stAnswerCard));
            //不用MQ开始考试
            //把其他次考试是否为最后一次置为否
            StAnswerCard stAnswerCardUpd = new StAnswerCard();
            UpdateWrapper updateWrapper = new UpdateWrapper();
            updateWrapper.eq("user_id", stAnswerCard.getUserId());
            updateWrapper.eq("exam_id", stAnswerCard.getExamId());
            stAnswerCardUpd.setIsLatest(CommonConsts.NO);
            stAnswerCardService.update(stAnswerCardUpd, updateWrapper);
            //查看是否为补考，第几次补考
            Long retestTimes = getRetestTimes(stAnswerCard.getExamId(), stAnswerCard.getUserId());
            if (null == retestTimes) {
                stAnswerCard.setRetestTimes(0L);
            } else {
                stAnswerCard.setRetestTimes(retestTimes.intValue() + 1L);
            }
            stAnswerCard.setScore(0.0);
            stAnswerCard.setIsPass(CommonConsts.NO);
            stAnswerCard.setIsLatest(CommonConsts.YES);
            stAnswerCardService.save(stAnswerCard);
        }
        stAnswerCardVO.setId(stAnswerCard.getId());
        String userExamKey = RedisConsts.USER_EXAM_KEY + stAnswerCardVO.getUserId();
        RedisUtils.setCacheObject(userExamKey, stAnswerCardVO.getExamId(), 60 * 60 * 4L);
        return String.valueOf(stAnswerCardVO.getId());
    }

    /**
     * 保存错误信息
     *
     * @param stUserWrongActVO 错误信息
     * @return 补考次数
     */
    @Override
    public String saveWrongAct(StUserWrongActVo stUserWrongActVO) {
        StUserWrongAct stUserWrongAct = new StUserWrongAct();
        CopyUtils.copyProperties(stUserWrongActVO, stUserWrongAct);
        stUserWrongAct.setId(SequenceBean.getSequence());
        stUserWrongAct.setDelFlag(CommonDataBaseConst.YES_OR_NO.NO.getCode());
        stUserWrongAct.setCreateTime(new Date());
        stUserWrongAct.setCreateBy(LoginHelper.getUserId());
        stUserWrongAct.setUserName(LoginHelper.getUsername());
        stUserWrongAct.setRemindContent(stUserWrongActVO.getWrongAction());
        stUserWrongAct.setProjectId(LoginHelper.getProjectId());
        ThreadPoolUtil.execute(() -> {
            stUserWrongActMapper.insert(stUserWrongAct);
        });
        return CommonConsts.YES;
    }

    /**
     * 缓存试卷
     */
    @Override
    public void cacheExamPapers(StAnswerCardVo stAnswerCardVO) {
        String examId = String.valueOf(stAnswerCardVO.getExamId());
        Long userId = LoginHelper.getProjectId();
        String answercardKey = RedisConsts.ANSWERCARD_KEY + examId + ":" + userId;
        stAnswerCardVO.setProjectId(LoginHelper.getProjectId());
        stAnswerCardVO.setUserId(LoginHelper.getProjectId());
        // 2023年3月8日 避免频繁访问数据库，更新时间直接在开始考试保存了 这里注释了--开始
//        if (stAnswerCardVO.getIsForcedExam() != null && stAnswerCardVO.getIsForcedExam() == CommonDataBaseConst.YES_OR_NO.YES.getCode()) {
        // 更新答题卡时间
//            UpdateWrapper<StAnswerCard> updateWrapper = new UpdateWrapper<>();
//            updateWrapper.eq(TableFieldNameConsts.StAnswerCardTable.EXAM_ID, examId);
//            updateWrapper.eq(TableFieldNameConsts.USER_ID, userId);
//            updateWrapper.eq(TableFieldNameConsts.StAnswerCardTable.IS_LATEST, CommonDataBaseConst.YES_OR_NO.YES.getCode());
//            updateWrapper.set(TableFieldNameConsts.UPDATE_TIME, LocalDateTime.now());
//            stAnswerCardService.update(updateWrapper);

//        }
        // 2023年3月8日 避免频繁访问数据库，更新时间直接在开始考试保存了 这里注释了--结束
        RedisUtils.setCacheObject(answercardKey, JSONObject.toJSONString(stAnswerCardVO), 60 * 60 * 36L);
    }

    @Override
    public List<StExamVo> selectAchievemenListByComplaint() {
//        String domainCode = UserHelpBean.getDomainCode();
        return examMapper.selectAchievemenListByComplaint(LoginHelper.getUserId());
    }

    @Override
    public StAnswerCardVo getCacheExamPapers(String examId, Long userId) {
        String userIdStr = null;
        if (userId != null) {
            userIdStr = userId.toString();
        } else {
            userIdStr = String.valueOf(LoginHelper.getUserId());
        }
        String answercardKey = RedisConsts.ANSWERCARD_KEY + examId + ":" + userIdStr;
        Object obj = RedisUtils.getCacheObject(answercardKey);
        if (obj == null) {
            return null;
        }
        StAnswerCardVo stAnswerCardVO1 = JSONObject.parseObject(obj.toString(), StAnswerCardVo.class);
        return stAnswerCardVO1;
    }

    @Override
    public Integer compulsionExamTimeout(Long examId) {

        StExam stExam = stExamService.getById(examId);
        if (!"1".equals(stExam.getIsCompulsionComplaint())) {
            throw new BizException("这场考试未设置强制申诉");
        }
        //因为没算补考时间，并且超时的也会自动提交，所以能进到这的也就不用判断是否超时了，以下注释 2023.03.15 张广旭
//        // 判断时间 是否超出
//        String canAppealTime = stExam.getCanAppealTime();
//        LocalDateTime endTime = stExam.getEndTime();
//        endTime = endTime.plusMinutes(Long.valueOf(canAppealTime));
//        //获取当前时间
//        LocalDateTime nowTime = LocalDateTime.now();
//        if (nowTime.isAfter(endTime)) {
//            return CommonConsts.TIMEOUT;
//        }
        return CommonConsts.NORMAL;
    }

    @Override
    public void autoCommmit(Long examId, Long userId) {
        StAnswerCardVo stAnswerCardVO = getCacheExamPapers(examId.toString(), userId);
        if(stAnswerCardVO!=null){
            stAnswerCardVO.setSubmitReason("提交缓存试卷");
            submitExamPapers(stAnswerCardVO);
        }
    }
    /**
     * 查询试题 测试
     *
     * @param  conditionParam
     */
    @Override
    @Transactional
    public Map<String, String> startExamTest(Map<String, String> conditionParam){
        Map<String, String> retMap = new HashMap<>();
        StExam stexam = stExamService.getById((Long.parseLong((String) conditionParam.get("examId"))));
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("group_id",stexam.getGroupId());
        queryWrapper.eq("user_id",conditionParam.get("userId"));
        List checkList = stUserGroupService.list(queryWrapper);
        StUserGroup stUserGroup = new StUserGroup();
        if(GyUtils.isNull(checkList)){
            //插入
            stUserGroup.setId(SequenceBean.getSequence());
            stUserGroup.setUserId((Long.parseLong((String) conditionParam.get("userId"))));
            stUserGroup.setGroupId(stexam.getGroupId());
            stUserGroup.setDelFlag(0);
            stUserGroup.setCreateBy(Long.parseLong((String) conditionParam.get("userId")));
            stUserGroup.setCreateTime(new Date());
            stUserGroup.setUpdateBy(Long.parseLong((String) conditionParam.get("userId")));
            stUserGroup.setUpdateTime(new Date());
            retMap.put("userGroupId",String.valueOf(stUserGroup.getId()));
        }else {
            retMap.put("userGroupId","");
        }
        return retMap;
    }
    /**
     * 删除考试测试记录
     *
     * @param  conditionParam
     */
    @Override
    public Map<String, String> delExamTest(Map<String, String> conditionParam){
        Map<String, String> retMap = new HashMap<>();
        //删除组id
        stUserGroupService.removeById(conditionParam.get("userGroupId"));
        stAnswerCardService.removeById(conditionParam.get("answerId"));
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id",conditionParam.get("answerId"));
        stAnswerCardDetailService.remove(queryWrapper);
        return retMap;
    }
    /**
     * 提交手动阅卷
     *
     * @param  stAnswerCardVO
     */
    @Override
    public StAchievemenCertificateVo submitManualReview(StAnswerCardVo stAnswerCardVO){

        stAnswerCardVO.setUserId(stAnswerCardVO.getUserId());
        //处理手动阅卷数据阅卷
        manualReviewPapers(stAnswerCardVO);
        //生成成绩证书并保存
        StAchievemenCertificateVo certificateVo = createCertificate(stAnswerCardVO);
        stAnswerCardVO.setPreId(SequenceBean.getSequence());
        certificateVo.setAnswerCardId(Long.valueOf(stAnswerCardVO.getId()));
        saveCreateCertificate(certificateVo);
        //更新答题卡表
        StAnswerCard stAnswerCard = new StAnswerCard();
        stAnswerCard.setId(stAnswerCardVO.getId());
        stAnswerCard.setScore(stAnswerCardVO.getScore());
        stAnswerCard.setReviewStatus(stAnswerCardVO.getReviewStatus());
        stAnswerCard.setUpdateBy(stAnswerCardVO.getUserId());
        stAnswerCard.setUpdateTime(new Date());
        stAnswerCardService.updateById(stAnswerCard);

        //更新答题卡详细表
        List<StAnswerCardDetailVo> answerCardDetailVOList = stAnswerCardVO.getAnswerCardDetailVOList();
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.eq("answer_card_id",stAnswerCard.getId());
        String detailJson = JSON.toJSONString(answerCardDetailVOList);
        StAnswerCardDetail stAnswerCardDetail = new StAnswerCardDetail();
//        stAnswerCardDetail.setId(SequenceBean.getSequence());
//        stAnswerCardDetail.setAnswerCardId(stAnswerCard.getId());
//        stAnswerCardDetail.setTabTime(LocalDateTime.now());
        stAnswerCardDetail.setUpdateTime(new Date());
        stAnswerCardDetail.setUpdateBy(stAnswerCardVO.getUserId());
        stAnswerCardDetail.setDetailJson(detailJson);
        stAnswerCardDetailService.update(stAnswerCardDetail,updateWrapper);
        return certificateVo;
    }

    /**
     * 每天凌晨1点执行 超过五天未手动阅卷，自动阅卷
     *
     */
    @Override
    public void autoManualReview(){
        logger.info("超过五天未手动阅卷，自动阅卷开始-------------------------");
        //查找超过五天未阅卷的考卷
        QueryWrapper  queryWrapper = new QueryWrapper();
        queryWrapper.eq("del_flag", CommonDataBaseConst.YES_OR_NO.NO.getCode());
        queryWrapper.eq("review_status",CommonDataBaseConst.YES_OR_NO.NO.getCode());
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
        Calendar cal   =   Calendar.getInstance();
        cal.add(Calendar.DATE, -5);
        String fiveDaysAgo=df.format(cal.getTime());
        queryWrapper.le("create_time",fiveDaysAgo);


        List<StAnswerCard> answerCardList = stAnswerCardService.list(queryWrapper);
//        List<StAnswerCardVo> answerCardList = CopyUtils.copyPropertiesList(answerCardList1,StAnswerCardVo.class);
        for (StAnswerCard stAnswerCard : answerCardList) {
            StAnswerCardVo stAnswerCardVO = new StAnswerCardVo();
            CopyUtils.copyPropertiesEtyToVo(stAnswerCard,stAnswerCardVO);
            StAnswerCardDetailVo stAnswerCardDetailVO = examMapper.selectExamPaper(String.valueOf(stAnswerCardVO.getId()));
            List<StAnswerCardDetailVo> stQuestionVOList = new ArrayList<>();
            stQuestionVOList = JSONObject.parseArray(stAnswerCardDetailVO.getDetailJson(), StAnswerCardDetailVo.class);
            stAnswerCardVO.setAnswerCardDetailVOList(stQuestionVOList);
//            reviewExamPapers(stAnswerCardVO);
            stAnswerCardVO.setIsAutoManualReview(CommonDataBaseConst.YES_OR_NO.YES.getCode());
            submitManualReview(stAnswerCardVO);
            logger.info("自动阅卷结束-------------------------stAnswerCardId" + stAnswerCardVO.getId());
        }
        logger.info("超过五天未手动阅卷，自动阅卷结束-------------------------");
    }

    /**
     * 题干与答案的JSON转对象
     *
     * @param json 题干与答案的JSON
     * @return List<JsonDataDTO>
     */
    @Override
    public List<JsonDataDTO> jsonForMat(String json){
        if(StringUtils.isBlank(json)){
            return null;
        }
        JSONArray rightKeyArray = JSONArray.parseArray(json);
        //转实体接收
        return rightKeyArray.toJavaList(JsonDataDTO.class);
    }

    @Override
    public int getQuestionByOptionalCourse(StExam stExam, List<StQuestionVo> questionTotalList) {
        //用户组群id
//        long groupId = stExam.getGroupId();
//        //根据考试分数计算各题型题数
//        double examScore = stExam.getExamScore();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("exam_id", stExam.getId());
        queryWrapper.eq("del_flag", CommonDataBaseConst.YES_OR_NO.NO.getCode());
        //queryWrapper.orderByAsc("question_genre_sn");
        List<StExamQuestionGenre> stExamQuestionGenreList = stExamQuestionGenreService.list(queryWrapper);
        Map queryParam = new HashMap();
        queryParam.put("user_id", LoginHelper.getUserId());
        List<StCourse> stCourseList = examMapper.getOptionalCourseByUserId(queryParam);
        if (stCourseList != null && stCourseList.size() > 0) {
            if (stExam.getAllowExamTrainDuration() > stCourseList.stream().map(e -> e.getCourseDuration()).reduce(0L, Long::sum)) {
                //培训时长不满足考试最小所需时长的情况下
                return CommonConsts.CHECK_QUESTION_EXAM_NE1;
            }
        } else {
            return CommonConsts.CHECK_QUESTION_EXAM_NE1;
        }
        int courseCnt = stCourseList.size();
        //分配各题型题数
        //查询用户组对应课程，课程对应的试题
        for (StExamQuestionGenre stExamQuestionGenre : stExamQuestionGenreList) {
            //该题型需要试题数总数
            int questionCountTol = stExamQuestionGenre.getQuestionGenreCount();
            //需要试题数除以课程
            int questionCount = questionCountTol / courseCnt;
            //除以课程后余题
            int remQuestionCount = questionCountTol % courseCnt;
            List<StQuestionVo> questionList = new ArrayList<>();
            List<StQuestionVo> questionGrpList = null;
            Map conditionParam = new HashMap();
            conditionParam.put("questionGenre", stExamQuestionGenre.getQuestionGenreId());
//            conditionParam.put("domainCode", stExam.getDomainCode());
            questionGrpList = examMapper.createQuestionByExam(conditionParam);
            for (int i = 0; i < stCourseList.size(); i++) {
                int realQuestionCount;
                if (i == 0) {
                    realQuestionCount = questionCount + remQuestionCount;
                } else {
                    realQuestionCount = questionCount;
                }
                String courseId = String.valueOf(stCourseList.get(i).getId());
                List<StQuestionVo> questionCouList = null;
                questionCouList = questionGrpList.stream().filter(s->s.getCourseId().equals(courseId)).collect(Collectors.toList());
                //随机抓取对应试题数
                questionList.addAll(getRandomList(questionCouList, realQuestionCount));
            }
            if (questionList.size() < questionCountTol) {
                //如果题数小于设置的题数
                return CommonConsts.CHECK_QUESTION_EXAM_NE2;
            }
            if (questionTotalList != null) {
                questionTotalList.addAll(questionList);
            }
        }
        randomOptionContent(questionTotalList);
        return CommonConsts.CHECK_QUESTION_EXAM_1;
    }


    /**
     * 在list集合中随机变换顺序
     *
     * @param list  取元素的集合
     * @return
     */
    public  List<StQuestionVo> randomOptionContent(List<StQuestionVo> list) {
        if(list == null){
            return null;
        }
        //随机选项待测试
        for(StQuestionVo stQuestionVO : list){
            if(stQuestionVO.getQuestionGenre()==CommonDataBaseConst.CONST_SINGLE_CHOICE || stQuestionVO.getQuestionGenre() == CommonDataBaseConst.CONST_MULTIPLE_CHOICE){
                com.alibaba.fastjson.JSONArray jSONArray = com.alibaba.fastjson.JSONArray.parseArray( stQuestionVO.getOptionContent());
                com.alibaba.fastjson.JSONArray newjSONArray = new com.alibaba.fastjson.JSONArray();
                int count = jSONArray.size();
                Map<String,Integer> randomMap = generateRandomMap(count,count);
                for(int i=0;i<jSONArray.size(); i++){
                    JSONObject jSONObject = (JSONObject) jSONArray.get(randomMap.get(String.valueOf(i)));
                    jSONObject.put("id",i+1);
                    newjSONArray.add(jSONObject);
                }
                stQuestionVO.setOptionContent(newjSONArray.toJSONString());
                String rightKey = stQuestionVO.getRightKey();
                //随机选项后转换答案
                String newRightKey = convertRandomRightKey(rightKey,randomMap);
                stQuestionVO.setRightKey(newRightKey);
                stQuestionVO.setRandomOptionMap(JSONObject.toJSONString(randomMap));
            }
            //答案加密
//            try {
//                //案例题只加密部分数据
//                if(stQuestionVO.getQuestionGenre() == CommonDataBaseConst.CASE){
//                    String returnRightKey = null;
//                    //将字符串转成对象
//                    List<JsonDataDTO> jsonDataDTOS = examService.jsonForMat(stQuestionVO.getRightKey());
//                    for (JsonDataDTO jsonDataDTO : jsonDataDTOS) {
//                        //设置答案为密文
//                        jsonDataDTO.setAnswer(Base64Util.encryptBASE64(jsonDataDTO.getAnswer().getBytes()));
//                    }
//                    if(jsonDataDTOS != null && !jsonDataDTOS.isEmpty()){
//                        //将对象转成json字符串并且排序
//                        returnRightKey = JSON.toJSONString(jsonDataDTOS, SerializerFeature.SortField, SerializerFeature.WriteMapNullValue);
//                    }
//                    stQuestionVO.setRightKey(returnRightKey);
//                }else {
//                    stQuestionVO.setRightKey(Base64Util.encryptBASE64(stQuestionVO.getRightKey().getBytes()));
//                }
//            }catch (Exception e){
//                throw new BizException(e.getMessage());
//            }
        }
        return list;
    }

    public String convertRandomRightKey(String rightKey,Map<String,Integer> randomMap){
        String newRightKey = "";
        for(String rightKeySingle : rightKey.split(",")){
            for(String key :randomMap.keySet()){
                String oldRightKey = String.valueOf(randomMap.get(key)+1);
                if(oldRightKey.equals(rightKeySingle)){
                    rightKeySingle = rightKeySingle.replace(oldRightKey,String.valueOf(Integer.parseInt(key)+1));
                    newRightKey = newRightKey +","+rightKeySingle;
                    break;
                }
            }
        }
        return newRightKey.replaceFirst(",","");
    }

    public Map<String,Integer> generateRandomMap(int len,int max){
        Map<String,Integer> randomMap = new HashMap();
        int[] arr = new int[len];
        for(int i=0;i<arr.length;i++){
            int num;
            do {
                num = (int)(Math.random()*(max));

            } while (randomMap.containsKey(String.valueOf(num)));
            randomMap.put(String.valueOf(num),i);
        }
        return randomMap;
    }

    /**
     * 案例题rightKey解密
     *
     * @param rightKey 初始json
     * @return 对象字符串
     */
    public String caseDecryBase64(String rightKey) {
        return rightKey;
//        try {
//            List<JsonDataDTO> jsonDataDTOS = jsonForMat(rightKey);
//            for (JsonDataDTO jsonDataDTO : jsonDataDTOS) {
//                jsonDataDTO.setAnswer(new String(Base64Util.decryBASE64(jsonDataDTO.getAnswer())));
//            }
//            return JSON.toJSONString(jsonDataDTOS, SerializerFeature.SortField, SerializerFeature.WriteMapNullValue);
//        }catch (Exception e){
//            throw new BizException(e.getMessage());
//        }
    }

    /**
     * 在list集合中随机取出指定数量的元素
     *
     * @param list  取元素的集合
     * @param count 个数
     * @return
     */
    public static List getRandomList(List list, int count) {
        List olist = new ArrayList<>();
        if (list.size() <= count) {
            return list;
        } else {
            Random random = new Random();
            for (int i = 0; i < count; i++) {
                int intRandom = random.nextInt(list.size() - 1);
                olist.add(list.get(intRandom));
                list.remove(list.get(intRandom));
            }
            return olist;
        }
    }

    /**
     * 成绩详情
     *
     * @param answerCardId 答题卡id
     * @return 答题卡对象
     */
    @Override
    public StAnswerCardVo selectAchievemenDetail(String answerCardId) {
        return stAnswerCardService.queryById(answerCardId);
    }
}

