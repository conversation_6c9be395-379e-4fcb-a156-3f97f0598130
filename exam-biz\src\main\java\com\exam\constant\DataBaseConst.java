package com.exam.constant;

import java.util.LinkedHashMap;

/**
 * 数据库对应的状态码常量类
 */
public class DataBaseConst {

    /**
     *  是否状态码
     *
     * <AUTHOR>
     */
    public enum YES_OR_NO {
        NO(0, "否"),
        YES(1, "是");
        private int code;
        private String mess;

        YES_OR_NO(int code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public int getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (YES_OR_NO as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }


    /**
     * user表中user_status的值
     *
     * <AUTHOR>
     */
    public enum USER_STATUS {
        FROZEN(0, "冻结"),
        NORMAL(1, "正常"),
        QUIT(2, "离职");
        private int code;
        private String mess;

        USER_STATUS(int code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public int getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (USER_STATUS as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 公司表中的公司级别
     *
     * <AUTHOR>
     */
    public enum COMPANY_LEVEL {
        BUREAU_LEVEL(0, "局级"),
        DIVISION_LEVEL(1, "处级"),
        BRANCH_COMPANY(2, "分公司"),
        OTHERS(3, "其他");
        private int code;
        private String mess;

        COMPANY_LEVEL(int code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public int getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (COMPANY_LEVEL as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 人员性质
     *
     * <AUTHOR>
     */
    public enum NATURE_PERSONNEL {
        REGULAR_EMPLOYEE(0, "正式员工"),
        HUMAN_AGENCY(1, "人事代理"),
        TEMPORARY_EMPLOYEE(2, "临时员工");
        private int code;
        private String mess;

        NATURE_PERSONNEL(int code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public int getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (NATURE_PERSONNEL as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 二维码长宽
     *
     * <AUTHOR>
     */
    public enum QRCODE_LW {
        COVER_LW(100, "封面二维码长宽"),
        HEADER_LW(30, "页眉二维码长宽");
        private int code;
        private String mess;

        QRCODE_LW(int code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public int getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (QRCODE_LW as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }

    /**
     * 页眉相关参数
     *
     * <AUTHOR>
     */
    public enum PLACEHOLDER_PARAMETER {
        QR_PLACEHOLDER("{QRCode}", "二维码占位符"),
        FONTSIZE("10", "字体大小"),
        FONTFAMILY("仿宋_GB2312", "页眉字体");
        private String code;
        private String mess;

        PLACEHOLDER_PARAMETER(String code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public String getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<String, String> getMap() {
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            for (PLACEHOLDER_PARAMETER as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }
    /**
     * 用户文件类型
     *
     * <AUTHOR>
     */
    public enum USER_FILE_TYPE {
        IDCARD_FRONT(0, "身份证正面"),
        IDCARD_REVERSE(1, "身份证反面");
        private int code;
        private String mess;

        USER_FILE_TYPE(int code, String mess) {
            this.code = code;
            this.mess = mess;
        }

        public int getCode() {
            return code;
        }

        public String getMess() {
            return mess;
        }

        public static LinkedHashMap<Integer, String> getMap() {
            LinkedHashMap<Integer, String> map = new LinkedHashMap<>();
            for (USER_FILE_TYPE as : values()) {
                map.put(as.getCode(), as.getMess());
            }
            return map;
        }
    }
}
