package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
    * 铁路局-企业管理-消息
    */
@ApiModel(description="铁路局-企业管理-消息")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "railway_message")
public class RailwayMessage {
    /**
     * 消息Id
     */
    @TableId(value = "rm_id")
    @ApiModelProperty(value="消息Id")
    private Long rmId;

    /**
     * 消息标题
     */
    @TableField(value = "rm_title")
    @ApiModelProperty(value="消息标题")
    private String rmTitle;

    /**
     * 消息内容
     */
    @TableField(value = "rm_content")
    @ApiModelProperty(value="消息内容")
    private String rmContent;

    /**
     * 状态1已读｜0未读
     */
    @TableField(value = "rm_status")
    @ApiModelProperty(value="状态1已读｜0未读")
    private Boolean rmStatus;

    /**
     * 已读时间
     */
    @TableField(value = "rm_read")
    @ApiModelProperty(value="已读时间")
    private LocalDateTime rmRead;

    /**
     * 发送用户
     */
    @TableField(value = "rm_send_user")
    @ApiModelProperty(value="发送用户")
    private Long rmSendUser;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 删除标识(0:未删除,其他删除)
     */
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer delFlag;
}
