package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 岗位属性业务对象 st_cheat_pictrue
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StCheatPictrueBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 考试id
     */
    @NotNull(message = "考试id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long examId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 图片路径
     */
    @NotBlank(message = "图片路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileUrl;

    /**
     * 错误信息
     */
    @NotBlank(message = "错误信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String msg;

    /**
     * 接口返回信息
     */
    @NotBlank(message = "接口返回信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String response;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date createTime;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createBy;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateBy;

    /**
     * 领域
     */
    @NotBlank(message = "领域不能为空", groups = { AddGroup.class, EditGroup.class })
    private String domainCode;

    /**
     * 所属单位
     */
    @NotBlank(message = "所属单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;


}
