package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StJobAttrBo;
import com.exam.domain.vo.StJobAttrVo;
import com.exam.service.IStJobAttrService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 岗位属性
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/jobAttr")
public class StJobAttrController extends BaseController {

    private final IStJobAttrService iStJobAttrService;

    /**
     * 查询岗位属性列表
     */
    @SaCheckPermission("exam:jobAttr:list")
    @GetMapping("/list")
    public TableDataInfo<StJobAttrVo> list(StJobAttrBo bo, PageQuery pageQuery) {
        return iStJobAttrService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出岗位属性列表
     */
    @SaCheckPermission("exam:jobAttr:export")
    @Log(title = "岗位属性", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StJobAttrBo bo, HttpServletResponse response) {
        List<StJobAttrVo> list = iStJobAttrService.queryList(bo);
        ExcelUtil.exportExcel(list, "岗位属性", StJobAttrVo.class, response);
    }

    /**
     * 获取岗位属性详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:jobAttr:query")
    @GetMapping("/{id}")
    public R<StJobAttrVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStJobAttrService.queryById(id));
    }

    /**
     * 新增岗位属性
     */
    @SaCheckPermission("exam:jobAttr:add")
    @Log(title = "岗位属性", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StJobAttrBo bo) {
        return toAjax(iStJobAttrService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改岗位属性
     */
    @SaCheckPermission("exam:jobAttr:edit")
    @Log(title = "岗位属性", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StJobAttrBo bo) {
        return toAjax(iStJobAttrService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除岗位属性
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:jobAttr:remove")
    @Log(title = "岗位属性", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStJobAttrService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
