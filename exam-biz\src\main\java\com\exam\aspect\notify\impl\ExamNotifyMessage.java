package com.exam.aspect.notify.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.exam.aspect.notify.NotifyMessageObject;
import com.exam.constant.CommonConsts;
import com.exam.constant.CommonDataBaseConst;
import com.exam.constant.TableFieldNameConsts;
import com.exam.domain.StExam;
import com.exam.domain.StMessageUser;
import com.exam.domain.StMessageUserShield;
import com.exam.domain.StUserGroup;
import com.exam.domain.vo.StMessageVo;
import com.exam.service.IStMessageService;
import com.exam.service.IStMessageUserService;
import com.exam.service.IStMessageUserShieldService;
import com.exam.service.IStUserGroupService;
import com.exam.utils.SequenceBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
public class ExamNotifyMessage extends NotifyMessageObject {

    @Autowired
    private IStMessageService stMessageService;
    @Autowired
    private IStMessageUserService stMessageUserService;
    @Autowired
    private IStUserGroupService stUserGroupService;
    @Autowired
    private IStMessageUserShieldService iStMessageUserShieldService;

    @Override
    public void notifyMessage(int msgType, int msgDetailType, Object... args){


        StExam stExam = null;
        if(args.length != 0 && args[0] instanceof StExam) {
            stExam = (StExam) args[0];
        }
        StMessageVo vo = stMessageService.getByMsgAndDetailType(msgType, msgDetailType, stExam.getTenantId());

        List<StUserGroup> stUserGroupList =  stUserGroupService.getUserGroup(stExam.getGroupId().toString());
        for(StUserGroup stUserGroup : stUserGroupList) {
            QueryWrapper<StMessageUserShield> queryWrapper = new QueryWrapper<StMessageUserShield>();
            queryWrapper.eq(TableFieldNameConsts.USER_ID, stUserGroup.getUserId());
            queryWrapper.eq(TableFieldNameConsts.TENANT_ID, stExam.getTenantId());
            StMessageUserShield stMessageUserShield = iStMessageUserShieldService.getBaseMapper().selectOne(queryWrapper);

            if(stMessageUserShield == null || CommonConsts.NO.equals(stMessageUserShield.getIsShield())) {
                StMessageUser stMessageUser = new StMessageUser();
//                EntityUtils.setInsertInfo(stMessageUser);
                stMessageUser.setDelFlag(CommonDataBaseConst.YES_OR_NO.NO.getCode());
                stMessageUser.setId(SequenceBean.getSequence());
                stMessageUser.setMainTableId(stExam.getId());
                stMessageUser.setPushTime(LocalDateTime.now());
                stMessageUser.setStatus(Integer.valueOf(CommonDataBaseConst.MSG_READ_STATUS.UNREAD.getCode()));
                stMessageUser.setUserInfoId(stUserGroup.getUserId());
                stMessageUser.setMessageId(vo.getId());
                stMessageUser.setMsgDetailType(msgDetailType);
                stMessageUser.setTenantId(stExam.getTenantId());
                stMessageUserService.save(stMessageUser);
            }
        }
    }

}
