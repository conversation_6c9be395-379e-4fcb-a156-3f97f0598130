package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 【请填写功能名称】业务对象 st_user
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StUserBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 照片
     */
    @NotBlank(message = "照片不能为空", groups = { AddGroup.class, EditGroup.class })
    private String profilePicture;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date birthDate;

    /**
     * 籍贯
     */
    @NotBlank(message = "籍贯不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nativePlace;

    /**
     * 职称
     */
    @NotNull(message = "职称不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long title;

    /**
     * 政治面貌
     */
    @NotNull(message = "政治面貌不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer policitalStatus;

    /**
     * 毕业学校
     */
    @NotBlank(message = "毕业学校不能为空", groups = { AddGroup.class, EditGroup.class })
    private String graduationSchool;

    /**
     * 毕业时间
     */
    @NotNull(message = "毕业时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date graduationTime;

    /**
     * 专业
     */
    @NotBlank(message = "专业不能为空", groups = { AddGroup.class, EditGroup.class })
    private String major;

    /**
     * 工作经历
     */
    @NotBlank(message = "工作经历不能为空", groups = { AddGroup.class, EditGroup.class })
    private String workExperience;

    /**
     * 培训经历
     */
    @NotBlank(message = "培训经历不能为空", groups = { AddGroup.class, EditGroup.class })
    private String trainExperience;

    /**
     * 个人业绩
     */
    @NotBlank(message = "个人业绩不能为空", groups = { AddGroup.class, EditGroup.class })
    private String achievement;

    /**
     * 获奖情况
     */
    @NotBlank(message = "获奖情况不能为空", groups = { AddGroup.class, EditGroup.class })
    private String award;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date createTime;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createBy;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateBy;


}
