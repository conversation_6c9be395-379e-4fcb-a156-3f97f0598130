package com.exam.domain;

import com.exam.common.core.domain.BaseEntity;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 身份证图片
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StUserIdentityPic extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 用户
     */
    private Long userId;

    /**
     * 文件路径
     */
    private String filePath;


    /**
     * 图片类型  （0：身份证正面， 1：身份证反面）
     */
    private Integer type;


    /**
     * 所属项目
     */
    private Long projectId;


}
