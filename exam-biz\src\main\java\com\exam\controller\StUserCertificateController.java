package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StUserCertificateBo;
import com.exam.domain.vo.StUserCertificateVo;
import com.exam.service.IStUserCertificateService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 用户证件
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/userCertificate")
public class StUserCertificateController extends BaseController {

    private final IStUserCertificateService iStUserCertificateService;

    /**
     * 查询用户证件列表
     */
    @SaCheckPermission("exam:userCertificate:list")
    @GetMapping("/list")
    public TableDataInfo<StUserCertificateVo> list(StUserCertificateBo bo, PageQuery pageQuery) {
        return iStUserCertificateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户证件列表
     */
    @SaCheckPermission("exam:userCertificate:export")
    @Log(title = "用户证件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StUserCertificateBo bo, HttpServletResponse response) {
        List<StUserCertificateVo> list = iStUserCertificateService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户证件", StUserCertificateVo.class, response);
    }

    /**
     * 获取用户证件详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:userCertificate:query")
    @GetMapping("/{id}")
    public R<StUserCertificateVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStUserCertificateService.queryById(id));
    }

    /**
     * 新增用户证件
     */
    @SaCheckPermission("exam:userCertificate:add")
    @Log(title = "用户证件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StUserCertificateBo bo) {
        return toAjax(iStUserCertificateService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改用户证件
     */
    @SaCheckPermission("exam:userCertificate:edit")
    @Log(title = "用户证件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StUserCertificateBo bo) {
        return toAjax(iStUserCertificateService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户证件
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:userCertificate:remove")
    @Log(title = "用户证件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStUserCertificateService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
