package com.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.domain.RailwayUserInfo;
import com.exam.domain.RailwayUserPageDto;
import com.exam.domain.vo.SignUpVo;
import com.exam.domain.vo.UserInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface RailwayUserInfoMapper extends BaseMapper<RailwayUserInfo> {

    IPage<UserInfoVO> queryUserPage(@Param("page") Page page, @Param("dto") RailwayUserPageDto dto);

}
