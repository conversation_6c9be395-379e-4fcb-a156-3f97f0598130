package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 考试业务对象 st_exam
 *
 * <AUTHOR>
 * @date 2023-11-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StExamBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     *
     */
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String examName;

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long groupId;

    /**
     * 课程类别id集合
     */
    @NotBlank(message = "课程类别id集合不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionTypeIds;

    /**
     * 试卷类型（1：随机组卷，2：自定义组卷）
     */
    @NotBlank(message = "试卷类型（1：随机组卷，2：自定义组卷）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paperType;

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long examLength;

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal examScore;

    /**
     * 及格分数
     */
    @NotNull(message = "及格分数不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal passScore;

    /**
     * 允许补考次数
     */
    @NotNull(message = "允许补考次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long allowedTimes;

    /**
     * 是否需要培训(1.是；0.否)
     */
    @NotBlank(message = "是否需要培训(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isNeedTrain;

    /**
     * 是否设置等级(1.是；0.否)
     */
    @NotBlank(message = "是否设置等级(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isSetLevel;

    /**
     * 是否查看答案解析(1.是；0.否)
     */
    @NotBlank(message = "是否查看答案解析(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isAnswerAnalysis;

    /**
     * 是否开启纠纷申诉(1.是；0.否)
     */
    @NotBlank(message = "是否开启纠纷申诉(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isDisputeComplaint;

    /**
     * 是否开启强制交卷申诉（1.是；0：否）
     */
    @NotBlank(message = "是否开启强制交卷申诉（1.是；0：否）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isCompulsionComplaint;

    /**
     * 可申诉时间
     */
    @NotBlank(message = "可申诉时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String canAppealTime;

    /**
     * 申诉处理时间
     */
    @NotBlank(message = "申诉处理时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appealHandleTime;

    /**
     * 申诉继续考试时间
     */
    @NotBlank(message = "申诉继续考试时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appealExamTime;

    /**
     * 是否启用人脸识别(1.是；0.否)
     */
    @NotBlank(message = "是否启用人脸识别(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isFaceRecognition;

    /**
     * 是否视频监控(1.是；0.否)
     */
    @NotBlank(message = "是否视频监控(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isVideoSurveillance;

    /**
     * 是否开启客户端考试(1.是；0.否)
     */
    @NotBlank(message = "是否开启客户端考试(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isClient;

    /**
     * 是否多屏禁用(1.是；0.否)
     */
    @NotBlank(message = "是否多屏禁用(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isMultiScreen;

    /**
     * 是否动态监控(1.是；0.否)
     */
    @NotBlank(message = "是否动态监控(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isDynamicSupervisory;

    /**
     * 是否强制全屏(1.是；0.否)
     */
    @NotBlank(message = "是否强制全屏(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isForceFullScreen;

    /**
     * 是否切换页面(1.是；0.否)
     */
    @NotBlank(message = "是否切换页面(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isSwitchPages;

    /**
     * 作弊动作提醒次数
     */
    @NotBlank(message = "作弊动作提醒次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cheatCnt;

    /**
     * 多个人脸提醒次数
     */
    @NotBlank(message = "多个人脸提醒次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private String morePeopleCnt;

    /**
     * 无人脸提醒次数
     */
    @NotBlank(message = "无人脸提醒次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private String noOneCnt;

    /**
     * 非本人提醒次数
     */
    @NotBlank(message = "非本人提醒次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ordersCnt;

    /**
     * 抓拍次数
     */
    @NotBlank(message = "抓拍次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private String captureCnt;

    /**
     * 识别错误数阈值
     */
    @NotBlank(message = "识别错误数阈值不能为空", groups = { AddGroup.class, EditGroup.class })
    private String recognitionErrorCnt;

    /**
     * 切换页面数阈值
     */
    @NotBlank(message = "切换页面数阈值不能为空", groups = { AddGroup.class, EditGroup.class })
    private String switchPagesCnt;

    /**
     * 补考开始时间
     */
    @NotNull(message = "补考开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date resitStartTime;

    /**
     * 补考结束时间
     */
    @NotNull(message = "补考结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date resitEndTime;

    /**
     * 允许考试的最少培训时间（秒）
     */
    @NotNull(message = "允许考试的最少培训时间（秒）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long allowExamTrainDuration;

    /**
     * 是否手动阅卷(1.是；0.否)
     */
    @NotBlank(message = "是否手动阅卷(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isManualReview;

    /**
     * 是否安管人员(1.是；0.否)
     */
    @NotNull(message = "是否安管人员(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isSafety;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;

    /**
     * 所属项目
     */
    @NotNull(message = "所属项目不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;


}
