package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.vo.StAnswerCardVo;
import com.exam.domain.bo.StAnswerCardBo;
import com.exam.service.IStAnswerCardService;
import com.exam.common.core.page.TableDataInfo;

/**
 * 答题卡
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/answerCard")
public class StAnswerCardController extends BaseController {

    private final IStAnswerCardService iStAnswerCardService;

    /**
     * 查询答题卡列表
     */
    @SaCheckPermission("exam:answerCard:list")
    @GetMapping("/list")
    public TableDataInfo<StAnswerCardVo> list(StAnswerCardBo bo, PageQuery pageQuery) {
        return iStAnswerCardService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出答题卡列表
     */
    @SaCheckPermission("exam:answerCard:export")
    @Log(title = "答题卡", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StAnswerCardBo bo, HttpServletResponse response) {
        List<StAnswerCardVo> list = iStAnswerCardService.queryList(bo);
        ExcelUtil.exportExcel(list, "答题卡", StAnswerCardVo.class, response);
    }

    /**
     * 获取答题卡详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:answerCard:query")
    @GetMapping("/{id}")
    public R<StAnswerCardVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable String id) {
        return R.ok(iStAnswerCardService.queryById(id));
    }

    /**
     * 新增答题卡
     */
    @SaCheckPermission("exam:answerCard:add")
    @Log(title = "答题卡", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StAnswerCardBo bo) {
        return toAjax(iStAnswerCardService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改答题卡
     */
    @SaCheckPermission("exam:answerCard:edit")
    @Log(title = "答题卡", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StAnswerCardBo bo) {
        return toAjax(iStAnswerCardService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除答题卡
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:answerCard:remove")
    @Log(title = "答题卡", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable String[] ids) {
        return toAjax(iStAnswerCardService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
