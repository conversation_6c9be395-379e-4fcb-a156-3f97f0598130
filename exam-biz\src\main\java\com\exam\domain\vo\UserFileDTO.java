package com.exam.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "用户文件对象")
public class UserFileDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 身份证正面照片路径
     */
    @ApiModelProperty(value = "身份证正面照片路径")
    private String frontPath;
    private String relativeFrontPath;

    /**
     * 身份证反面照片路径
     */
    @ApiModelProperty(value = "身份证反面照片路径")
    private String reversePath;
    private String relativeReversePath;

    /**
     * 需要进行比对照片的相对路径
     */
    @ApiModelProperty(value = "需要进行比对照片的相对路径")
    private String photoPath;

    /**
     * base64文件
     */
    @ApiModelProperty(value = "base64文件")
    private String  baseFile;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;


    /**
     * 来自哪个系统
     */
    @ApiModelProperty(value = "来自哪个系统")
    private String app;

    /**
     * 时间戳
     */
    @ApiModelProperty(value = "时间戳")
    private String appTime;
}
