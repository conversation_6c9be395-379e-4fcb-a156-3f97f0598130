package com.exam.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 【请填写功能名称】对象 st_score_level
 *
 * <AUTHOR>
 * @date 2023-11-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_score_level")
public class StScoreLevel extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 考试id
     */
    private Long examId;
    /**
     * 成绩等级名称
     */
    private String scoreLevelName;
    /**
     * 区间最高分
     */
    private Double highestScore;
    /**
     * 区间最低分
     */
    private Double lowestScore;
    /**
     * 租户id
     */
    private Long tenantId;

}
