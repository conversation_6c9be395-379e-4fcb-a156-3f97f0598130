package com.exam.framework.service.impl;

import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.exam.framework.service.AsyncService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("AsyncService2")
public class AsyncServiceImpl implements AsyncService {

    @Async
    @Override
    public <T, R> CompletableFuture<R> async(Function<T, R> fun, T t) {
        R r = fun.apply(t);
        return CompletableFuture.completedFuture(r);
    }

}
