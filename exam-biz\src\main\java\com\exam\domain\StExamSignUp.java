package com.exam.domain;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 考试报名详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Getter
@Setter
@TableName("st_exam_sign_up")
@ApiModel(value = "StExamSignUp对象", description = "考试报名详情表")
public class StExamSignUp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("考试id")
    private Long examId;

    @ApiModelProperty("是否完成（0：未完成；1：完成；）")
    private Integer completeFlag;

    @ApiModelProperty("报名结束时间")
    private LocalDateTime completeTime;

    @ApiModelProperty("租户id")
    private Long tenantId;

    private Long delFlag;

    @ApiModelProperty("创建人")
    private Long createBy;

    @ApiModelProperty("修改人")
    private Long updateBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;


}
