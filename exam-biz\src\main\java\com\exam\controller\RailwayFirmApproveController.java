package com.exam.controller;

import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.enums.BusinessType;
import com.exam.domain.RailwayFirmApproveDto;
import com.exam.service.RailwayFirmApproveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 企业审批管理
 *
 * @ClassName: railwayFirmApprove
 * @Description: 企业审批管理
 * @Author: Xmj
 * @Date: 2025/5/20 17:22
 * @Version: 1.0
 */
@RequestMapping("/railwayFirmApprove")
@RestController
@Api(tags = "企业审批管理")
public class RailwayFirmApproveController extends BaseController {

    @Autowired
    private RailwayFirmApproveService railwayFirmApproveService;




    @Log(title = "企业审批管理", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "企业审批")
    @PostMapping(value = "/approve")
    public R<Void> approve(@Validated(AddGroup.class) @RequestBody RailwayFirmApproveDto firmApproveDto) {
        railwayFirmApproveService.approve(firmApproveDto);
        return R.ok();
    }


}
