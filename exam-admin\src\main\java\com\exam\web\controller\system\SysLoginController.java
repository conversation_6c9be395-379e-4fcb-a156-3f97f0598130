package com.exam.web.controller.system;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.exam.common.annotation.Anonymous;
import com.exam.common.constant.Constants;
import com.exam.common.core.domain.R;
import com.exam.common.core.domain.entity.SysMenu;
import com.exam.common.core.domain.entity.SysUser;
import com.exam.common.core.domain.model.LoginBody;
import com.exam.common.core.domain.model.LoginUser;
import com.exam.common.helper.LoginHelper;
import com.exam.common.utils.StreamUtils;
import com.exam.common.utils.redis.RedisUtils;
import com.exam.domain.RailwayFirm;
import com.exam.service.RailwayFirmService;
import com.exam.system.domain.vo.RouterVo;
import com.exam.system.domain.vo.LoginUserInfoVo;
import com.exam.system.service.ISysMenuService;
import com.exam.system.service.ISysUserEntityService;
import com.exam.system.service.ISysUserService;
import com.exam.system.service.SysLoginService;
import com.exam.system.service.SysPermissionService;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 登录验证
 *
 * <AUTHOR> Li
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
public class SysLoginController {

    private final SysLoginService loginService;
    private final ISysMenuService menuService;
    private final ISysUserService userService;
    private final ISysUserEntityService userEntityService;
    private final SysPermissionService permissionService;
    private final RailwayFirmService railwayFirmService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @Anonymous
    @PostMapping("/login")
    public R<Map<String, Object>> login(@Validated @RequestBody LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 个人考试登录
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @Anonymous
    @PostMapping("/exam/login")
    public R<Map<String, Object>> examlogin(@Validated @RequestBody LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>(1);
        // 生成令牌
        String token = loginService.examlogin(loginBody.getUsername(), loginBody.getPassword());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 企业用户登录
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @Anonymous
    @PostMapping("/tenant/login")
    public R<Map<String, Object>> tenantLogin(@Validated @RequestBody LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.tenantLogin(loginBody.getUsername(), loginBody.getPassword());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 国铁登录
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @Anonymous
    @PostMapping("/admin/login")
    public R<Map<String, Object>> adminLogin(@Validated @RequestBody LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.adminLogin(loginBody.getUsername(), loginBody.getPassword());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    @Anonymous
    @GetMapping("/cacheUser")
    public R<Void> cacheUser() {
        Long tenantId = 100L;
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime deadline = now.plusDays(1);

        List<Long> userIdList = CollUtil.newArrayList();
        Long init = 3000000000001000001L;
        for (Long i = 0L; i <= 20000L; i++) {
            userIdList.add(init + i);
        }

        userEntityService.cacheExamUser(tenantId, userIdList, deadline);
        return R.ok();
    }

    /**
     * 退出登录
     */
    @Anonymous
    @PostMapping("/logout")
    public R<Void> logout() {
        loginService.logout();
        return R.ok("退出成功");
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public R<Map<String, Object>> getInfo() {
        SysUser user = userService.selectUserById(LoginHelper.getUserId());
        // 角色集合
//        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
//        Set<String> permissions = permissionService.getMenuPermission(user);
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("user", user);
//        ajax.put("roles", roles);
//        ajax.put("permissions", permissions);
        return R.ok(ajax);
    }

    /**
     * 获取用户信息简版
     *
     * @return 用户信息
     */
    @GetMapping("getInfo/simple")
    public R<Map<String, Object>> getInfoSimple() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        LoginUserInfoVo user = new LoginUserInfoVo();
        user.setUserId(loginUser.getUserId());
        user.setNickName(loginUser.getNickname());
        user.setTenantId(loginUser.getTenantId());
        String tenantCacheKey = "tenant";
        String tenantName = "";
        Map<String, String> tenantCacheMap = RedisUtils.getCacheMap(tenantCacheKey);
        if (Objects.isNull(tenantCacheMap)) {
            List<RailwayFirm> tenantList = railwayFirmService.list();
            if (CollUtil.isNotEmpty(tenantList)) {
                Map<String, String> tenantMap = StreamUtils.toMap(tenantList, item -> {
                    return item.getRfId() + "";
                }, RailwayFirm::getRfName);
                RedisUtils.setCacheMap(tenantCacheKey, tenantMap);
                tenantName = tenantMap.get(loginUser.getTenantId() + "");
            }
        } else {
            tenantName = tenantCacheMap.get(loginUser.getTenantId() + "");
            if (Objects.isNull(tenantName)) {
                RailwayFirm tenant = railwayFirmService.getById(loginUser.getTenantId());
                if (Objects.nonNull(tenant)) {
                    tenantName = tenant.getRfName();
                }
            }
        }
        user.setTenantName(tenantName);
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("user", user);
        return R.ok(ajax);
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public R<List<RouterVo>> getRouters() {
        Long userId = LoginHelper.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return R.ok(menuService.buildMenus(menus));
    }
}
