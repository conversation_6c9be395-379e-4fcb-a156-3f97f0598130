package com.exam.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 租户视图对象 tenant
 *
 * <AUTHOR>
 * @date 2023-10-27
 */
@Data
@ExcelIgnoreUnannotated
public class TenantVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 租户名称
     */
    @ExcelProperty(value = "租户名称")
    private String name;

    /**
     * 用户名
     */
    @ExcelProperty(value = "用户名")
    private String userName;

    /**
     * 项目数量限制
     */
    @ExcelProperty(value = "项目数量限制")
    private Long projectLimit;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 截至时间
     */
    @ExcelProperty(value = "截至时间")
    private Date endTime;

    /**
     * 企业介绍
     */
    @ExcelProperty(value = "企业介绍")
    private String enterpriseIntroduction;


}
