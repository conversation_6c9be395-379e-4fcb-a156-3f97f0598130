package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 【请填写功能名称】视图对象 st_score_level
 *
 * <AUTHOR>
 * @date 2023-11-06
 */
@Data
@ExcelIgnoreUnannotated
public class StScoreLevelVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 考试id
     */
    @ExcelProperty(value = "考试id")
    private Long examId;

    /**
     * 成绩等级名称
     */
    @ExcelProperty(value = "成绩等级名称")
    private String scoreLevelName;

    /**
     * 区间最高分
     */
    @ExcelProperty(value = "区间最高分")
    private Double highestScore;

    /**
     * 区间最低分
     */
    @ExcelProperty(value = "区间最低分")
    private Double lowestScore;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;


}
