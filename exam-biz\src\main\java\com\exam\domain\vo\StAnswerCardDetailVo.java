package com.exam.domain.vo;

import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 答题卡详细视图对象 st_answer_card_detail
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
@Data
@ExcelIgnoreUnannotated
public class StAnswerCardDetailVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 答题卡id
     */
    @ExcelProperty(value = "答题卡id")
    private Long answerCardId;

    /**
     * 试题id
     */
    @ExcelProperty(value = "试题id")
    private Long questionId;

    /**
     * 试题类型
     */
    @ExcelProperty(value = "试题类型")
    private Integer questionGenre;

    /**
     * 答案
     */
    @ExcelProperty(value = "答案")
    private String answer;

    /**
     * 此题得分
     */
    @ExcelProperty(value = "此题得分")
    private Double singleScore;

    /**
     * 判定结果（0：错误，1：正确）
     */
    @ExcelProperty(value = "判定结果", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=：错误，1：正确")
    private Long reviewResult;

    /**
     * 详细json
     */
    @ExcelProperty(value = "详细json")
    private String detailJson;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 正确答案
     */
    @ExcelProperty(value = "正确答案")
    private String rightKey;

    /**
     * 是否有关键词 1 是 0 否
     */
    @ExcelProperty(value = "是否有关键词 1 是 0 否")
    private Integer isKeyWord;

    /**
     * 选项内容
     */
    @ExcelProperty(value = "选项内容")
    private String optionContent;

    /**
     * 解析
     */
    @ExcelProperty(value = "解析")
    private String analysis;

    /**
     * 题干
     */
    @ExcelProperty(value = "题干")
    private String questionContent;

    /**
     * 是否纠纷申诉(0：否，1：是)
     */
    @ExcelProperty(value = "是否纠纷申诉(0：否，1：是)")
    private String isDisputeComplaint;

    /**
     * 随机选项对应map
     */
    @ExcelProperty(value = "随机选项对应map")
    private String randomOptionMap;


}
