package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 职业考试业务对象 st_professional_examination
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StProfessionalExaminationBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 考试名称
     */
    @NotBlank(message = "考试名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String examName;

    /**
     * 考试时间
     */
    @NotNull(message = "考试时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date examTime;

    /**
     * 入口链接
     */
    @NotBlank(message = "入口链接不能为空", groups = { AddGroup.class, EditGroup.class })
    private String link;

    /**
     * 领域
     */
    @NotBlank(message = "领域不能为空", groups = { AddGroup.class, EditGroup.class })
    private String domainCode;

    /**
     * 权限
     */
    @NotBlank(message = "权限不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date createTime;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createBy;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateBy;


}
