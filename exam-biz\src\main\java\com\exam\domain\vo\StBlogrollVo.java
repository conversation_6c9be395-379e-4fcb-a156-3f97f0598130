package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 友情链接视图对象 st_blogroll
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@Data
@ExcelIgnoreUnannotated
public class StBlogrollVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 链接名称
     */
    @ExcelProperty(value = "链接名称")
    private String name;

    /**
     * 链接地址
     */
    @ExcelProperty(value = "链接地址")
    private String link;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long projectId;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private String createTime;

    /**
     * 创建人id
     */
    @ExcelProperty(value = "创建人id")
    private Long createBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private String updateTime;

    /**
     * 更新人id
     */
    @ExcelProperty(value = "更新人id")
    private Long updateBy;


}
