package com.exam.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StUserExamVo extends StUserExam {

    @ApiModelProperty("考试类型")
    private String examType;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    @ApiModelProperty("考场地址")
    private String address;

    @ApiModelProperty("考场")
    private String code;




}
