package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StUserBo;
import com.exam.domain.vo.StUserVo;
import com.exam.service.IStUserService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/user")
public class StUserController extends BaseController {

    private final IStUserService iStUserService;

    /**
     * 查询【请填写功能名称】列表
     */
    @SaCheckPermission("exam:user:list")
    @GetMapping("/list")
    public TableDataInfo<StUserVo> list(StUserBo bo, PageQuery pageQuery) {
        return iStUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @SaCheckPermission("exam:user:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StUserBo bo, HttpServletResponse response) {
        List<StUserVo> list = iStUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "【请填写功能名称】", StUserVo.class, response);
    }

    /**
     * 获取【请填写功能名称】详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:user:query")
    @GetMapping("/{id}")
    public R<StUserVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStUserService.queryById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @SaCheckPermission("exam:user:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StUserBo bo) {
        return toAjax(iStUserService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改【请填写功能名称】
     */
    @SaCheckPermission("exam:user:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StUserBo bo) {
        return toAjax(iStUserService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除【请填写功能名称】
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:user:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStUserService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
