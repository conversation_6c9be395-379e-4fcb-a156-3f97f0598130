package com.exam.domain;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.exam.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 考场表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
@TableName("st_exam_room")
@ApiModel(value = "StExamRoom对象", description = "考场表")
public class StExamRoom extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty("考场名称")
    private String name;

    @ApiModelProperty("考场编号")
    private String code;

    @ApiModelProperty("省")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long provinceId;

    @ApiModelProperty("市")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long cityId;

    @ApiModelProperty("区县")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long countyId;

    @ApiModelProperty("考场地址")
    private String address;

    @ApiModelProperty("容纳人数")
    private Integer capacity;

    @ApiModelProperty("摄像头链接json")
    private String cameraLinkJson;


    @ApiModelProperty("状态（1：启用；2：停用）")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long status;




}
