package com.exam.domain.vo;

import com.exam.domain.StExamRoom;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.concurrent.atomic.AtomicInteger;

@Data
@EqualsAndHashCode
public class ExamRoomVo extends StExamRoom {

    @ApiModelProperty("完整地址")
    private String completeAddress;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("添加人")
    private String createUserName;

    @ApiModelProperty("修改人")
    private String updateUserName;


    private AtomicInteger currentOccupantNum = new AtomicInteger(0);

    public boolean addOccupant() {
        if(currentOccupantNum.get() < getCapacity()) {
            currentOccupantNum.incrementAndGet();
            return true;
        }
        return false;
    }


    public String getStatusName() {
        Long status = getStatus();
        if(status==null){
            return "";
        }
        if(status.equals(1L)){
            return "启用";
        }else if(status.equals(2L)){
            return "停用";
        }
        return statusName;
    }
}
