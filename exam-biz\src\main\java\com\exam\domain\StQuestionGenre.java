package com.exam.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.exam.common.core.domain.BaseEntity2;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 试题类型对象 st_question_genre
 *
 * <AUTHOR>
 * @date 2023-11-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_question_genre")
public class StQuestionGenre extends BaseEntity2 {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 试题类型名称
     */
    private String questionGenreName;
    /**
     * 分值
     */
    private Long questionScore;
    /**
     * 类型排序
     */
    private Long questionGenreSn;
    /**
     * 删除标识(0:未删除,其他删除)
     */
    @TableLogic
    private Integer delFlag;
}
