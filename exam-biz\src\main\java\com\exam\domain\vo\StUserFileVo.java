package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 用户文件视图对象 st_user_file
 *
 * <AUTHOR>
 * @date 2023-11-24
 */
@Data
@ExcelIgnoreUnannotated
public class StUserFileVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userInfoId;

    /**
     * 文件相对路径
     */
    @ExcelProperty(value = "文件相对路径")
    private String path;

    /**
     * 图片类型  （0：身份证正面， 1：身份证反面）
     */
    @ExcelProperty(value = "图片类型  ", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=：身份证正面，,1=：身份证反面")
    private Long type;

    /**
     * 人脸识别失败照片  文件相对路径
     */
    @ExcelProperty(value = "人脸识别失败照片  文件相对路径")
    private String faceFailPath;


}
