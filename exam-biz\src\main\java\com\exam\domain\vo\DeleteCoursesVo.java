package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname DeleteCoursesVo
 * @Description TODO
 * <AUTHOR>
 * @Version 1.0
 * @Date 2023/11/6 14:18
 */
@Data
@ExcelIgnoreUnannotated
public class DeleteCoursesVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "课程id集合")
    private List<Long> courseIds;
}
