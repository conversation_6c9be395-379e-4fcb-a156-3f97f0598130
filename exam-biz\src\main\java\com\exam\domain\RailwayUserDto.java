package com.exam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 铁路企业用户
 */
@ApiModel(description = "铁路企业用户")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RailwayUserDto {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "证书注册单位")
    private String certificateUnit;

    @ApiModelProperty(value = "省Id")
    private Long provinceId;
    @ApiModelProperty(value = "市Id")
    private Long cityId;
    @ApiModelProperty(value = "区Id")
    private Long countyId;

    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    private String county;


    @ApiModelProperty(value = "子公司或项目Id")
    private Long projectId;

    @ApiModelProperty(value = "企业Id")
    private Long tenantId;
    /**
     * 企业类型：1国铁｜2企业
     */
    @ApiModelProperty(value = "企业类型：1国铁｜2企业")
    private Integer firmType;
    /**
     * 角色组
     */
    @ApiModelProperty(value = "角色组")
    private Long[] roleIds;

    @ApiModelProperty(value = "是否短信通知:1是｜2否")
    private Integer isSendSms;

}
