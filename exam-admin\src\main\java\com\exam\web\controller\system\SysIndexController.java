package com.exam.web.controller.system;

import com.exam.common.annotation.Anonymous;
import com.exam.common.config.ExamConfig;
import com.exam.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
public class SysIndexController {

    /**
     * 系统基础配置
     */
    private final ExamConfig examConfig;

    /**
     * 访问首页，提示语
     */
    @Anonymous
    @GetMapping("/")
    public String index() {
        return StringUtils.format("欢迎使用{}后台管理框架，当前版本：v{}，请通过前端地址访问。", examConfig.getName(), examConfig.getVersion());
    }
}
