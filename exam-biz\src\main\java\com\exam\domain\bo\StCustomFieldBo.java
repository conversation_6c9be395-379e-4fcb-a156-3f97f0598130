package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 岗位属性业务对象 st_custom_field
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StCustomFieldBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 页面类型
     */
    @NotBlank(message = "页面类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fieldType;

    /**
     * 自定义列json
     */
    @NotBlank(message = "自定义列json不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fieldJson;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 项目id
     */
    private Long projectId;


}
