package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 播放进度对象 st_play_progres_optional_history
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_play_progres_optional_history")
public class StPlayProgresOptionalHistory extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 课时id
     */
    private Long classhourId;
    /**
     * 播放时长
     */
    private Long playDuration;
    /**
     * 进度
     */
    private Long progres;
    /**
     * 是否删除(1.是；0.否)
     */
    private Integer delFlag;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人id
     */
    private Long createBy;
    /**
     * 修改人id
     */
    private Long updateBy;
    /**
     * 播放状态(1.已看完；0.未看完)
     */
    private Long playStatus;
    /**
     * 所属公司
     */
    private String companyCode;
    /**
     * 领域
     */
    private String domainCode;

}
