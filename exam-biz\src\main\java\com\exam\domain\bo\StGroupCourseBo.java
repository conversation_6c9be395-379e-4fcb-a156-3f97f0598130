package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 组群课程关系业务对象 st_group_course
 *
 * <AUTHOR>
 * @date 2023-11-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StGroupCourseBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 组群id
     */
    @NotNull(message = "组群id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long groupId;

    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long courseId;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;


}
