package com.exam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.exam.common.helper.LoginHelper;
import com.exam.domain.RailwayMessage;
import com.exam.domain.RailwayMessageDto;
import com.exam.domain.qo.SearchQO;
import com.exam.domain.vo.RailwayMessagePageVo;
import com.exam.domain.vo.RailwayMessageVo;
import com.exam.mapper.RailwayMessageMapper;
import com.exam.service.RailwayMessageService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class RailwayMessageServiceImpl extends ServiceImpl<RailwayMessageMapper, RailwayMessage> implements RailwayMessageService{

    /**
     * 企业管理消息提醒-发布单条消息提醒
     * @param railwayMessageDto
     * @return
     */
    @Override
    public Boolean publishMessage(RailwayMessageDto railwayMessageDto) {
        LocalDateTime now = LocalDateTime.now();
        RailwayMessage railwayMessage = BeanUtil.toBean(railwayMessageDto, RailwayMessage.class);
        railwayMessage.setRmRead(now);
        boolean saveBoolean = save(railwayMessage);
        return saveBoolean;
    }

    /**
     * 通过id查询当条信息的详细内容
     * @param rmId
     * @return
     */
    @Override
    public RailwayMessageVo getMessageById(Long rmId) {
        RailwayMessage railwayMessage = getById(rmId);
        RailwayMessageVo railwayMessageVo = BeanUtil.toBean(railwayMessage, RailwayMessageVo.class);
        return railwayMessageVo;
    }

    /**
     * 根据id批量修改为已读
     * @return
     */
    @Override
    public Boolean updateMessageRead() {
        LocalDateTime now = LocalDateTime.now();
        LambdaUpdateWrapper<RailwayMessage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(RailwayMessage::getRmStatus, 1);
        updateWrapper.set(RailwayMessage::getRmRead, now);
        updateWrapper.eq(RailwayMessage::getRmSendUser, LoginHelper.getUserId());
        boolean result = update(updateWrapper);
        return result;
    }

    /**
     * 消息提醒列表页面分页
     * @param searchQO
     * @return
     */
    @Override
    public IPage<RailwayMessagePageVo> fingMessagePage(SearchQO searchQO) {
        Page page = new Page<>(searchQO.getCurrent(), searchQO.getSize());
        LambdaQueryWrapper<RailwayMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RailwayMessage::getRmSendUser, LoginHelper.getUserId());
        queryWrapper.orderByDesc(RailwayMessage::getCreateTime);
        queryWrapper.orderByAsc(RailwayMessage::getRmStatus);
        IPage<RailwayMessagePageVo> pages = page(page, queryWrapper);
        return pages;
    }

    /**
     * 单条标记为已读
     * @param rmId
     * @return
     */
    @Override
    public Boolean updateMessageReadOne(Long rmId) {
        LambdaUpdateWrapper<RailwayMessage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RailwayMessage::getRmId, rmId);
        updateWrapper.set(RailwayMessage::getRmStatus, 1);
        boolean update = update(updateWrapper);
        return update;
    }
}
