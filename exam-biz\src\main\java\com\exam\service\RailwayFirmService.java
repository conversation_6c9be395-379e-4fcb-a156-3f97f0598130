package com.exam.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.exam.domain.RailwayFirm;
import com.exam.domain.RailwayFirmDto;
import com.exam.domain.vo.RailwayFirmTreeVo;
import com.exam.domain.vo.RailwayFirmVo;

import java.util.List;

public interface RailwayFirmService extends IService<RailwayFirm>{


    void add(RailwayFirmDto firmDto);

    IPage<RailwayFirmVo> queryPage(String rfName, String rfContact, String rfPhone, Integer rfApprove, Integer pageSize, Integer pageNum);

    void editById(RailwayFirmDto firmDto);

    List<RailwayFirmTreeVo> queryFirmTree(String rfName, Long rfId, Integer isParent);

    RailwayFirmVo queryByRfId(Long rfId);

    RailwayFirm selectById(Long tenantId);
}
