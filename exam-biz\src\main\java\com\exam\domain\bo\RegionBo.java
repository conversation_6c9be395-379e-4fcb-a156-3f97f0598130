package com.exam.domain.bo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class RegionBo  {

    /**
     *
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     *
     */
    @ApiModelProperty("地区code")
    private String code;

    /**
     *
     */
    @ApiModelProperty("地区名字")
    private String name;

    /**
     *
     */
    @ApiModelProperty("父节点code，市区必传")
    private String parentCode;

    @ApiModelProperty("级别 1-省，2-市，3-区县 必传")
    private Integer level;
}
