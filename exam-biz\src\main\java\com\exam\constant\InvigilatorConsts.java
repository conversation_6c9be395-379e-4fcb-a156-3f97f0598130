package com.exam.constant;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description:全局变量
 * @create 2020-06-05 11:17
 */
public class InvigilatorConsts {
    public static final String CC19_COMPANY_CODE = "CC19";
    public static final String SADMIN = "admin";
    public static final String USER_PASSWORD = "123456";

    //小程序appid
    public static final String WX_TASK_APPID = "wx0e63e51f5ac0f141";
    public static final String WX_TASK_APPSECRET = "36c6207e93298e42e48485665d4f62de";

    // 小程序code换Session Key地址
    public static final String wx_code2session_url = "https://api.weixin.qq.com/sns/jscode2session?appid=";

    //用户type
    public static final Integer USER_TYPE = 3;
    //默认头像、用户昵称
    public static final String MEM_USERNAME = "默认用户";

    public static final String VALIDATE_CODE = "VALIDATECODE";

    //验证码有效时间   秒
    public static final Long VERIFYCODE_EXPIRATION_TIME = 300L;

    public static final Long TOKEN_EXPIRATION_TIME = 6000L;

    /**
     * 操作成功！MyException中的1000
     */
    public static final String SUCCESS = "1000";

    /**
     * token不存在 请重新登陆！
     */
    public static final String TOKEN_NOT_EXIT = "405";

    /**
     * 请求的数据错误
     */
    public static final String BODY_NOT_MATCH = "400";

    /**
     * 服务器内部错误
     */
    public static final String INTERNAL_SERVER_ERROR = "500";

    /**
     * 签名校验失败
     */
    public static final String SIGN_CHECK_ERROR = "999";

    /**
     * 用户名或密码不能为空！
     */
    public static final String NAME_PASSWORD_ISNULL = "1002";

    /**
     * 用户名或密码错误！
     */
    public static final String NAME_PASSWORD_ERROR = "1003";

    /**
     * 账户已冻结！
     */
    public static final String ACCOUNTS_FROZEN = "1021";

    /**
     * 用户已离职！
     */
    public static final String ACCOUNTS_QUIT = "1022";

    /**
     * 局级单位已存在
     */
    public static final String CC19_EXIST = "1023";

    /**
     * 公司编码重复
     */
    public static final String COMPANY_NO_REPEAT = "1024";

    /**
     * 此公司禁止删除
     */
    public static final String COMPANY_PROHIBIT_DELETE = "1025";

    /**
     * 系统默认角色不可以删除
     */
    public static final String SYSTEM_ROLE_PROHIBIT_DELETE = "1026";

    /**
     * 请选择所属公司
     */
    public static final String NO_SELECT_COMPANY = "1027";
    /**
     * 系统默认角色不可以分配菜单
     */
    public static final String SYSTEM_ROLE_NO_DISTRIBUTE = "1028";

    /**
     * 角色已被使用，禁止删除
     */
    public static final String ROLE_USED = "1029";
    /**
     * 用户已存在！
     */
    public static final String ACCOUNTS_EXIST = "1030";
    /**
     * 无此系统登录权限！
     */
    public static final String NO_LIMITS_OF_AUTHORITY = "1031";
    /**
     * 角色名称已存在
     */
    public static final String ROLE_NAME_EXIST = "1032";
    /**
     * 部门已被使用，禁止删除
     */
    public static final String DEPARTMENT_USED = "1011";
    /**
     * 岗位已被使用，禁止删除
     */
    public static final String JOB_USED = "1012";

    /**
     * 文件不存在
     */
    public static final String FILE_NOT_EXIST = "1013";
    /**
     * 文件类型错误
     */
    public static final String FILE_TYPE_ERROR = "1014";
    /**
     * 部门名称已存在
     */
    public static final String DEPT_NAME_EXIST = "1015";
    /**
     * 岗位名称已存在
     */
    public static final String JOB_NAME_EXIST = "1016";
    /**
     * 项目已存在关联信息
     */
    public static final String PROJECT_RELATION_EXIST = "1017";
    /**
     * 文件类型错误
     */
    public static final String VALIDATE_CODE_ERROR = "2008";
    /**
     * token is null!
     */
    public static final String TOKEN_IS_NULL = "3000";

    /**
     * code is null!
     */
    public static final String WECHAT_LOGIN_CODE_IS_NULL = "3001";

    /**
     * 微信授权失败!
     */
    public static final String WECHAT_PRIVILEGE_GRANT_FAILED = "3013";

    /**
     * 用户与此微信不匹配!
     */
    public static final String OPENID_PHONENUM_NOT_MATCHING = "3014";
    /**
     * 用户信息无效
     */
    public static final String USER_INFO_INVALID = "3015";
    /**
     * 用户密码错误
     */
    public static final String USER_PASSWORD_ERROR = "3016";
    /**
     * 原始密码错误
     */
    public static final String ORIGINAL_PWDERROR = "3017";
    /**
     * 人脸对比错误
     */
    public static final String FACE_ERROR = "3068";
    /**
     * 人脸比对不合格
     */
    public static final String FACE_COMPARE_UNQUALIFIED = "3100";
    /**
     * 人脸比对mq
     */
    public static final String IMAGE_COMPARE_ASYNCHRONOUS_QUEUE  = "image_compare_asynchronous_queue";

    /**
     * 人脸比 系统与成功比例
     */
    public static Map<String, Object> FACE_COMPARE_APP_CONFIDENCE;

    /**
     * 人脸正在处理
     */
    public static final String FACE_PROCESSING = "3200";

    /**
     * 人脸处理超时
     */
    public static final String FACE_TIMEOUT = "3201";

    /**
     * 非本人
     */
    public static final String ALIFACE_ORDERS = "3202";
    /**
     * 多个人脸
     */
    public static final String ALIFACE_MORE_PEOPLE = "3203";
    /**
     * 无人脸
     */
    public static final String ALIFACE_NO_ONE = "3204";
    /**
     * 作弊动作
     */
    public static final String ALIFACE_CHEAT = "3205";

    /**
     * 阿里人脸对比报错
     */
    public static final String ALIFACE_ERROR = "3206";

    public static final int confidenceInt = 61;

    /**
     * 人脸处理超时时间
     */
    public static final Long FACE_PROCESSING_TIME = 60L * 10;


    /**
     * 上传文件的大小限制  kb
     */
    public static final Integer FILE_SIZE_MAX = 1024 * 20;

    /**
     * 文件过大，上传失败
     */
    public static final String FILE_SIZE_TOO_LARGE = "4001";

    /**
     * 上传用户类型文件路径不存在
     */
    public static final String USER_FILE_NULL_ERROR = "3067";

}
