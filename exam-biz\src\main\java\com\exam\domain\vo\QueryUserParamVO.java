package com.exam.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/11/24 16:00
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "查询用户列表参数对象")
public class QueryUserParamVO {


    /**
     * 节点id
     */
    @ApiModelProperty(value = "节点id")
    private Integer nodeId;

    /**
     * 节点编码
     */
    @ApiModelProperty(value = "节点编码")
    private String orgCode;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;


    /**
     * 组id
     */
    @ApiModelProperty(value = "组id")
    private String groupId;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量")
    private Integer size;

    /**
     * 当前页数
     */
    @ApiModelProperty(value = "当前页数")
    private Integer current;


    @ApiModelProperty(value = "分类（1，公司；2，部门；3，项目;4，岗位;）")
    private int type;
    /**
     * 公司编码
     */
    private String companyCode;

    private String companyCodes;

    private String jobIds;

    @ApiModelProperty(value = "部门")
    private String deptName;

    @ApiModelProperty(value = "岗位")
    private String jobName;

    @ApiModelProperty(value = "属性")
    private String jobAttrName;

    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 租户id
     */
    private Long tenantId;
}
