package com.exam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
    * 铁路局-企业管理-考场
    */
@ApiModel(description="铁路局-企业管理-考场")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "railway_exam_room")
public class RailwayExamRoom {
    /**
     * 考场Id
     */
    @TableId(value = "rer_id")
    @ApiModelProperty(value="考场Id")
    private Long rerId;

    /**
     * 企业管理Id
     */
    @TableField(value = "rf_id")
    @ApiModelProperty(value="企业管理Id")
    private Long rfId;

    /**
     * 名称
     */
    @TableField(value = "rer_name")
    @ApiModelProperty(value="名称")
    private String rerName;

    /**
     * 地址
     */
    @TableField(value = "rer_address")
    @ApiModelProperty(value="地址")
    private String rerAddress;

    /**
     * 容纳人数
     */
    @TableField(value = "rer_people_count")
    @ApiModelProperty(value="容纳人数")
    private Integer rerPeopleCount;

    /**
     * 状态1启用｜0停用
     */
    @TableField(value = "rer_status")
    @ApiModelProperty(value="状态1启用｜0停用")
    private Boolean rerStatus;

    /**
     * 创建者
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value="创建者")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value="更新者")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updateTime;
}
