package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.constant.CommonDataBaseConst;
import com.exam.domain.StMessage;
import com.exam.domain.StMessageDTO;
import com.exam.domain.bo.StMessageBo;
import com.exam.domain.qo.StMessageQO;
import com.exam.domain.vo.StMessageVo;
import com.exam.service.IStMessageService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 纠纷申诉
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/message")
public class StMessageController extends BaseController {

    private final IStMessageService iStMessageService;

    @ApiOperationSupport(order = 1)
    @GetMapping(value = "/groupList")
    @ApiOperation(value = "1.0 消息通知首页查询")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<StMessageVo>> groupList() {
        return R.ok(iStMessageService.groupList());
    }

    @ApiOperationSupport(order = 1)
    @PostMapping(value = "/listByQO")
    @ApiOperation(value = "1.1 消息通知查询根据查询条件")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<StMessageVo>> listByQO(@RequestBody StMessageQO stMessageQO) {
        return R.ok(iStMessageService.listByQO(stMessageQO));
    }

    @ApiOperationSupport(order = 1)
    @PostMapping(value = "/listByType")
    @ApiOperation(value = "1.2 消息通知查根据触发条件")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<StMessageVo>> listByType(String type) {
        StMessageQO stMessageQO = new StMessageQO();
        stMessageQO.setMsgType(type);
        return R.ok(iStMessageService.listByQO(stMessageQO));
    }

    @ApiOperationSupport(order = 1)
    @PostMapping(value = "/update")
    @ApiOperation(value = "1.3 消息通知修改")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> update(@RequestBody StMessageBo stMessageBo) {
        iStMessageService.updateByBo(stMessageBo);
        return R.ok();
    }

    @ApiOperationSupport(order = 1)
    @PostMapping(value = "/updateList")
    @ApiOperation(value = "1.7 消息通知修改")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> updateList(@RequestBody List<StMessage> list) {
        iStMessageService.saveOrUpdateBatch(list);
        return R.ok();
    }

    @ApiOperationSupport(order = 1)
    @GetMapping(value = "/start")
    @ApiOperation(value = "1.4 消息通知启用")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> start(String msgType) {
        StMessageDTO stMessageDTO = new StMessageDTO();
        stMessageDTO.setMsgType(msgType);
        stMessageDTO.setMsgStatus(CommonDataBaseConst.MSG_STATUS.START.getCode().toString());
        iStMessageService.updateStatusByMsgType(stMessageDTO);
        return R.ok();
    }

    @ApiOperationSupport(order = 1)
    @GetMapping(value = "/stop")
    @ApiOperation(value = "1.5 消息通知停用")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> stop(String msgType) {
        StMessageDTO stMessageDTO = new StMessageDTO();
        stMessageDTO.setMsgType(msgType);
        stMessageDTO.setMsgStatus(CommonDataBaseConst.MSG_STATUS.STOP.getCode().toString());
        iStMessageService.updateStatusByMsgType(stMessageDTO);
        return R.ok();
    }

    /**
     * 查询纠纷申诉列表
     */
    @SaCheckPermission("exam:message:list")
    @GetMapping("/list")
    public TableDataInfo<StMessageVo> list(StMessageBo bo, PageQuery pageQuery) {
        return iStMessageService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出纠纷申诉列表
     */
    @SaCheckPermission("exam:message:export")
    @Log(title = "纠纷申诉", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StMessageBo bo, HttpServletResponse response) {
        List<StMessageVo> list = iStMessageService.queryList(bo);
        ExcelUtil.exportExcel(list, "纠纷申诉", StMessageVo.class, response);
    }

    /**
     * 获取纠纷申诉详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:message:query")
    @GetMapping("/{id}")
    public R<StMessageVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStMessageService.queryById(id));
    }

    /**
     * 新增纠纷申诉
     */
    @SaCheckPermission("exam:message:add")
    @Log(title = "纠纷申诉", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StMessageBo bo) {
        return toAjax(iStMessageService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改纠纷申诉
     */
    @SaCheckPermission("exam:message:edit")
    @Log(title = "纠纷申诉", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StMessageBo bo) {
        return toAjax(iStMessageService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除纠纷申诉
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:message:remove")
    @Log(title = "纠纷申诉", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStMessageService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
