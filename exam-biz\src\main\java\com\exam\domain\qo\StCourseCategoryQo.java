package com.exam.domain.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;

/**
 * @Classname StCourseCategoryQo
 * @Description TODO
 * <AUTHOR>
 * @Version 1.0
 * @Date 2023/10/31 15:16
 */
@Data
public class StCourseCategoryQo {

    /**
     * 所属类别id
     */
    private Long categoryId;

    /**
     * 是否安管人员(1.是；0.否)
     */
    private Long isSafety;

    /**
     * 课程分类名称
     */
    private String categoryName;

    /**
     * 父id
     */
    private Long fatherId;

    /**
     *
     */
    private String childIds;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 项目ID
     */
    private Long projectId;

}
