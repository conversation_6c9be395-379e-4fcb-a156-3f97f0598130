package com.exam.domain.vo;

import com.exam.utils.ReadFileUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 培训情况
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserTrainSituationVO", description="培训情况vo")
public class UserTrainSituationVO implements Serializable {

    @ApiModelProperty(value = "课程id")
    private String courseId;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "课时数")
    private Integer classhourCount;

    private Long courseDuration;

    @ApiModelProperty(value = "开始培训时间")
    private String startTrainTime;

    @ApiModelProperty(value = "结束培训时间")
    private String endTrainTime;

    @ApiModelProperty(value = "播放时长")
    private long playDuration;

    @ApiModelProperty(value = "所属公司")
    private String companyCode;

    @ApiModelProperty(value = "课程来源（1，规定；2，自选）")
    private String courseSource;

    @ApiModelProperty(value = "培训状态（1，已完成；2，培训中；3，待培训）")
    private String trainStatusDis;


    @ApiModelProperty(value = "课程时长（显示）")
    private String courseDurationDis;

    @ApiModelProperty(value = "学时（显示）")
    private String studyDuration;

    @ApiModelProperty(value = "培训进度（显示）")
    private String progresDis;
    @ApiModelProperty(value = "培训进度")
    private String progres;

    @ApiModelProperty(value = "重置：0未重置，1重置")
    private String isReset;

    public String getCourseDurationDis() {
        if(courseDuration!=null){
            return ReadFileUtil.secondToTime(courseDuration);
        }
        return "";
    }
    public String getProgresDis() {
        if(this.getProgres().equals("100")){
            return "100%";
        }
        if(courseDuration!=null && courseDuration!=0){
            BigDecimal playDurationB = new BigDecimal(playDuration*100);
            BigDecimal courseDurationB = new BigDecimal(courseDuration);
            BigDecimal progres= playDurationB.divide(courseDurationB,2, BigDecimal.ROUND_HALF_UP);
//            int progres= (int)(playDuration*1.0/courseDuration)*100;
            return progres.toString() + "%";
        }
        return "0%";
    }

    public String getTrainStatusDis() {
        if(this.getProgres().equals("100")){
            return "100%";
        }
        if(courseDuration!=null && courseDuration!=0){
            BigDecimal playDurationB = new BigDecimal(playDuration*100);
            BigDecimal courseDurationB = new BigDecimal(courseDuration);
            BigDecimal progres= playDurationB.divide(courseDurationB,2, BigDecimal.ROUND_HALF_UP);
//            int progres= (int)(playDuration*1.0/courseDuration)*100;
            return progres.toString() + "%";
//            int progres= (int)(playDuration*1.0/courseDuration)*100;
//            return progres+"%";
        }
        return "0%";
    }
}

