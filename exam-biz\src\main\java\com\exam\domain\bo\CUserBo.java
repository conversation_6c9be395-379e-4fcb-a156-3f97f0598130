package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户业务对象 c_user
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class CUserBo extends BaseEntity {

    /**
     * 为了兼容已有设备管理系统产生的主键ID，与key_id值相同
     */
    @NotNull(message = "为了兼容已有设备管理系统产生的主键ID，与key_id值相同不能为空", groups = { EditGroup.class })
    private Long userInfoId;

    /**
     * 用户姓名
     */
    @NotBlank(message = "用户姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userPhoneNum;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pwd;

    /**
     * 用户类型(,1,2,3,)
     */
    @NotBlank(message = "用户类型(,1,2,3,)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userType;

    /**
     * 用户级别（1：局级；2：处级；3：项目；）
     */
    @NotNull(message = "用户级别（1：局级；2：处级；3：项目；）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userLevel;

    /**
     * 所属部门ID
     */
    @NotNull(message = "所属部门ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;

    /**
     * 职务ID
     */
    @NotNull(message = "职务ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long jobId;

    /**
     * 用户头像
     */
    @NotBlank(message = "用户头像不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userAvatar;

    /**
     * 小程序openID
     */
    @NotBlank(message = "小程序openID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String openId;

    /**
     * 微信开放平台用户ID
     */
    @NotBlank(message = "微信开放平台用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String unionId;

    /**
     * 注册时间
     */
    @NotNull(message = "注册时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date signTime;

    /**
     * 状态（0：冻结；1:正常；2:离职）
     */
    @NotNull(message = "状态（0：冻结；1:正常；2:离职）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userStatus;

    /**
     * 注册时间(显示使用)
     */
    @NotBlank(message = "注册时间(显示使用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String signTimeStr;

    /**
     * 所属公司
     */
    @NotBlank(message = "所属公司不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @NotNull(message = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dataStatus;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long signUserId;

    /**
     * 更新人id
     */
    @NotNull(message = "更新人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateUserId;

    /**
     * 编辑时间(显示使用)
     */
    @NotBlank(message = "编辑时间(显示使用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String updateTimeStr;

    /**
     * 用户性别（0：男，1：女）
     */
    @NotNull(message = "用户性别（0：男，1：女）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sex;

    /**
     * 身份证号码
     */
    @NotBlank(message = "身份证号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idNumber;

    /**
     * 民族
     */
    @NotBlank(message = "民族不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nation;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phoneNumber;

    /**
     * 电子邮箱
     */
    @NotBlank(message = "电子邮箱不能为空", groups = { AddGroup.class, EditGroup.class })
    private String email;

    /**
     * 人员性质（0：正式员工；1：合同工；2：临时员工；3：务工人员）
     */
    @NotNull(message = "人员性质（0：正式员工；1：合同工；2：临时员工；3：务工人员）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long naturePersonnel;

    /**
     * 密码更新时间
     */
    @NotBlank(message = "密码更新时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pwdUpdateDate;

    /**
     * 数据状态(0:是,1:否)
     */
    @NotNull(message = "数据状态(0:是,1:否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isLeader;

    /**
     * 节点编码
     */
    @NotBlank(message = "节点编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgCode;

    /**
     * 一体化平台用户id
     */
    @NotNull(message = "一体化平台用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long integrativeId;

    /**
     * 是否为管理员(0:不是,1:是)
     */
    @NotNull(message = "是否为管理员(0:不是,1:是)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isManager;

    /**
     * 删除时间
     */
    @NotBlank(message = "删除时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deleteTime;

    /**
     * 手机号码 区号
     */
    @NotBlank(message = "手机号码 区号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phoneNumberCode;

    /**
     * 0：程序   1：从userjob中导入
     */
    @NotNull(message = "0：程序   1：从userjob中导入不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long jobSource;


}
