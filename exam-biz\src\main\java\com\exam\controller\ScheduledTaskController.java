package com.exam.controller;


import com.exam.service.*;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName ScheduledTaskController
 * @Description 定时任务
 * <AUTHOR>
 * @Date 2021/6/16 16:42 下午
 * @Version 1.0
 */


@ApiSort(value = 8)
@RequestMapping("ScheduledTaskController")
@RestController
@Api(tags = "ScheduledTaskController(定时任务)")
@EnableScheduling
@Slf4j
public class ScheduledTaskController {

    @Autowired
    IStExamService iStExamService;
    @Autowired
    IStCompulsionComplaintService stCompulsionComplaintService;
    @Autowired
    UserInfoManageService userInfoManageService;
    @Autowired
    IExamService iExamService;
    @Autowired
    SignUpService signUpService;

    @ApiOperationSupport(order = 1)
    @GetMapping(value = "/autoQueryUserExamQuestion")
    @ApiOperation(value = "1.2 每一个小时执行一次，创建考试前一小时的试卷")
    @Scheduled(cron = "0 0 * * * ?")
//  @Scheduled(cron = "0 0/5 * * * ?")
    @SchedulerLock(name = "syncOrder",
            lockAtMostFor = "600000", lockAtLeastFor = "300000")
    public void autoQueryUserExamQuestion() {
        iStExamService.createUserExamQuestion();
    }



    /**
     * 强制交卷申诉是否超时
     *
     */
    @Scheduled(cron = "0 0 * * * ?") // 每一个小时执行一次 判断是否超时
	@net.javacrumbs.shedlock.spring.annotation.SchedulerLock(name = "compulsionComplaintTimeOut", lockAtMostFor = "PT4M", lockAtLeastFor = "PT2M")
	public void compulsionComplaintTimeOut() {
        stCompulsionComplaintService.isTimeOut();
    }




    @ApiOperationSupport(order = 5)
    @GetMapping(value = "/autoManualReview")
    @ApiOperation(value = "1.5 每天凌晨1点执行 超过五天未手动阅卷，自动阅卷")
    @Scheduled(cron = "0 0 1 * * ?")
    public void autoManualReview() {
        iExamService.autoManualReview();
    }


    @ApiOperationSupport(order = 6)
    @GetMapping(value = "/autoFinishSignUp")
    @ApiOperation(value = "1.6 每一分钟执行一次 考试报名到时间自动结束")
    @Scheduled(cron = "0 * * * * ?")
    public void autoFinishSignUp() {
        signUpService.autoFinishSignUp();
    }
}
