package com.exam.domain.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 强制申诉QO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StCompulsionComplaintQo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 考试名称
     */
    private String examName;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 申诉时间
     */
    private String disputeTime;

    /**
     * 项目id
     */
    private Long projectId;
    /**
     * 用户id
     */
    private Long userId;

}
