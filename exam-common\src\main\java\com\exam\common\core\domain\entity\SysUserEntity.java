package com.exam.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.annotation.Sensitive;
import com.exam.common.constant.UserConstants;
import com.exam.common.core.domain.BaseEntity;
import com.exam.common.enums.SensitiveStrategy;
import com.exam.common.xss.Xss;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class SysUserEntity extends BaseEntity {

    /**
     * 用户ID
     */
    @TableId(value = "user_id")
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户账号
     */
    private String userName;

    public String getUserName() {
        if (Objects.isNull(userName)) {
            return "";
        }
        return userName;
    }

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户类型（sys_user系统用户）
     */
    private String userType;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phonenumber;

    public String getPhonenumber() {
        if (Objects.isNull(phonenumber)) {
            return "";
        }
        return phonenumber;
    }

    /**
     * 身份证号码
     */
    private String idNumber;

    public String getIdNumber() {
        if (Objects.isNull(idNumber)) {
            return "";
        }
        return idNumber;
    }

    /**
     * 用户性别
     */
    private String sex;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 密码
     */
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 备注
     */
    private String remark;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 企业类型
     */
    private Integer firmType;

}
