package com.exam.domain.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 考试说明
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ExamStatedVO implements Serializable {

    /**
     * 题型集合
     */
    private List<StQuestionGenreVo> stQuestionGenreVOList;

    /**
     * 考试时长
     */
    private Long examLength;

    /**
     * 试卷分值
     */
    private double examScore;

    /**
     * 考试 ID
     */
    private String examId;

    /**
     * 试卷类型
     */
    private String paperType;

    /**
     * 是否查看答案解析(1.是；0.否)
     */
    private String isAnswerAnalysis;

    /**
     * 是否开启纠纷申诉
     */
    private String isDisputeComplaint;

    /**
     * 是否开启强制交卷申诉(1.是；0.否)
     */
    private String isCompulsionComplaint;

    /**
     * 可申诉时间
     */
    private String canAppealTime;

    /**
     * 申诉处理时间
     */
    private String appealHandleTime;

    /**
     * 申诉继续考试时间
     */
    private String appealExamTime;

    /**
     * 是否启用人脸识别(1.是；0.否)
     */
    private String isFaceRecognition;

    /**
     * 是否视频监控(1.是；0.否)
     */
    private String isVideoSurveillance;

    /**
     * 抓拍次数
     */
    private String captureCnt;

    /**
     * 识别错误数阈值
     */
    private String recognitionErrorCnt;

    /**
     * 切换页面数阈值
     */
    private String switchPagesCnt;

    /**
     * 是否开启客户端考试(1.是；0.否)
     */
    private String isClient;

    /**
     * 是否多屏禁用(1.是；0.否)
     */
    private String isMultiScreen;

    /**
     * 是否动态监控(1.是；0.否)
     */
    private String isDynamicSupervisory;

    /**
     * 是否强制全屏
     */
    private String isForceFullScreen;

    /**
     * 是否切换页面
     */
    private String isSwitchPages;

    /**
     * 作弊动作提醒次数
     */
    private String cheatCnt;

    /**
     * 多个人脸提醒次数
     */
    private String morePeopleCnt;

    /**
     * 无人脸提醒次数
     */
    private String noOneCnt;

    /**
     * 非本人提醒次数
     */
    private String ordersCnt;

    /**
     * 及格分数
     */
    private double passScore;

    /**
     * 注意事项
     */
    private String[] note;


    /**
     * ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 考试名称
     */
    private String examName;

    /**
     * 用户组群 ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long groupId;

    /**
     * 培训方式(0:不培训,1:规定课程,2:自选课程)
     */
    private String isNeedTrain;

    /**
     * 允许考试的最少培训时间（秒）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long allowExamTrainDuration;

    /**
     * 是否设置等级(1:是、0:否)
     */
    private String isSetLevel;

    /**
     * 试题类别id集合(逗号分割)
     */
    private String questionTypeIds;

    /**
     * 允许补考次数
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Integer allowedTimes;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 补考开始时间
     */
    private String resitStartTime;

    /**
     * 补考结束时间
     */
    private String resitEndTime;

    /**
     * 是否手动阅卷(1.是；0.否)
     */
    private String isManualReview;
}
