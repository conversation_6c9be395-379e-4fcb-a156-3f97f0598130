package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 角色视图对象 c_role
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class CRoleVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 角色名称
     */
    @ExcelProperty(value = "角色名称")
    private String name;

    /**
     * 角色编码
     */
    @ExcelProperty(value = "角色编码")
    private String code;

    /**
     * 所属公司ID
     */
    @ExcelProperty(value = "所属公司ID")
    private Long companyId;

    /**
     * 角色类型（0：默认角色；1：自定义角色；）
     */
    @ExcelProperty(value = "角色类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=：默认角色；1：自定义角色；")
    private Long type;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Long sn;

    /**
     * 所属公司编码
     */
    @ExcelProperty(value = "所属公司编码")
    private String companyCode;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @ExcelProperty(value = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))")
    private Long dataStatus;

    /**
     * 节点编码
     */
    @ExcelProperty(value = "节点编码")
    private String orgCode;

    /**
     * 是否为主岗位(0：否；1：是)
     */
    @ExcelProperty(value = "是否为主岗位(0：否；1：是)")
    private Long isMainPost;

    /**
     * 父节点ID
     */
    @ExcelProperty(value = "父节点ID")
    private Long fatherId;

    /**
     * 创建人ID
     */
    @ExcelProperty(value = "创建人ID")
    private Long createUserId;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private String createTimeStr;

    /**
     * 编辑人ID
     */
    @ExcelProperty(value = "编辑人ID")
    private Long updateUserId;

    /**
     * 编辑时间
     */
    @ExcelProperty(value = "编辑时间")
    private String updateTimeStr;

    /**
     * 上级节点编码
     */
    @ExcelProperty(value = "上级节点编码")
    private String parentOrgCode;

    /**
     * 是否为外部岗位(0：否；1：是；)
     */
    @ExcelProperty(value = "是否为外部岗位(0：否；1：是；)")
    private Long isExternal;

    /**
     * 角色级别（1：局级；2：处级；3：项目；）
     */
    @ExcelProperty(value = "角色级别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=：局级；2：处级；3：项目；")
    private Long roleLevel;


}
