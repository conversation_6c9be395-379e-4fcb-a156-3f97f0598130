package com.exam.domain.vo;

import com.exam.system.domain.vo.SysOssVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 铁路局——企业管理
 */
@ApiModel(description = "铁路局——企业管理")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RailwayFirmVo {
    /**
     * 企业管理Id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "企业管理Id")
    private Long rfId;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String rfName;

    /**
     * 信用码
     */
    @ApiModelProperty(value = "信用码")
    private String rfCreditCode;

    /**
     * 注册登记地
     */
    @ApiModelProperty(value = "注册登记地")
    private String rfAddress;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String rfContact;

    /**
     * 联系人手机号
     */
    @ApiModelProperty(value = "联系人手机号")
    private String rfPhone;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String rfIdCard;


    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private Long createBy;


    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createByUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createTimeStr;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private Long updateBy;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private String updateByUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private String updateTimeStr;

    /**
     * 营业执照ssoId
     */
    @ApiModelProperty(value = "营业执照ssoId")
    private String rfBusinessLicenseSsoId;

    /**
     * 安全生产许可证ssoId
     */
    @ApiModelProperty(value = "安全生产许可证ssoId")
    private String rfSafeProductionSsoId;

    @ApiModelProperty(value = "审核状态:0未审核｜1通过｜2驳回")
    private Integer rfaApprove;
    @ApiModelProperty(value = "审核人Id")
    private Long rfaApprover;
    @ApiModelProperty(value = "审核人")
    private String rfaApproverName;
    @ApiModelProperty(value = "审核时间")
    private LocalDateTime rfaTime;
    @ApiModelProperty(value = "审核时间Str")
    private String rfaTimeStr;


}
