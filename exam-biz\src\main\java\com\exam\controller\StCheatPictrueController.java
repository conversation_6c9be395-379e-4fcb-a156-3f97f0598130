package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StCheatPictrueBo;
import com.exam.domain.vo.StCheatPictrueVo;
import com.exam.service.IStCheatPictrueService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.common.core.page.TableDataInfo;

/**
 * 岗位属性
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/cheatPictrue")
public class StCheatPictrueController extends BaseController {

    private final IStCheatPictrueService iStCheatPictrueService;

    /**
     * 查询岗位属性列表
     */
    @SaCheckPermission("exam:cheatPictrue:list")
    @GetMapping("/list")
    public TableDataInfo<StCheatPictrueVo> list(StCheatPictrueBo bo, PageQuery pageQuery) {
        return iStCheatPictrueService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出岗位属性列表
     */
    @SaCheckPermission("exam:cheatPictrue:export")
    @Log(title = "岗位属性", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StCheatPictrueBo bo, HttpServletResponse response) {
        List<StCheatPictrueVo> list = iStCheatPictrueService.queryList(bo);
        ExcelUtil.exportExcel(list, "岗位属性", StCheatPictrueVo.class, response);
    }

    /**
     * 获取岗位属性详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:cheatPictrue:query")
    @GetMapping("/{id}")
    public R<StCheatPictrueVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStCheatPictrueService.queryById(id));
    }

    /**
     * 新增岗位属性
     */
    @SaCheckPermission("exam:cheatPictrue:add")
    @Log(title = "岗位属性", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StCheatPictrueBo bo) {
        return toAjax(iStCheatPictrueService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改岗位属性
     */
    @SaCheckPermission("exam:cheatPictrue:edit")
    @Log(title = "岗位属性", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StCheatPictrueBo bo) {
        return toAjax(iStCheatPictrueService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除岗位属性
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:cheatPictrue:remove")
    @Log(title = "岗位属性", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStCheatPictrueService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
