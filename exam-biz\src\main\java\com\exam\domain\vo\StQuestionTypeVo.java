package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;


/**
 * 试题类型视图对象 st_question_type
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Data
@ExcelIgnoreUnannotated
public class StQuestionTypeVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private String id;

    /**
     * 试题类别名称
     */
    @ExcelProperty(value = "试题类别名称")
    private String questionTypeName;

    /**
     * 类别排序
     */
    @ExcelProperty(value = "类别排序")
    private Long questionTypeSn;

    /**
     * 父id
     */
    @ExcelProperty(value = "父id")
    private String fatherId;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人ID
     */
    private String createBy;

    /**
     * 创建人ID
     */
    private String userName;

    /**
     * 分类层级
     */
    private Integer level;

    /**
     * 二级试题类别
     */
    private List<StQuestionTypeVo> children;

}
