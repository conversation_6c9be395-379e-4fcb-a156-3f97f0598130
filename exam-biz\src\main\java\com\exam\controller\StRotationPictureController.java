package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.page.TableDataInfo;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.bo.StRotationPictureBo;
import com.exam.domain.vo.StRotationPictureVo;
import com.exam.service.IStRotationPictureService;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 【请填写功能名称】
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/rotationPicture")
public class StRotationPictureController extends BaseController {

    private final IStRotationPictureService iStRotationPictureService;

    /**
     * 查询【请填写功能名称】列表
     */
    @SaCheckPermission("exam:rotationPicture:list")
    @GetMapping("/list")
    public TableDataInfo<StRotationPictureVo> list(StRotationPictureBo bo, PageQuery pageQuery) {
        return iStRotationPictureService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @SaCheckPermission("exam:rotationPicture:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StRotationPictureBo bo, HttpServletResponse response) {
        List<StRotationPictureVo> list = iStRotationPictureService.queryList(bo);
        ExcelUtil.exportExcel(list, "【请填写功能名称】", StRotationPictureVo.class, response);
    }

    /**
     * 获取【请填写功能名称】详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:rotationPicture:query")
    @GetMapping("/{id}")
    public R<StRotationPictureVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStRotationPictureService.queryById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @SaCheckPermission("exam:rotationPicture:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StRotationPictureBo bo) {
        return toAjax(iStRotationPictureService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改【请填写功能名称】
     */
    @SaCheckPermission("exam:rotationPicture:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StRotationPictureBo bo) {
        return toAjax(iStRotationPictureService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除【请填写功能名称】
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:rotationPicture:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable Long[] ids) {
        return toAjax(iStRotationPictureService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
