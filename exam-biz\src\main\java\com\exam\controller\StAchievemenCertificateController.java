package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StAchievemenCertificateBo;
import com.exam.domain.vo.StAchievemenCertificateVo;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.service.IStAchievemenCertificateService;
import com.exam.common.core.page.TableDataInfo;

/**
 * 成绩证书
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/achievemenCertificate")
public class StAchievemenCertificateController extends BaseController {

    private final IStAchievemenCertificateService iStAchievemenCertificateService;

    /**
     * 查询成绩证书列表
     */
    @SaCheckPermission("exam:achievemenCertificate:list")
    @GetMapping("/list")
    public TableDataInfo<StAchievemenCertificateVo> list(StAchievemenCertificateBo bo, PageQuery pageQuery) {
        return iStAchievemenCertificateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出成绩证书列表
     */
    @SaCheckPermission("exam:achievemenCertificate:export")
    @Log(title = "成绩证书", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StAchievemenCertificateBo bo, HttpServletResponse response) {
        List<StAchievemenCertificateVo> list = iStAchievemenCertificateService.queryList(bo);
        ExcelUtil.exportExcel(list, "成绩证书", StAchievemenCertificateVo.class, response);
    }

    /**
     * 获取成绩证书详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:achievemenCertificate:query")
    @GetMapping("/{id}")
    public R<StAchievemenCertificateVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStAchievemenCertificateService.queryById(id));
    }

    /**
     * 新增成绩证书
     */
    @SaCheckPermission("exam:achievemenCertificate:add")
    @Log(title = "成绩证书", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StAchievemenCertificateBo bo) {
        return toAjax(iStAchievemenCertificateService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改成绩证书
     */
    @SaCheckPermission("exam:achievemenCertificate:edit")
    @Log(title = "成绩证书", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StAchievemenCertificateBo bo) {
        return toAjax(iStAchievemenCertificateService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除成绩证书
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:achievemenCertificate:remove")
    @Log(title = "成绩证书", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStAchievemenCertificateService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
