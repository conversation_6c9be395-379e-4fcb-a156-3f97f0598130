package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 模式考试题库业务对象 st_mock_question
 *
 * <AUTHOR>
 * @date 2023-11-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StMockQuestionBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long id;

    /**
     * 试题类别id
     */
    @NotNull(message = "试题类别id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long questionTypeId;

    /**
     * 试题id
     */
    @NotNull(message = "试题id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long questionId;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;

    /**
     * 所属项目
     */
    @NotNull(message = "所属项目不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;


    /**
     * 试题类别IdStr
     */
    private String questionTypeIdStr;

    /**
     * 试题类别
     */
    private Long questionGenre;

    /**
     * 试题内容
     */
    private String questionContent;

    /**
     * 选择日期
     */
    private String questionTime;

    /**
     * 试题类别Ids
     */
    private String[] questionTypeIds;

}
