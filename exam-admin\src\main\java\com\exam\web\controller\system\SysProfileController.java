package com.exam.web.controller.system;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import com.exam.common.annotation.Log;
import com.exam.common.constant.UserConstants;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.R;
import com.exam.common.core.domain.entity.SysUser;
import com.exam.common.enums.BusinessType;
import com.exam.common.helper.LoginHelper;
import com.exam.common.utils.StringUtils;
import com.exam.common.utils.file.MimeTypeUtils;
import com.exam.common.utils.redis.RedisUtils;
import com.exam.domain.bo.ChangeUserBo;
import com.exam.system.domain.SysOss;
import com.exam.system.service.ISysOssService;
import com.exam.system.service.ISysUserEntityService;
import com.exam.system.service.ISysUserService;
import com.exam.utils.SmsUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.exam.constant.RedisKeyConstant.VERIFICATION_CODE;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController extends BaseController {

    private final ISysUserService userService;
    private final ISysOssService iSysOssService;

    private final ISysUserEntityService sysUserEntityService;


    /**
     * 个人信息
     */
    @GetMapping
    public R<Map<String, Object>> profile() {
        SysUser user = userService.selectUserById(getUserId());
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("user", user);
        ajax.put("roleGroup", userService.selectUserRoleGroup(user.getUserName()));
        ajax.put("postGroup", userService.selectUserPostGroup(user.getUserName()));
        return R.ok(ajax);
    }

    /**
     * 修改用户
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> updateProfile(@RequestBody SysUser user) {
        if (StringUtils.isNotEmpty(user.getPhonenumber())
            && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail())
            && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUserId(getUserId());
        user.setUserName(null);
        user.setPassword(null);
        user.setAvatar(null);
        user.setDeptId(null);
        if (userService.updateUserProfile(user) > 0) {
            return R.ok();
        }
        return R.fail("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     *
     * @param newPassword 旧密码
     * @param oldPassword 新密码
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public R<Void> updatePwd(String oldPassword, String newPassword) {
        if (!newPassword.matches("/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[\\W_]).+$/")
            || newPassword.length() <= 8
            || newPassword.length() >= 20) {
            return R.fail("用户新密码长度必须介于 8 和 20 之间且必须同时包含大小写英文字母、数字和特殊符号");
        }

        SysUser user = userService.selectUserById(LoginHelper.getUserId());
        String userName = user.getUserName();
        String password = user.getPassword();
        if (!BCrypt.checkpw(oldPassword, password)) {
            return R.fail("修改密码失败，旧密码错误");
        }
        if (BCrypt.checkpw(newPassword, password)) {
            return R.fail("新密码不能与旧密码相同");
        }

        String newPwdHash = BCrypt.hashpw(newPassword);

        if (userService.resetUserPwd(userName, newPwdHash) > 0) {

            HashMap<String, String> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put(newPassword, newPwdHash);

            sysUserEntityService.cachePassword(stringStringHashMap);

            return R.ok();
        }
        return R.fail("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     *
     * @param avatarfile 用户头像
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/avatar", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Map<String, Object>> avatar(@RequestPart("avatarfile") MultipartFile avatarfile) {
        Map<String, Object> ajax = new HashMap<>();
        if (!avatarfile.isEmpty()) {
            String extension = FileUtil.extName(avatarfile.getOriginalFilename());
            if (!StringUtils.equalsAnyIgnoreCase(extension, MimeTypeUtils.IMAGE_EXTENSION)) {
                return R.fail("文件格式不正确，请上传" + Arrays.toString(MimeTypeUtils.IMAGE_EXTENSION) + "格式");
            }
            SysOss oss = iSysOssService.upload(avatarfile);
            String avatar = oss.getUrl();
            if (userService.updateUserAvatar(getUsername(), avatar)) {
                ajax.put("imgUrl", avatar);
                return R.ok(ajax);
            }
        }
        return R.fail("上传图片异常，请联系管理员");
    }

    @PostMapping(value = "/sendSms")
    @ApiOperation(value = "发送验证码")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<Object>> sendSms(String Phone) {
        String randomNumbers = RandomUtil.randomNumbers(6);
        String s = SmsUtil.sendCodeSMS(Phone, randomNumbers);
        RedisUtils.setCacheObject(VERIFICATION_CODE + Phone, randomNumbers);
        System.out.println(s);
        if (s.equals("fail")) {
            return R.fail();
        }
        return R.ok();
    }

    /**
     * 修改密码验证
     */
    @PostMapping("/updatePasswordCheck")
    public R<Void> updatePasswordCheck(@RequestBody ChangeUserBo changeUserBo) {
        String phoneNumber = changeUserBo.getPhoneNumber();
        String verificationCode = changeUserBo.getVerificationCode();
        String password = changeUserBo.getPassword();

        if (!SmsUtil.checkVerificationCode(phoneNumber, verificationCode)) {
            return R.fail("验证码错误或已经失效");
        }

        SysUser user = userService.selectUserById(LoginHelper.getUserId());
        if (!BCrypt.checkpw(password, user.getPassword())) {
            return R.fail("原密码错误");
        }

        return R.ok();
    }


    @PostMapping("/updatePhoneNumberCheck")
    public R<Void> updatePhoneNumberCheck(@RequestBody ChangeUserBo changeUserBo) {
        String phoneNumber = changeUserBo.getPhoneNumber();
        String verificationCode = changeUserBo.getVerificationCode();
        if (!SmsUtil.checkVerificationCode(phoneNumber, verificationCode)) {
            return R.fail("验证码错误或已经失效");
        }

        return R.ok();
    }


    /**
     * 修改密码
     */
    @Log(title = "个人中心", businessType = BusinessType.UPDATE)
    @PostMapping("/changePassword")
    public R<Void> changePassword(@ApiParam @RequestBody ChangeUserBo changeUserBo) {
        String phoneNumber = changeUserBo.getPhoneNumber();
        String verificationCode = changeUserBo.getVerificationCode();
        String password = changeUserBo.getPassword();
        if (!password.matches("/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[\\W_]).+$/")
            || password.length() <= 8
            || password.length() >= 20) {
            return R.fail("用户新密码长度必须介于 8 和 20 之间且必须同时包含大小写英文字母、数字和特殊符号");
        }
        SysUser user = new SysUser();
        user.setPassword(BCrypt.hashpw(password));
        user.setUserId(LoginHelper.getUserId());
        boolean result = userService.changePassword(user);
        if (result) {

            HashMap<String, String> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put(password, user.getPassword());

            sysUserEntityService.cachePassword(stringStringHashMap);

            return R.ok("修改密码成功");
        }
        return R.fail("修改密码异常，请联系管理员");
    }

    /**
     * 修改手机号
     */
    @Log(title = "个人中心", businessType = BusinessType.UPDATE)
    @PostMapping("/changePhone")
    public R<Void> changePhone(@ApiParam @RequestBody ChangeUserBo changeUserBo) {
        String phoneNumber = changeUserBo.getPhoneNumber();
        String verificationCode = changeUserBo.getVerificationCode();
        if (!(phoneNumber.length() == 11)) {
            return R.fail("新手机号长度异常");
        }

        if (!SmsUtil.checkVerificationCode(phoneNumber, verificationCode)) {
            return R.fail("新手机验证码错误或已经失效");
        }

        SysUser user = new SysUser();
        user.setPhonenumber(phoneNumber);
        user.setUserId(LoginHelper.getUserId());
        boolean result = userService.changePhoneNumber(user);
        if (result) {
            return R.ok("修改手机成功");
        }
        return R.fail("修改手机异常，请联系管理员");
    }
}
