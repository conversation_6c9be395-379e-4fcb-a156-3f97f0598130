package com.exam.domain.vo;

import com.exam.constant.CommonDataBaseConst;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 纠纷申诉VO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="StDisputeComplaintDetailVO对象", description="纠纷申诉VO")
public class StComplaintVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty(value = "申诉类型：1-纠纷申诉、2-强制交卷申诉")
    private Integer type;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "考试名称")
    private String examName;

    @ApiModelProperty(value = "申诉内容")
    private String content;

    @ApiModelProperty(value = "提醒次数")
    private Integer warnCount;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "申诉时间")
    private String disputeTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成时间")
    private String completeTime;

    @ApiModelProperty(value = "状态")
    private Integer disputeStatus;

    @ApiModelProperty(value = "状态名称")
    private String disputeStatusName;

    public String getDisputeStatusName() {
        if(type.equals(CommonDataBaseConst.COMPLAINT_TYPE.DISPUTE.getCode())) {
            return CommonDataBaseConst.DISPUTE_STATUS.getMap().get(disputeStatus);
        }else {
            return CommonDataBaseConst.COMPLAINT_STATUS.getMap().get(disputeStatus);
        }
    }

}
