package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 组群业务对象 st_group
 *
 * <AUTHOR>
 * @date 2023-11-07
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StGroupBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 父id
     */
    @NotNull(message = "父id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long fatherId;

    /**
     * 组群名称
     */
    @NotBlank(message = "组群名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String groupName;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;


}
