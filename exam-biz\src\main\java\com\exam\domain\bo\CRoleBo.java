package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 角色业务对象 c_role
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class CRoleBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String code;

    /**
     * 所属公司ID
     */
    @NotNull(message = "所属公司ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long companyId;

    /**
     * 角色类型（0：默认角色；1：自定义角色；）
     */
    @NotNull(message = "角色类型（0：默认角色；1：自定义角色；）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long type;

    /**
     * 序号
     */
    @NotNull(message = "序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sn;

    /**
     * 所属公司编码
     */
    @NotBlank(message = "所属公司编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @NotNull(message = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dataStatus;

    /**
     * 节点编码
     */
    @NotBlank(message = "节点编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgCode;

    /**
     * 是否为主岗位(0：否；1：是)
     */
    @NotNull(message = "是否为主岗位(0：否；1：是)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isMainPost;

    /**
     * 父节点ID
     */
    @NotNull(message = "父节点ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fatherId;

    /**
     * 创建人ID
     */
    @NotNull(message = "创建人ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createUserId;

    /**
     * 创建时间
     */
    @NotBlank(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String createTimeStr;

    /**
     * 编辑人ID
     */
    @NotNull(message = "编辑人ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateUserId;

    /**
     * 编辑时间
     */
    @NotBlank(message = "编辑时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String updateTimeStr;

    /**
     * 上级节点编码
     */
    @NotBlank(message = "上级节点编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentOrgCode;

    /**
     * 是否为外部岗位(0：否；1：是；)
     */
    @NotNull(message = "是否为外部岗位(0：否；1：是；)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isExternal;

    /**
     * 角色级别（1：局级；2：处级；3：项目；）
     */
    @NotNull(message = "角色级别（1：局级；2：处级；3：项目；）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long roleLevel;


}
