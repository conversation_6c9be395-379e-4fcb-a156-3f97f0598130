package com.exam.domain.bo;

import com.exam.domain.vo.StQuestionKeyWordVo;
import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 试题业务对象 st_question
 *
 * <AUTHOR>
 * @date 2023-10-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StQuestionBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long courseId;

    /**
     * 试题类型
     */
    @NotNull(message = "试题类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer questionGenre;

    /**
     * 题干
     */
    @NotBlank(message = "题干不能为空", groups = {AddGroup.class, EditGroup.class})
    private String questionContent;

    /**
     * 选项内容
     */
    @NotBlank(message = "选项内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String optionContent;

    /**
     * 正确答案
     */
    @NotBlank(message = "正确答案不能为空", groups = {AddGroup.class, EditGroup.class})
    private String rightKey;

    /**
     * 解析
     */
    @NotBlank(message = "解析不能为空", groups = {AddGroup.class, EditGroup.class})
    private String analysis;

    /**
     * 试题类别
     */
    @NotNull(message = "试题类别不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long questionType;

    /**
     * 图片路径
     */
    @NotBlank(message = "图片路径不能为空", groups = {AddGroup.class, EditGroup.class})
    private String picPath;

    /**
     * 试题序号
     */
    @NotNull(message = "试题序号不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sn;

    /**
     * 是否公开 1 公有 0 私有
     */
    @NotNull(message = "是否公开 1 公有 0 私有不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long isOpen;

    /**
     * 是否有关键词 1 是 0 否
     */
    @NotNull(message = "是否有关键词 1 是 0 否不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long isKeyWord;

    /**
     * 背景资料
     */
    @NotBlank(message = "背景资料不能为空", groups = {AddGroup.class, EditGroup.class})
    private String background;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;

    /**
     * 所属项目
     */
    @NotNull(message = "所属项目不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;

    /**
     * 关键词
     */
    private List<StQuestionKeyWordVo> keyWordList;

    /**
     * 随机选项
     */
    private String randomOptionMap = "";

    /**
     * 数据权限（0，只读；1，可编辑）
     */
    private String dataPower;

    /**
     * 不等于ID
     */
    private Long notId;

    /**
     * 不包含的 试题 ID 列表
     */
    private List<String> notIds;
}
