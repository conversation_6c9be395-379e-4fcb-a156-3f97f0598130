package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 试题关键词业务对象 st_question_key_word
 *
 * <AUTHOR>
 * @date 2023-10-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StQuestionKeyWordBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 试题id
     */
    @NotNull(message = "试题id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long questionId;

    /**
     * 关键词内容
     */
    @NotBlank(message = "关键词内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String keyWordContent;

    /**
     * 分值占比
     */
    @NotNull(message = "分值占比不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long scoreProportion;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;

    /**
     * 所属项目
     */
    @NotNull(message = "所属项目不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;


}
