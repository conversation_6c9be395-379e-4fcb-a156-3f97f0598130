package com.exam.common.utils;

import java.util.concurrent.*;

public final class ThreadPoolUtil {
    // 私有构造函数，防止实例化
    private ThreadPoolUtil() {}
    // CPU核心数
    private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();
    // 核心线程数：CPU核心数 × 5（IO密集型任务）
    private static final int CORE_POOL_SIZE = CPU_COUNT * 5;

    // 最大线程数：CPU核心数 × 10（应对突发峰值）
    private static final int MAX_POOL_SIZE = CPU_COUNT * 10;
    // 空闲线程存活时间
    private static final long KEEP_ALIVE_TIME = 60L;
    // 时间单位
    private static final TimeUnit TIME_UNIT = TimeUnit.SECONDS;
    // 队列容量：根据系统资源和任务特性调整
    private static final int QUEUE_CAPACITY = 2000;

    // 默认线程池
    private static final ThreadPoolExecutor DEFAULT_EXECUTOR;

    static {
        DEFAULT_EXECUTOR = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            KEEP_ALIVE_TIME,
            TIME_UNIT,
            new LinkedBlockingQueue<>(QUEUE_CAPACITY),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 设置核心线程允许超时
        DEFAULT_EXECUTOR.allowCoreThreadTimeOut(true);
    }

    /**
     * 执行无返回值的任务
     * @param task 任务
     */
    public static void execute(Runnable task) {
        DEFAULT_EXECUTOR.execute(task);
    }

    /**
     * 提交有返回值的任务
     * @param task 任务
     * @param <T>  返回值类型
     * @return Future 对象
     */
    public static <T> Future<T> submit(Callable<T> task) {
        return DEFAULT_EXECUTOR.submit(task);
    }

    /**
     * 提交无返回值的任务
     * @param task 任务
     * @return Future 对象
     */
    public static Future<?> submit(Runnable task) {
        return DEFAULT_EXECUTOR.submit(task);
    }

    /**
     * 关闭线程池
     */
    public static void shutdown() {
        DEFAULT_EXECUTOR.shutdown();
    }

    /**
     * 立即关闭线程池
     */
    public static void shutdownNow() {
        DEFAULT_EXECUTOR.shutdownNow();
    }

    /**
     * 判断线程池是否已关闭
     * @return true/false
     */
    public static boolean isShutdown() {
        return DEFAULT_EXECUTOR.isShutdown();
    }

    /**
     * 获取线程池当前状态
     * @return 状态信息
     */
    public static String getStatus() {
        return String.format("Pool Size: %d, Active Threads: %d, " +
                "Completed Tasks: %d, Total Tasks: %d, Queue Size: %d",
            DEFAULT_EXECUTOR.getPoolSize(),
            DEFAULT_EXECUTOR.getActiveCount(),
            DEFAULT_EXECUTOR.getCompletedTaskCount(),
            DEFAULT_EXECUTOR.getTaskCount(),
            DEFAULT_EXECUTOR.getQueue().size());
    }

    /**
     * 创建自定义线程池
     * @param corePoolSize    核心线程数
     * @param maxPoolSize     最大线程数
     * @param keepAliveTime   空闲线程存活时间
     * @param timeUnit        时间单位
     * @param queueCapacity   队列容量
     * @param threadFactory   线程工厂
     * @param rejectedHandler 拒绝策略
     * @return 线程池实例
     */
    public static ThreadPoolExecutor createCustomExecutor(
        int corePoolSize,
        int maxPoolSize,
        long keepAliveTime,
        TimeUnit timeUnit,
        int queueCapacity,
        ThreadFactory threadFactory,
        RejectedExecutionHandler rejectedHandler) {

        return new ThreadPoolExecutor(
            corePoolSize,
            maxPoolSize,
            keepAliveTime,
            timeUnit,
            new LinkedBlockingQueue<>(queueCapacity),
            threadFactory,
            rejectedHandler
        );
    }
//    public static void main(String[] args) {
//        // 执行无返回值任务
//        ThreadPoolUtil.execute(() -> {
//            System.out.println("执行简单任务");
//        });
//
//        // 执行有返回值任务
//        Future<String> future = ThreadPoolUtil.submit(() -> {
//            Thread.sleep(2000);
//            return "任务执行结果";
//        });
//
//        try {
//            System.out.println(future.get()); // 获取任务结果
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        // 获取线程池状态
//        System.out.println(ThreadPoolUtil.getStatus());
//
//        // 关闭线程池（通常在应用程序退出时调用）
//        Runtime.getRuntime().addShutdownHook(new Thread(ThreadPoolUtil::shutdown));
//    }
}
