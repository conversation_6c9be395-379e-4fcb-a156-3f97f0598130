package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 【请填写功能名称】业务对象 st_rotation_picture
 *
 * <AUTHOR>
 * @date 2023-12-08
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StRotationPictureBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 停留时间
     */
    @NotNull(message = "停留时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long residenceTime;

    /**
     * 自动适配
     */
    @NotNull(message = "自动适配不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long autoAdapt;

    /**
     * 图片地址（多个用，分割）
     */
    @NotBlank(message = "图片地址（多个用，分割）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String picPaths;

    /**
     * 区分（web,1;  APP,2）
     */
    @NotNull(message = "区分（web,1;  APP,2）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long differentiate;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;

    /**
     * 所属项目
     */
    @NotNull(message = "所属项目不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;


}
