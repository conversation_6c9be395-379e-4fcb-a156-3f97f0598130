<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.exam.mapper.SignUpMapper">
    <select id="getSignUpList" resultType="com.exam.domain.vo.SignUpVo">
        select a.id as examId,a.exam_name,a.apply_start_time,a.apply_end_time,a.start_time,a.end_time,
               a.create_time as releaseTime,count(b.id) as signUpCount,IFNULL(c.complete_flag,0) completeFlag,
               c.complete_time,a.admission_password
        from st_exam a
        left join st_user_exam b on a.id=b.exam_id and b.del_flag=0
        left join st_exam_sign_up c on a.id=c.exam_id and c.del_flag=0
        where a.del_flag=0 and a.tenant_id=#{tenantId}
        <if test="examName!=null and examName!=''">
            and a.exam_name like concat('%',#{examName},'%')
        </if>
        <if test="status!=null and status==1 ">
             and a.apply_start_time > now()
        </if>
        <if test="status!=null and status==2 ">
            and now() > a.apply_start_time  and a.apply_end_time > now() and (c.complete_flag is null or c.complete_flag=0)
        </if>
        <if test="status!=null and status==3 ">
            and  a.start_time > now() and c.complete_flag=1
        </if>
        <if test="status!=null and status==4 ">
            and now() > a.start_time  and a.end_time > now()
        </if>
        <if test="status!=null and status==5 ">
            and now() > a.end_time
        </if>
        GROUP BY
        examId,a.exam_name,a.apply_start_time,a.apply_end_time,a.start_time,a.end_time,releaseTime,completeFlag,c.complete_time,a.admission_password
    </select>

    <select id="getSignUpDetailList" resultType="com.exam.domain.vo.SignUpDetailVo">
        select b.user_id,c.rp_name as projectName,d.nick_name as userName,a.exam_name,d.phonenumber,d.id_number,
               e.name as examRoomName,b.exam_num,concat(f.name,g.name,h.name,e.address) as examRoomAddress,
               b.join_flag
        from st_exam a
        left join st_user_exam b on a.id=b.exam_id and b.del_flag=0
        left join railway_project c on c.rf_id=a.tenant_id
        left join sys_user d on b.user_id=d.user_id
        left join st_exam_room e on b.exam_room_id=e.id
        left join con_province f on e.province_id=f.id
        left join con_city g on e.city_id=g.id
        left join con_county h on e.county_id=h.id
        where a.del_flag=0 and a.id=#{examId} and a.tenant_id=#{tenantId}
        <if test="examRoomName!=null and examRoomName !=''">
            and e.name like concat('%',#{examRoomName},'%')
        </if>
        <if test="nameOrPhone!=null and nameOrPhone !=''">
            and (d.nick_name like concat('%',#{nameOrPhone},'%') or d.phonenumber like concat('%',#{nameOrPhone},'%') )
        </if>
        <if test="idNumber!=null and idNumber !=''">
            and d.id_number like concat('%',#{idNumber},'%')
        </if>
        <if test="joinFlag!=null">
            and b.join_flag=#{joinFlag}
        </if>
    </select>

    <delete id="deleteByExamId">
        delete from st_user_exam where exam_id =#{examId} and tenant_id=#{tenantId}
    </delete>

    <select id="getSignUpSmsInfoList" resultType="com.exam.domain.SignUpSmsInfoVo">
        select b.user_id,d.phonenumber,a.exam_name,a.start_time,a.end_time,
               e.name as examRoomName,b.exam_num,concat(f.name,g.name,h.name,e.address) as examRoomAddress,
               b.exam_num
        from st_exam a
                 left join st_user_exam b on a.id=b.exam_id and b.del_flag=0
                 left join sys_user d on b.user_id=d.user_id
                 left join st_exam_room e on b.exam_room_id=e.id
                 left join con_province f on e.province_id=f.id
                 left join con_city g on e.city_id=g.id
                 left join con_county h on e.county_id=h.id
        where a.del_flag=0 and a.user_id in (#{ids})
    </select>
</mapper>
