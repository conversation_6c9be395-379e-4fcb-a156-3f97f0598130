package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 试题类别抽题比例业务对象 st_question_proportion
 *
 * <AUTHOR>
 * @date 2023-11-03
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StQuestionProportionBo extends BaseEntity {

    /**
     * id
     */
    @NotBlank(message = "id不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 试题类别
     */
    @NotNull(message = "试题类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long questionTypeId;

    /**
     * 试题比例
     */
    @NotNull(message = "试题比例不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer questionProportion;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;

    /**
     * 所属项目
     */
    @NotNull(message = "所属项目不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;


}
