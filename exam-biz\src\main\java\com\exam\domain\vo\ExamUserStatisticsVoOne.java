package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * (国铁局)考试人员成绩统计(按考试区分)
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@ExcelIgnoreUnannotated
public class ExamUserStatisticsVoOne {

    /**
     * 考试id
     */
    private Long id;

    /**
     * 考试名称
     */
    @ExcelProperty(value = "考试名称",index = 0)
    private String examName;

    /**
     * 考试类别
     */
    @ExcelProperty(value = "考试类别",index = 1)
    private String examType;
    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名",index = 2)
    private String userName;
    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号码",index = 3)
    private String phoneNum;
    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号码",index = 4)
    private String idCardNum;
    /**
     * 租户id
     */

    private Long tenantId;
    /**
     * 所属企业
     */
    @ExcelProperty(value = "所属企业",index = 5)
    private String tenantName;

    /**
     *证书注册单位
     */
    @ExcelProperty(value = "证书注册单位",index = 6)
    private String certificateRegistrationUnit;
    /**
     * 参加考试时间
     */
    @ExcelProperty(value = "参加考试时间",index = 7)
    private Date examTime;
    /**
     * 分数
     */
    @ExcelProperty(value = "分数",index = 8)
    private String score;

    /**
     * 考试结果
     */
    @ExcelProperty(value = "结果",index = 9)
    private String examResult;

}
