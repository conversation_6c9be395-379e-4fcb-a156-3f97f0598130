package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗位属性业务对象 st_function_open
 *
 * <AUTHOR>
 * @date 2023-12-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StFunctionOpenBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 功能开放时间
     */
    @NotNull(message = "功能开放时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date startTime;

    /**
     * 功能结束时间
     */
    @NotNull(message = "功能结束时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date endTime;

    /**
     * 类型1：岗位属性，2：模拟考试
     */
    @NotNull(message = "类型1：岗位属性，2：模拟考试不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer type;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;

    /**
     * 所属项目
     */
    @NotNull(message = "所属项目不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;


}
