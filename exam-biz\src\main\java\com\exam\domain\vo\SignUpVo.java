package com.exam.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
public class SignUpVo {


    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty("考试id")
    private Long examId;

    /**
     *  考试名称
     */
    @ApiModelProperty("考试名称")
    private String examName;

    /**
     *  报名开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("报名开始时间")
    private LocalDateTime applyStartTime;
    /**
     * 报名结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("报名结束时间")
    private LocalDateTime applyEndTime;

    /**
     *  考试开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("考试开始时间")
    private LocalDateTime startTime;
    /**
     *  考试结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("考试结束时间")
    private LocalDateTime endTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("发布时间")
    private LocalDateTime releaseTime;

    @ApiModelProperty("报考人数")
    private int signUpCount;

    @ApiModelProperty("状态(1:未报名；2：已报名；3:报名结束；4：考试中；5：已完成)")
    private int status;

    @ApiModelProperty("状态名称")
    private String statusName;


    @ApiModelProperty("是否完成")
    private Integer completeFlag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("报名完成时间")
    private LocalDateTime completeTime;

    @ApiModelProperty("入场密码")
    private String admissionPassword;


    public int getStatus() {
        LocalDateTime now = LocalDateTime.now();
        if(now.isBefore(applyStartTime)){
            setStatusName("未报名");
            return 1;
        }
        if(now.isAfter(applyStartTime)&&now.isBefore(applyEndTime)&&(completeFlag==null||completeFlag==0)){
            setStatusName("报名中");
            return 2;
        }
        if(now.isBefore(startTime)&&completeFlag==1){
            setStatusName("报名结束");
            return 3;
        }
        if(now.isAfter(startTime)&&now.isBefore(endTime)){
            setStatusName("考试中");
            return 4;
        }
        if(now.isAfter(endTime)){
            setStatusName("已完成");
            return 5;
        }
        return status;
    }
}
