package com.exam.domain.vo;

import com.exam.common.helper.LoginHelper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <p>
 * 答题卡表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AchievemenVo", description="成绩列表vo")
public class AchievemenVo implements Serializable {

    @ApiModelProperty(value = "答题卡id")
    private String answerCardId;

    @ApiModelProperty(value = "答题卡id")
    private String answerCardIdStr;

    @ApiModelProperty(value = "成绩证书id")
    private String achievemenCertificateId;

    @ApiModelProperty(value = "考试id")
    private String examId;

    @ApiModelProperty(value = "考试名称")
    private String examName;

    @ApiModelProperty(value = "分数")
    private double score;

    @ApiModelProperty(value = "成绩评级")
    private String achievemenLevel;

    @ApiModelProperty(value = "补考次数")
    private Integer retestTimes;

    @ApiModelProperty(value = "创建人id")
    private String userId;

    @ApiModelProperty(value = "所属公司id")
    private String companyId;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "部门")
    private String deptName;

    @ApiModelProperty(value = "职务")
    private String jobName;

    @ApiModelProperty(value = "所属组织")
    private String organization;

    @ApiModelProperty(value = "参加考试日期")
    private String createTime;

    @ApiModelProperty(value = "用户名")
    private String tabUname;

    @ApiModelProperty(value = "用户组名称")
    private String groupName;

    @ApiModelProperty(value = "排名")
    private int rowno;

    @ApiModelProperty(value = "分数一样排名一样")
    private int rankno;

    @ApiModelProperty(value = "是否查看答案解析(1.是；0.否)")
    private String isAnswerAnalysis;

    public String getOrganization() {
        String str = "";
        if(!StringUtils.isBlank(companyName)){
            str = companyName;
        }
        if(!StringUtils.isBlank(deptName)){
            str = str+"/"+deptName;
        }
        if(!StringUtils.isBlank(jobName)){
            str = str+"/"+jobName;
        }
        return str;
    }


//    public String getTabUname() {
//        if(StringUtils.isBlank(userId)){
//            return "";
//        }
//        return UserHelpBean.getUserNameById(Long.parseLong(userId));
//    }

    @ApiModelProperty(value = "成绩级别显示")
    public String getAchievemenLevelDis() {
        return achievemenLevel;
    }
}
