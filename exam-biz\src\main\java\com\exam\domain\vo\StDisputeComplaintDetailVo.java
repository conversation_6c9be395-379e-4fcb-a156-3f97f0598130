package com.exam.domain.vo;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 纠纷申诉详情视图对象 st_dispute_complaint_detail
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StDisputeComplaintDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    @ApiModelProperty(value = "试题id")
    private Long questionId;

    @ApiModelProperty(value = "试题类型")
    private Integer questionGenre;

    @ApiModelProperty(value = "题干")
    private String questionContent;

    @ApiModelProperty(value = "选项内容")
    private String optionContent;

    @ApiModelProperty(value = "正确答案")
    private String rightKey;

    @ApiModelProperty(value = "答案")
    private String answer;

    @ApiModelProperty(value = "解析")
    private String analysis;

    @ApiModelProperty(value = "状态")
    private Integer disputeStatus;

    @ApiModelProperty(value = "随机选项对应map")
    private String randomOptionMap;


}
