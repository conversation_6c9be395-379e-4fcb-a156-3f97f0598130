package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户token业务对象 c_token
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class CTokenBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userInfoId;

    /**
     * 用户token
     */
    @NotBlank(message = "用户token不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userToken;

    /**
     * 设备ID
     */
    @NotBlank(message = "设备ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String uuid;

    /**
     * 设备注册ID
     */
    @NotBlank(message = "设备注册ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String registrationId;

    /**
     * 设备类型（-1:web;0：IOS、1：Andorid、2:小程序）
     */
    @NotNull(message = "设备类型（-1:web;0：IOS、1：Andorid、2:小程序）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deviceType;

    /**
     * token有效期
     */
    @NotNull(message = "token有效期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date availableTime;

    /**
     * 登录时间
     */
    @NotNull(message = "登录时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date loginTime;

    /**
     * 登录时间(显示用)
     */
    @NotBlank(message = "登录时间(显示用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String loginTimeStr;


}
