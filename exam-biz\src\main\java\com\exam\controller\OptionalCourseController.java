package com.exam.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.core.domain.R;
import com.exam.domain.vo.StClasshourVo;
import com.exam.domain.vo.StPlayProgresOptionalVo;
import com.exam.domain.vo.UserCourseCategoryVO;
import com.exam.service.OptionalCourseService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName OptionalCourseController
 * @Description 课程自选应用端
 * <AUTHOR>
 * @Date 2022/3/14 13:38 下午
 * @Version 1.0
 */

@ApiSort(value = 3)
@RequestMapping("OptionalCourseController")
@RestController
@Api(tags = "OptionalCourseController(自选课程)")
public class OptionalCourseController {
    private static final Logger logger = LoggerFactory.getLogger(com.exam.controller.OptionalCourseController.class);
    @Autowired
    OptionalCourseService optionalCourseService;

    @ApiOperationSupport(order = 1)
    @GetMapping(value = "/selectUserOptionalCourseList")
    @ApiOperation(value = "1.1 查询用户选择课程类别列表")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<UserCourseCategoryVO>> selectUserOptionalCourseList() {
        List<UserCourseCategoryVO> userOptionalCourseList = optionalCourseService.selectUserOptionalCourseList();
        return R.ok(userOptionalCourseList);
    }
    @ApiOperationSupport(order = 2)
    @GetMapping(value = "/getUserOptionalCourseInfo")
    @ApiOperation(value = "1.2 查询用户对应的课程类别详细")
    @ApiImplicitParams({@ApiImplicitParam(name = "categoryId", value = "课程类别id", paramType = "query", required = true, dataType = "long")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<UserCourseCategoryVO> getUserOptionalCourseInfo(long categoryId) {
        UserCourseCategoryVO userCourseCategoryVO = optionalCourseService.getUserOptionalCourseInfo(categoryId);
        return R.ok(userCourseCategoryVO);
    }

    @ApiOperationSupport(order = 3)
    @PostMapping(value = "/saveUserOptionalPlayProgres")
    @ApiOperation(value = "1.3 保存用户播放时长")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<String> saveUserOptionalPlayProgres(@ApiParam @RequestBody StPlayProgresOptionalVo StPlayProgresOptionalVo) {
        return R.ok(String.valueOf(optionalCourseService.saveUserOptionalPlayProgres(StPlayProgresOptionalVo)));
    }

    @ApiOperationSupport(order = 4)
    @GetMapping(value = "/getUserOptionalClasshourInfo")
    @ApiOperation(value = "1.4 查询用户课时详细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "classhourId", value = "课时id", paramType = "query", dataType = "long")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<StClasshourVo> getUserOptionalClasshourInfo(long classhourId) {
        StClasshourVo StClasshourVo = optionalCourseService.getUserOptionalClasshourInfo(classhourId);
        return R.ok(StClasshourVo);
    }

    @ApiOperationSupport(order = 5)
    @GetMapping(value = "/selectUserOptionalAllCourse")
    @ApiOperation(value = "1.5 查询一级类别下用户所有课程")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "categoryId", value = "课程类别", paramType = "query", dataType = "String")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<UserCourseCategoryVO> selectUserOptionalAllCourse(String categoryId,Integer size, Integer current) {
        return R.ok(optionalCourseService.selectUserOptionalAllCourse(categoryId,new Page<>(current, size)));
    }

    @ApiOperationSupport(order = 6)
    @GetMapping(value = "/getUserClasshourListByCourseId")
    @ApiOperation(value = "1.6 通过课程id查询客户课时列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId", value = "课程id", paramType = "query", dataType = "String")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<StClasshourVo>> getUserClasshourListByCourseId(String courseId) {
        List<StClasshourVo> StClasshourVolist = optionalCourseService.getUserClasshourListByCourseId(courseId);
        return R.ok(StClasshourVolist);
    }

    @ApiOperationSupport(order = 7)
    @GetMapping(value = "/getCurrClasshourByCategoryId")
    @ApiOperation(value = "1.7 通过课程类别id查询客户当前学习的课时")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "categoryId", value = "课程类别id", paramType = "query", dataType = "String")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<StClasshourVo> getCurrClasshourByCategoryId(String categoryId) {
        StClasshourVo StClasshourVo = optionalCourseService.getCurrClasshourByCategoryId(categoryId);
        return R.ok(StClasshourVo);
    }


    @ApiOperationSupport(order = 8)
    @GetMapping(value = "/getCurrClasshourByClassId")
    @ApiOperation(value = "1.8 通过课程id查询客户当前学习的课时")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId", value = "课程id", paramType = "query", dataType = "String")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<StClasshourVo> getCurrClasshourByClassId(String courseId) {
        StClasshourVo StClasshourVo = optionalCourseService.getCurrClasshourByClassId(courseId);
        return R.ok(StClasshourVo);
    }
}

