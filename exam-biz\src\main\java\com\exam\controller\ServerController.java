package com.exam.controller;

import com.exam.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/server")
@RestController
@Api(tags = "ServerController")
public class ServerController {


    /**
     * 获取系统时间
     *
     * @return ResultBody
     * <AUTHOR>
     * @date 2021/6/21
     */
    @ApiOperation(value = "获取系统时间", notes = "获取系统时间")
    @GetMapping(value = "getServerTime")
    public R<Long> getServerTime() {
        return R.ok(System.currentTimeMillis());
    }
}
