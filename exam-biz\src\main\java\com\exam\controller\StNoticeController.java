package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StNoticeBo;
import com.exam.domain.vo.StNoticeVo;
import com.exam.service.IStNoticeService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 公告
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/notice")
public class StNoticeController extends BaseController {

    private final IStNoticeService iStNoticeService;

    /**
     * 查询公告列表
     */
    @SaCheckPermission("exam:notice:list")
    @GetMapping("/list")
    public TableDataInfo<StNoticeVo> list(StNoticeBo bo, PageQuery pageQuery) {
        return iStNoticeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出公告列表
     */
    @SaCheckPermission("exam:notice:export")
    @Log(title = "公告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StNoticeBo bo, HttpServletResponse response) {
        List<StNoticeVo> list = iStNoticeService.queryList(bo);
        ExcelUtil.exportExcel(list, "公告", StNoticeVo.class, response);
    }

    /**
     * 获取公告详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:notice:query")
    @GetMapping("/{id}")
    public R<StNoticeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStNoticeService.queryById(id));
    }

    /**
     * 新增公告
     */
    @SaCheckPermission("exam:notice:add")
    @Log(title = "公告", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StNoticeBo bo) {
        return toAjax(iStNoticeService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改公告
     */
    @SaCheckPermission("exam:notice:edit")
    @Log(title = "公告", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StNoticeBo bo) {
        return toAjax(iStNoticeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除公告
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:notice:remove")
    @Log(title = "公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStNoticeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
