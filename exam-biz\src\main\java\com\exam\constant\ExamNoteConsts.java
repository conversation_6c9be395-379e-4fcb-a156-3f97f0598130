package com.exam.constant;


import com.exam.domain.StExam;
import java.util.ArrayList;
import java.util.List;
import jodd.util.StringUtil;

public class ExamNoteConsts {

    public static final String INIT = "请在规定时间作答，超出时间自动交卷。";
    public static final String IS_FACE_RECOGNITION = "本场考试已开启考前身份认证，请在个人中心上传证身份证照片，并保证电脑摄像头可用，若人脸识别不通过，请重新上传身份证照片，注意证件人像处是否有污渍，反光等遮挡，上传时要按照示例图片横版上传，拍照时注意周围环境，保证光线充足，面部可见。";
    public static final String IS_VIDEO_SURVEILLANCE = "本场考试已开启人脸对比，请全程保持人脸在左上角的人像区域中，防止其他原因造成违规。";
    public static final String IS_SWITCH_PAGES = "本场考试已禁止切换页面，考试时，请在考试页面操作，禁止切换其他窗口，菜单，最小化浏览器。请不要设置电脑屏幕保护，避免休眠后重新启动造成页面切换。";
    public static final String IS_FORCE_FULL_SCREEN = "本场考试已开启强制全屏，考试时浏览器全屏显示，请不要退出全屏（包括使用快捷键退出和切出浏览器），禁止切换到其他扩展屏幕操作。";
    public static final String IS_CLIENT = "本场考试已开启客户端考试，客户端将禁止操作其他软件，考试结束后自动关闭，禁止考试过程中连接多个屏幕。";
    public static final String IS_DYNAMIC_SUPERVISORY = "本场考试已开启动态监考，请不要做低头、抬头、摆头等小动作，不要离开人像区域，不要在人多的环境考试，若人像区域检测到其他人脸，按违规处理。";
    public static final String IS_ANSWER_ANALYSIS = "本场考试已开启查看答案解析功能，考生可在“成绩查询-答案解析”处查看正误情况和答案解析。";
    public static final String IS_DISPUTE_COMPLAINT = "本场考试已开启纠分申诉功能，考生可在“成绩查询-答案解析”处，将误判试题发起纠分申诉，通过后成绩重新核算。";
    public static final String IS_COMPULSION_COMPLAINT = "本场考试已开启考生申诉功能，考试过程中发生意外可根据实际情况在“个人中心-考生申诉”发起申诉，申诉通过后可点击“考试中心-去考试”按钮继续考试。";

    public static String[] getNotes(StExam stExam) {
        List<String> noteList = new ArrayList<>();
        int i = 1;
        noteList.add(getNoteStr(i++, ExamNoteConsts.INIT));

        if(StringUtil.equals(stExam.getIsFaceRecognition(), "1")) {
            noteList.add(getNoteStr(i++, ExamNoteConsts.IS_FACE_RECOGNITION));
        }

        if(StringUtil.equals(stExam.getIsVideoSurveillance(), "1")) {
            noteList.add(getNoteStr(i++, ExamNoteConsts.IS_VIDEO_SURVEILLANCE));
        }

        if(StringUtil.equals(stExam.getIsSwitchPages(), "1")) {
            noteList.add(getNoteStr(i++, ExamNoteConsts.IS_SWITCH_PAGES));
        }

        if(StringUtil.equals(stExam.getIsForceFullScreen(), "1")) {
            noteList.add(getNoteStr(i++, ExamNoteConsts.IS_FORCE_FULL_SCREEN));
        }

        if(StringUtil.equals(stExam.getIsClient(), "1")) {
            noteList.add(getNoteStr(i++, ExamNoteConsts.IS_CLIENT));
        }

        if(StringUtil.equals(stExam.getIsDynamicSupervisory(), "1")) {
            noteList.add(getNoteStr(i++, ExamNoteConsts.IS_DYNAMIC_SUPERVISORY));
        }

        if(StringUtil.equals(stExam.getIsAnswerAnalysis(), "1")) {
            noteList.add(getNoteStr(i++, ExamNoteConsts.IS_ANSWER_ANALYSIS));
        }

        if(StringUtil.equals(stExam.getIsDisputeComplaint(), "1")) {
            noteList.add(getNoteStr(i++, ExamNoteConsts.IS_DISPUTE_COMPLAINT));
        }

        if(StringUtil.equals(stExam.getIsCompulsionComplaint(), "1")) {
            noteList.add(getNoteStr(i++, ExamNoteConsts.IS_COMPULSION_COMPLAINT));
        }
        return noteList.toArray(new String[0]);
    }

    private static String getNoteStr(int index, String note) {
        return index + "." + note;
    }
}
