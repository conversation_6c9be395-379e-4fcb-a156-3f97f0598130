package com.exam.domain.vo;

import java.util.Date;
import java.util.Map;

import com.exam.constant.CommonDataBaseConst;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 纠纷申诉视图对象 st_dispute_complaint
 *
 * <AUTHOR>
 * @date 2023-11-30
 */
@Data
@ExcelIgnoreUnannotated
public class StDisputeComplaintVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;
    /**
     * 考试名称
     */
    @ApiModelProperty(value = "考试名称")
    private String examName;
    /**
     * 考试时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "考试时间")
    private String examTime;
    /**
     * 提醒次数
     */
    @ApiModelProperty(value = "提醒次数")
    private Integer warnCount;
    /**
     * 考试开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "考试开始时间")
    private String examStartTime;
    /**
     * 考试结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value = "考试结束时间")
    private String examEndTime;
    /**
     * 申诉时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "申诉时间")
    private String disputeTime;
    /**
     * 完成时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成时间")
    private String completeTime;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer disputeStatus;
    /**
     * 题型Map
     */
    @ApiModelProperty(value = "题型Map")
    private Map<Integer, StDisputeComplaintDetailVo> stDisputeComplaintDetailVOMap;
    /**
     * 状态名称
     */
    @ApiModelProperty(value = "状态名称")
    private String disputeStatusName;
    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 租户id
     */
    private Long tenantId;

    public String getDisputeStatusName() {
        return CommonDataBaseConst.DISPUTE_STATUS.getMap().get(disputeStatus);
    }

    public String getExamTime() {
        return this.getExamStartTime() + "~" + this.getExamEndTime();
    }
}
