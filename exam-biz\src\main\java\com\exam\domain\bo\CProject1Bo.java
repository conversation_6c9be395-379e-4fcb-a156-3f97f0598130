package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 项目业务对象 c_project1
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class CProject1Bo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 项目名称(公司名称)
     */
    @NotBlank(message = "项目名称(公司名称)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyName;

    /**
     * 项目编码(公司编码)，与归属公司有层级关系
     */
    @NotBlank(message = "项目编码(公司编码)，与归属公司有层级关系不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;

    /**
     * 归属公司的编码
     */
    @NotBlank(message = "归属公司的编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentCompanyCode;

    /**
     * 项目编码(公司编码),展示所用
     */
    @NotBlank(message = "项目编码(公司编码),展示所用不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyNum;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long longitude;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long latitude;

    /**
     * 所在区县ID
     */
    @NotNull(message = "所在区县ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long countyId;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String location;

    /**
     * 合同开工日期
     */
    @NotNull(message = "合同开工日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 合同完成日期
     */
    @NotNull(message = "合同完成日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endTime;

    /**
     * 所属公司ID
     */
    @NotNull(message = "所属公司ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long companyId;

    /**
     * 中标单位公司编码
     */
    @NotBlank(message = "中标单位公司编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bidWinnerCompanyCode;

    /**
     * 工程类别
     */
    @NotBlank(message = "工程类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectCategory;

    /**
     * 中标时间
     */
    @NotBlank(message = "中标时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String winningTheBidTime;

    /**
     * 中标合同金额
     */
    @NotNull(message = "中标合同金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal winningTheBidContractM;

    /**
     * 管理模式
     */
    @NotBlank(message = "管理模式不能为空", groups = { AddGroup.class, EditGroup.class })
    private String managementMode;

    /**
     * 合同工期
     */
    @NotNull(message = "合同工期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer contractConstructionPeriod;

    /**
     * 建设单位
     */
    @NotBlank(message = "建设单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String buildCompany;

    /**
     * 监理单位
     */
    @NotBlank(message = "监理单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supervisorCompany;

    /**
     * 设计单位
     */
    @NotBlank(message = "设计单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String designCompany;

    /**
     * 隶属单位
     */
    @NotBlank(message = "隶属单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String subordinateCompany;

    /**
     * 投资分类
     */
    @NotBlank(message = "投资分类不能为空", groups = { AddGroup.class, EditGroup.class })
    private String investmentType;

    /**
     * 项目经理
     */
    @NotBlank(message = "项目经理不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectManager;

    /**
     * 项目经理联系方式
     */
    @NotBlank(message = "项目经理联系方式不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectManagerTel;

    /**
     * 计划负责人
     */
    @NotBlank(message = "计划负责人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String planManager;

    /**
     * 计划负责人联系方式
     */
    @NotBlank(message = "计划负责人联系方式不能为空", groups = { AddGroup.class, EditGroup.class })
    private String planManagerTel;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @NotNull(message = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dataStatus;

    /**
     * 更新人ID
     */
    @NotNull(message = "更新人ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateUserId;

    /**
     * 更新时间
     */
    @NotBlank(message = "更新时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String updateTimeStr;

    /**
     * 创建人
     */
    @NotNull(message = "创建人不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createUserId;

    /**
     * 项目描述
     */
    @NotBlank(message = "项目描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectDesc;

    /**
     * 项目简称
     */
    @NotBlank(message = "项目简称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectAbbreviation;

    /**
     * 项目类型
     */
    @NotBlank(message = "项目类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectType;

    /**
     * 节点编码
     */
    @NotBlank(message = "节点编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgCode;

    /**
     * 上级节点编码
     */
    @NotBlank(message = "上级节点编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentOrgCode;

    /**
     * org表id
     */
    @NotNull(message = "org表id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orgId;

    /**
     * 项目经理 手机号码 区号
     */
    @NotBlank(message = "项目经理 手机号码 区号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phoneNumberCode;


}
