package com.exam.domain.vo;

import java.util.Date;

import com.exam.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 播放进度视图对象 st_play_progres_optional
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StPlayProgresOptionalVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 课时id
     */
    @ExcelProperty(value = "课时id")
    private Long classhourId;

    /**
     * 播放时长
     */
    @ExcelProperty(value = "播放时长")
    private Long playDuration;

    /**
     * 进度
     */
    @ExcelProperty(value = "进度")
    private Long progres;

    /**
     * 是否删除(1.是；0.否)
     */
    @ExcelProperty(value = "是否删除(1.是；0.否)")
    private Integer delFlag;


    /**
     * 播放状态(1.已看完；0.未看完)
     */
    @ExcelProperty(value = "播放状态(1.已看完；0.未看完)")
    private Long playStatus;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long projectId;

}
