package com.exam.pubsub;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Redis发布者服务
 * 用于向指定的Redis频道发布消息
 */
@Slf4j
@Service
public class RedisPublisher {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 发布消息到Topic
     *
     * @param topicName Topic名称
     * @param message   消息体
     */
    public void publish(String topicName, String message) {
        RTopic topic = redissonClient.getTopic(topicName);
        topic.publish(message);
        log.info("消息已发布：频道 => {}, 消息内容 => {}", topicName, message);
    }
}
