package com.exam.common.utils;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ThreadPoolManager {
    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolManager.class);
    private static final ThreadPoolManager INSTANCE = new ThreadPoolManager();
    private final ScheduledExecutorService executorService;

    private ThreadPoolManager() {
        // 创建一个单线程的定时线程池
        executorService = Executors.newSingleThreadScheduledExecutor();
    }

    public static ThreadPoolManager getInstance() {
        return INSTANCE;
    }

    public void scheduleTask(Runnable task,long initialDelay,long period) {
        // 让任务每秒执行一次
        executorService.scheduleAtFixedRate(task, initialDelay, period, TimeUnit.SECONDS);
    }

    public void shutdown() {
        executorService.shutdown();
    }
}
