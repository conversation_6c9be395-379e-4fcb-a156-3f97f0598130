package com.exam.system.service.impl;

import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.exam.system.service.AsyncService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AsyncServiceImpl implements AsyncService {

    @Async
    @Override
    public <T, R> CompletableFuture<R> async(Function<T, R> fun, T t) {
        R r = fun.apply(t);
        return CompletableFuture.completedFuture(r);
    }

}
