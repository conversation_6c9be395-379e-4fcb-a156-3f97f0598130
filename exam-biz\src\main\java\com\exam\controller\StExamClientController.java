package com.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.annotation.Log;
import com.exam.common.core.domain.R;
import com.exam.common.enums.BusinessType;
import com.exam.common.helper.LoginHelper;
import com.exam.common.utils.redis.RedisUtils;
import com.exam.config.Configure;
import com.exam.constant.CommonDataBaseConst;
import com.exam.domain.vo.*;
import com.exam.service.IExamService;
import com.exam.utils.GyUtils;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 考试用户端(考试中心)
 *
 * <AUTHOR>
 * @date 2023-11-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/examClient")
public class StExamClientController {

    private final IExamService examService;

    //配置文件
    private final Configure configure;

    /**
     * 提交试卷
     * @return
     */
    //  @SaCheckPermission("exam:examClient:submitExamPapers")
    @ApiOperationSupport(order = 1)
    @PostMapping(value = "/submitExamPapers")
    @ApiOperation(value = "1.1 提交试卷")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
//    @Log(title = "考试中心", businessType = BusinessType.INSERT)
    public R<StAnswerCardVo> submitExamPapers(@ApiParam @RequestBody StAnswerCardVo stAnswerCardVO) {
        stAnswerCardVO.setTerminal("1");
        if(GyUtils.isNull(stAnswerCardVO.getExamId())){
            return R.fail("考试id为空！");
        }
        return R.ok(examService.submitExamPapers(stAnswerCardVO));
    }
    /**
     * 查询成绩列表
     * @return
     */
    //  @SaCheckPermission("exam:examClient:selectAchievemenList")
    @ApiOperationSupport(order = 2)
    @GetMapping(value = "/selectAchievemenList")
    @ApiOperation(value = "1.2 查询成绩列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "startDate", value = "开始考试日期", paramType = "query", required = false, dataType = "String")
        , @ApiImplicitParam(name = "size", value = "每页条数", paramType = "query", required = true, dataType = "Integer")
        , @ApiImplicitParam(name = "current", value = "当前页数", paramType = "query", required = true, dataType = "Integer")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<IPage<AchievemenListVo>> selectAchievemenList(Integer size, Integer current, String startDate) {
        return R.ok(examService.selectAchievemenList(new Page<>(current, size),startDate));
    }
//    /**
//     * 查看证书(弃用)
//     * @return
//     */
//    //  @SaCheckPermission("exam:examClient:selectCertificate")
//    @ApiOperationSupport(order = 3)
//    @GetMapping(value = "/selectCertificate")
//    @ApiOperation(value = "1.3 查看证书")
//    @ApiImplicitParams({
//        @ApiImplicitParam(name = "achievemenCertificateId", value = "成绩证书id", paramType = "query", dataType = "String")
//    })
//    @ApiResponse(code = 1000, message = "操作成功")
//    @ResponseBody
//    public R<StAchievemenCertificateVo> selectCertificate(Long achievemenCertificateId) {
//        return R.ok(examService.selectCertificate(achievemenCertificateId));
//    }
    /**
     * 查询个人成绩在当前考试的排名
     * @return
     */
    //  @SaCheckPermission("exam:examClient:selectGroupAchievemenList")
    @ApiOperationSupport(order = 4)
    @GetMapping(value = "/selectGroupAchievemenList")
    @ApiOperation(value = "1.4 查询个人成绩在当前考试的排名")
    @ApiImplicitParams({@ApiImplicitParam(name = "examId", value = "考试id", paramType = "query", required = false, dataType = "String")
        , @ApiImplicitParam(name = "userName", value = "姓名", paramType = "query", required = false, dataType = "String")
        , @ApiImplicitParam(name = "size", value = "每页条数", paramType = "query", required = true, dataType = "Integer")
        , @ApiImplicitParam(name = "current", value = "当前页数", paramType = "query", required = true, dataType = "Integer")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<IPage<AchievemenVo>> selectGroupAchievemenList(Integer size, Integer current, String examId, String userName) {
        return R.ok(examService.selectGroupAchievemenList(new Page<>(current, size),examId,userName,null,null));
    }
//    /**
//     * 查看是否可以考试(弃用)
//     * @return
//     */
//    //  @SaCheckPermission("exam:examClient:getExam")
//    @ApiOperationSupport(order = 5)
//    @GetMapping(value = "/getExam")
//    @ApiOperation(value = "1.5 查看是否可以考试")
//    @ApiResponse(code = 1000, message = "操作成功")
//    @ApiImplicitParams({
//        @ApiImplicitParam(name = "examId", value = "成绩证书id", paramType = "query", dataType = "String")
//    })
//    @ResponseBody
//    public R<String> getExam(String examId) {
//        return R.ok(examService.getExam(examId));
//    }
    /**
     * 查看个人考试列表
     * @return
     */
    //  @SaCheckPermission("exam:examClient:getUserExamList")
    @ApiOperationSupport(order = 6)
    @GetMapping(value = "/getUserExamList")
    @ApiOperation(value = "1.6 查看个人考试列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "startTime", value = "开始时间", paramType = "query", required = false, dataType = "String")
        , @ApiImplicitParam(name = "endTime", value = "结束时间", paramType = "query", required = false, dataType = "String")
        , @ApiImplicitParam(name = "examName", value = "考试名称", paramType = "query", required = false, dataType = "String")
        , @ApiImplicitParam(name = "size", value = "每页条数", paramType = "query", required = true, dataType = "Integer")
        , @ApiImplicitParam(name = "current", value = "当前页数", paramType = "query", required = true, dataType = "Integer")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<StExamUserVO>> getUserExamList(Integer size, Integer current, String startTime, String endTime, String examName) {
        return R.ok(examService.getUserExamList(size,current,startTime,endTime,examName));
    }

    //    @ApiOperationSupport(order = 7)
//    @PostMapping(value = "/submitExamPapersApp")
//    @ApiOperation(value = "1.7 提交试卷(App)")
//    @ApiResponse(code = 1000, message = "操作成功")
//    @ResponseBody
//    public R<Map> submitExamPapersApp(@ApiParam @RequestBody StAnswerCardVo stAnswerCardVO) {
//        if(GyUtils.isNull(stAnswerCardVO.getExamId())){
//            return R.fail("考试id为空！");
//        }
//        stAnswerCardVO.setTerminal("2");
//        stAnswerCardVO.setUserId(String.valueOf(UserHelpBean.getCurrentUserInfoId()));
//        return R.ok(examService.submitExamPapersApp(stAnswerCardVO));
//    }
    /**
     * 提交试卷(App)
     * @return
     */
    //  @SaCheckPermission("exam:examClient:submitExamPapersApp")
//    @ApiOperationSupport(order = 7)
//    @PostMapping(value = "/submitExamPapersApp")
//    @ApiOperation(value = "1.7 提交试卷(App)")
//    @ApiResponse(code = 1000, message = "操作成功")
//    @ResponseBody
//    @Log(title = "考试中心", businessType = BusinessType.INSERT)
//    public R<Map> submitExamPapersApp(@ApiParam @RequestBody StAnswerCardVo stAnswerCardVO) {
//        if(GyUtils.isNull(stAnswerCardVO.getExamId())){
//            return R.fail("考试id为空！");
//        }
//        stAnswerCardVO.setTerminal("2");
//        stAnswerCardVO.setUserId(LoginHelper.getUserId());
//        return R.ok(examService.submitExamPapersApp(stAnswerCardVO));
//    }
    /**
     * 开始考试(App)
     * @return
     */
    //  @SaCheckPermission("exam:examClient:startExamApp")
    @ApiOperationSupport(order = 8)
    @PostMapping(value = "/startExamApp")
    @ApiOperation(value = "1.8 开始考试(App)")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<String> startExamApp(@ApiParam @RequestBody StAnswerCardVo stAnswerCardVO) throws InterruptedException {
        if(GyUtils.isNull(stAnswerCardVO.getExamId())){
            return R.fail("考试id为空！");
        }
        stAnswerCardVO.setTerminal("2");
        return R.ok(examService.startExamApp(stAnswerCardVO));
    }
    /**
     * 查看试卷解析
     * @return
     */
    //  @SaCheckPermission("exam:examClient:selectExamPaper")
    @ApiOperationSupport(order = 9)
    @GetMapping(value = "/selectExamPaper")
    @ApiOperation(value = "1.9 查看试卷解析")
    @ApiResponse(code = 1000, message = "操作成功")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "answerCardId", value = "答题卡id", paramType = "query", dataType = "String")
    })
    @ResponseBody
    public R<Map<String,Object>>selectExamPaper(String answerCardId) {
        return R.ok(examService.selectExamPaper(answerCardId));
    }
//    /**
//     * 开始考试（弃用）
//     * @return
//     */
//    //  @SaCheckPermission("exam:examClient:startExam")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/startExam")
//    @ApiOperation(value = "1.10 开始考试")
//    @ApiResponse(code = 1000, message = "操作成功")
//    @ResponseBody
//    public R<String> startExam(@ApiParam @RequestBody StAnswerCardVo stAnswerCardVO) throws InterruptedException {
//        if(GyUtils.isNull(stAnswerCardVO.getExamId())){
//            return R.fail("考试id为空！");
//        }
//        stAnswerCardVO.setTerminal("1");
//        return R.ok(examService.startExam(stAnswerCardVO));
//    }
    /**
     * 判断强制申诉考试时间是否超时
     * @return
     */
    //  @SaCheckPermission("exam:examClient:compulsionExamTimeout")
    @ApiOperationSupport(order = 10)
    @GetMapping(value = "/compulsionExamTimeout")
    @ApiOperation(value = "1.15 判断强制申诉考试时间是否超时")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Integer > compulsionExamTimeout(Long examId) throws InterruptedException {
        if(GyUtils.isNull(examId)){
            return R.fail("考试id为空！");
        }
        Integer result = examService.compulsionExamTimeout(examId);
        return R.ok(result);
    }
    /**
     * 保存错误信息
     * @return
     */
    //  @SaCheckPermission("exam:examClient:saveWrongAct")
    @ApiOperationSupport(order = 11)
    @PostMapping(value = "/saveWrongAct")
    @ApiOperation(value = "1.11 保存错误信息")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody

    public R<String> saveWrongAct(@ApiParam @RequestBody StUserWrongActVo stUserWrongActVO)  {
        return R.ok(examService.saveWrongAct(stUserWrongActVO));
    }
    /**
     * 缓存试卷
     * @return
     */
    //  @SaCheckPermission("exam:examClient:cacheExamPapers")
    @ApiOperationSupport(order = 12)
    @PostMapping(value = "/cacheExamPapers")
    @ApiOperation(value = "1.12 缓存试卷")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Void> cacheExamPapers(@ApiParam @RequestBody StAnswerCardVo stAnswerCardVO) {
        if(GyUtils.isNull(stAnswerCardVO.getExamId())){
            return R.fail("考试id为空！");
        }
        examService.cacheExamPapers(stAnswerCardVO);
        return R.ok();
    }
    /**
     * 查询成绩列表
     * @return
     */
    //  @SaCheckPermission("exam:examClient:selectAchievemenListByComplaint")
    @ApiOperationSupport(order = 13)
    @GetMapping(value = "/selectAchievemenListByComplaint")
    @ApiOperation(value = "1.13 查询成绩列表")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<StExamVo>> selectAchievemenListByComplaint() {
        return R.ok(examService.selectAchievemenListByComplaint());
    }
    /**
     * 获取缓存试卷
     * @return
     */
    //  @SaCheckPermission("exam:examClient:getCacheExamPapers")
    @ApiOperationSupport(order = 14)
    @GetMapping(value = "/getCacheExamPapers")
    @ApiOperation(value = "1.14 获取缓存试卷")
    @ApiImplicitParams({@ApiImplicitParam(name = "examId", value = "examId", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "userId", value = "userId", paramType = "query", required = false, dataType = "String")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<StAnswerCardVo> getCacheExamPapers(String examId,String userId) {
        if(GyUtils.isNull(examId)){
            return R.fail("考试id为空！");
        }
        if(GyUtils.isNull(userId)){
            userId = String.valueOf(LoginHelper.getUserId());
        }
        return R.ok(examService.getCacheExamPapers(examId, Long.parseLong(userId)));
    }

    /**
     * 提交手动阅卷
     * @return
     */
    //  @SaCheckPermission("exam:examClient:submitManualReview")
    @PostMapping(value = "/submitManualReview")
    @ApiOperation(value = "1.15 提交手动阅卷")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<StAchievemenCertificateVo> submitManualReview(@ApiParam @RequestBody StAnswerCardVo stAnswerCardVO) {
        if(GyUtils.isNull(stAnswerCardVO.getExamId())){
            return R.fail("考试id为空！");
        }
        if(stAnswerCardVO.getReviewStatus().equals(CommonDataBaseConst.YES_OR_NO.YES.getCode())){
            return R.fail("该试卷已阅！");
        }
        stAnswerCardVO.setUserId(LoginHelper.getUserId());
        return R.ok(examService.submitManualReview(stAnswerCardVO));
    }
    /**
     * 获取考试前缓存试卷
     * @return
     */
    //  @SaCheckPermission("exam:examClient:getCacheExamPapersBefore")
    @GetMapping(value = "/getCacheExamPapersBefore")
    @ApiOperation(value = "1.16 获取考试前缓存试卷")
    @ApiImplicitParams({@ApiImplicitParam(name = "examId", value = "examId", paramType = "query", required = false, dataType = "String"),
        @ApiImplicitParam(name = "userId", value = "userId", paramType = "query", required = false, dataType = "String")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<Map<Integer, List<StQuestionVo>>> getCacheExamPapersBefore(String examId,String userId) {
        if(GyUtils.isNull(examId)){
            return R.fail("考试id为空！");
        }
        if(GyUtils.isNull(userId)){
            userId = String.valueOf(LoginHelper.getUserId());
        }
        String autoUserExamQuestionKey = "autoQueryUserExam" +":"+ examId +":"+ userId;
        Map<Integer, List<StQuestionVo>> map = (Map<Integer, List<StQuestionVo>>) RedisUtils.getCacheObject(autoUserExamQuestionKey);
        return R.ok(map);
    }
    /**
     * 查看证书
     * @return
     */
    //  @SaCheckPermission("exam:examClient:selectCertificate")
    @ApiOperationSupport(order = 17)
    @GetMapping(value = "/selectAchievemenDetail")
    @ApiOperation(value = "1.17 查看成绩详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "answerCardId", value = "答题卡id", paramType = "query", dataType = "String")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<StAnswerCardVo> selectAchievemenDetail(String answerCardId) {
        return R.ok(examService.selectAchievemenDetail(answerCardId));
    }
    /**
     * 下载客户端地址
     * @return
     */
    @GetMapping(value = "/downloadClientPath")
    @ResponseBody
    public R<StringBuffer> downloadClientPath() {
        String path = configure.getFileServerPath() + configure.getClientPath();
        StringBuffer buffer = new StringBuffer();
        buffer.append(path);
        return R.ok(buffer);
    }

}
