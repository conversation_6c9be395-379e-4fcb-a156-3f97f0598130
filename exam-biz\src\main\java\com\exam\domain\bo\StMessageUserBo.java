package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 用户消息关联业务对象 st_message_user
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StMessageUserBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userInfoId;

    /**
     * 对应消息id
     */
    private Long mainTableId;

    /**
     * 消息ID
     */
    private Long messageId;

    /**
     * 消息类型
     */
    private Integer msgDetailType;

    /**
     * 追加消息
     */
    private String appendMsg;

    /**
     * 状态（0：未读；1：已读；）
     */
    private Integer status;

    /**
     * 阅读时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date readTime;

    /**
     * 推送时间
     */
    @NotNull(message = "推送时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date pushTime;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date createTime;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createBy;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateBy;

    /**
     * 领域
     */
    @NotBlank(message = "领域不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;

    /**
     * 所属单位
     */
    @NotBlank(message = "所属单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;
}
