package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 试题类型对象 st_question_type
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_question_type")
public class StQuestionType extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 试题类别名称
     */
    private String questionTypeName;
    /**
     * 类别排序
     */
    private Long questionTypeSn;
    /**
     * 父id
     */
    private Long fatherId;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 所属项目
     */
    private Long projectId;

}
