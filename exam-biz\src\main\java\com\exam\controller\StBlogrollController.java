package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StBlogrollBo;
import com.exam.domain.vo.StBlogrollVo;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.service.IStBlogrollService;
import com.exam.common.core.page.TableDataInfo;

/**
 * 友情链接
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/blogroll")
public class StBlogrollController extends BaseController {

    private final IStBlogrollService iStBlogrollService;

    /**
     * 查询友情链接列表
     */
    @SaCheckPermission("exam:blogroll:list")
    @GetMapping("/list")
    public TableDataInfo<StBlogrollVo> list(StBlogrollBo bo, PageQuery pageQuery) {
        return iStBlogrollService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出友情链接列表
     */
    @SaCheckPermission("exam:blogroll:export")
    @Log(title = "友情链接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StBlogrollBo bo, HttpServletResponse response) {
        List<StBlogrollVo> list = iStBlogrollService.queryList(bo);
        ExcelUtil.exportExcel(list, "友情链接", StBlogrollVo.class, response);
    }

    /**
     * 获取友情链接详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:blogroll:query")
    @GetMapping("/{id}")
    public R<StBlogrollVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStBlogrollService.queryById(id));
    }

    /**
     * 新增友情链接
     */
    @SaCheckPermission("exam:blogroll:add")
    @Log(title = "友情链接", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StBlogrollBo bo) {
        return toAjax(iStBlogrollService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改友情链接
     */
    @SaCheckPermission("exam:blogroll:edit")
    @Log(title = "友情链接", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StBlogrollBo bo) {
        return toAjax(iStBlogrollService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除友情链接
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:blogroll:remove")
    @Log(title = "友情链接", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStBlogrollService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
