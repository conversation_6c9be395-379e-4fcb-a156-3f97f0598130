package com.exam.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.domain.StExamRoom;
import com.baomidou.mybatisplus.extension.service.IService;
import com.exam.domain.vo.ExamRoomVo;

import java.util.List;

/**
 * <p>
 * 考场表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface IStExamRoomService extends IService<StExamRoom> {

    IPage<ExamRoomVo> getExamRoomList(Page page, String name, Long provinceId, Long cityId, Long countyId, Long status);

    void saveExamRoom(StExamRoom stExamRoom);

    void updateExamRoom(StExamRoom stExamRoom);

    void enableExamRoom(Long id, Long status);

    List<ExamRoomVo> getNotOccupiedExamRoomList();
}
