<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.exam.mapper.StExamRoomMapper">

    <select id="getExamRoomList" resultType="com.exam.domain.vo.ExamRoomVo">
        select a.id,a.name,concat(b.name,c.name,d.name,a.address) as completeAddress,a.capacity,a.status,
               e.nick_name as createUserName,a.create_time,f.nick_name as updateUserName,a.update_time
        from st_exam_room a
        left join con_province b on a.province_id=b.id
        left join con_city c on a.city_id=c.id
        left join con_county d on a.county_id=d.id
        left join sys_user e on a.create_by = e.user_id
        left join sys_user f on a.update_by = f.user_id
        where a.del_flag = 0
        <if test="name!=null and name!=''">
            and a.name like concat('%',#{name},'%')
        </if>
        <if test="provinceId!=null">
            and a.province_id=#{provinceId}
        </if>
        <if test="cityId!=null">
            and a.city_id=#{cityId}
        </if>
        <if test="countyId!=null">
            and a.county_id=#{countyId}
        </if>
        <if test="status!=null">
            and a.status=#{status}
        </if>
    </select>
    <select id="getNotOccupiedExamRoomList" resultType="com.exam.domain.vo.ExamRoomVo">
        select a.id,a.name,concat(b.name,c.name,d.name,a.address) as completeAddress,a.capacity,a.status,
               e.nick_name as createUserName,a.create_time,f.nick_name as updateUserName,a.update_time
        from st_exam_room a
                 left join con_province b on a.province_id=b.id
                 left join con_city c on a.city_id=c.id
                 left join con_county d on a.county_id=d.id
                 left join sys_user e on a.create_by = e.user_id
                 left join sys_user f on a.update_by = f.user_id
                 left join st_user_exam g on g.exam_room_id = a.id and g.del_flag=0
        where a.del_flag = 0 and g.id is null
    </select>

</mapper>
