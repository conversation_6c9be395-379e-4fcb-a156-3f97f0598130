package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.constant.InvigilatorBaseEnum;
import com.exam.domain.bo.StUserFileBo;
import com.exam.domain.vo.StUserFileVo;
import com.exam.domain.vo.UserFileVO;
import com.exam.service.IStUserFileService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.common.core.page.TableDataInfo;

/**
 * 用户文件
 *
 * <AUTHOR>
 * @date 2023-11-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/userFile")
public class StUserFileController extends BaseController {

    private final IStUserFileService iStUserFileService;

    /**
     * 查询用户文件列表
     */
    @SaCheckPermission("exam:userFile:list")
    @GetMapping("/list")
    public TableDataInfo<StUserFileVo> list(StUserFileBo bo, PageQuery pageQuery) {
        return iStUserFileService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户文件列表
     */
    @SaCheckPermission("exam:userFile:export")
    @Log(title = "用户文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StUserFileBo bo, HttpServletResponse response) {
        List<StUserFileVo> list = iStUserFileService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户文件", StUserFileVo.class, response);
    }

    /**
     * 获取用户文件详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:userFile:query")
    @GetMapping("/{id}")
    public R<StUserFileVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStUserFileService.queryById(id));
    }

    /**
     * 新增用户文件
     */
    @SaCheckPermission("exam:userFile:add")
    @Log(title = "用户文件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StUserFileBo bo) {
        return toAjax(iStUserFileService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改用户文件
     */
    @SaCheckPermission("exam:userFile:edit")
    @Log(title = "用户文件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StUserFileBo bo) {
        return toAjax(iStUserFileService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户文件
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:userFile:remove")
    @Log(title = "用户文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStUserFileService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    @ApiOperation(value = "保存/更新用户身份证照片", notes = "保存/更新用户身份证照片")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "frontPath", value = "身份证正面照片路径", paramType = "query", required = true, dataType = "String"),
        @ApiImplicitParam(name = "reversePath", value = "身份证反面照片路径", paramType = "query", required = true, dataType = "String")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @PostMapping(value = "saveIdCardFile")
    public R saveIdCardFile(@RequestBody UserFileVO userFileVO) {
        if (userFileVO.getFrontPath() == null || userFileVO.getFrontPath().length() <= 0) {
            return R.fail(InvigilatorBaseEnum.USER_IDCARD_FRONT_ERROR);
        }
        if (userFileVO.getReversePath() == null || userFileVO.getReversePath().length() <= 0) {
            return R.fail(InvigilatorBaseEnum.USER_IDCARD_REVERSE_ERROR);
        }
        iStUserFileService.saveIdCardFile(userFileVO);
        return R.ok();
    }

}
