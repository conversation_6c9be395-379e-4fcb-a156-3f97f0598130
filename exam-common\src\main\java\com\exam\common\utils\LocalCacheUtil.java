package com.exam.common.utils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import com.exam.common.core.domain.entity.SysUserEntity;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import lombok.experimental.UtilityClass;

@UtilityClass
public class LocalCacheUtil {

    private static Cache<String, Object> cache;

    private static Cache<String, Map<String, Object>> cacheMap;

    private static Cache<String, SysUserEntity> cacheUserMap;

    private static Cache<String, Map<String, String>> cachePwd;

    static {
        cache = Caffeine.newBuilder().maximumSize(1000).expireAfterWrite(30, TimeUnit.MINUTES).build();
        cacheMap = Caffeine.newBuilder().build();
        cacheUserMap = Caffeine.newBuilder().maximumSize(Integer.MAX_VALUE).build();
        cachePwd = Caffeine.newBuilder().maximumSize(Integer.MAX_VALUE).build();
    }

    public Object get(String key) {
        return cache.getIfPresent(key);
    }

    public void put(String key, Object value) {
        cache.put(key, value);
    }

    public Map<String, Object> getCacheMap(String cacheKey) {
        return cacheMap.getIfPresent(cacheKey);
    }

    public Object getCacheMapValue(String cacheKey, String mapKey) {
        Map<String, Object> map = getCacheMap(cacheKey);
        if (MapUtil.isEmpty(map)) {
            return null;
        }
        return map.get(mapKey);
    }

    public void setCacheMap(String cacheKey, Map<String, Object> value) {
        cacheMap.put(cacheKey, value);
    }

    public void setCacheMapValue(String cacheKey, String mapKey, Object value) {
        Map<String, Object> map = getCacheMap(cacheKey);
        map.put(mapKey, value);
    }

    public void delMultiCacheMapValue(String cacheKey, Set<String> mapKeySet) {
        Map<String, Object> map = getCacheMap(cacheKey);
        MapUtil.removeAny(map, ArrayUtil.toArray(mapKeySet, String.class));
    }

    // 用户缓存
    public SysUserEntity getCacheUserMap(String cacheKey) {
        return cacheUserMap.getIfPresent(cacheKey);
    }

    public void setCacheUserMap(String cacheKey, SysUserEntity value) {
        cacheUserMap.put(cacheKey, value);
    }

    // 密码缓存
    public Map<String, String> getPwd(String key) {
        return cachePwd.getIfPresent(key);
    }

    public Map<String, String> putPwd(String key, String value) {
        Map<String, String> pwdMap = getPwd(key);
        if (Objects.isNull(pwdMap)) {
            pwdMap = MapUtil.newHashMap();
        }
        pwdMap.put(value, "");
        cachePwd.put(key, pwdMap);
        return pwdMap;
    }

}
