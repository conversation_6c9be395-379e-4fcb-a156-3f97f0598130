package com.exam.domain.bo;


import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 播放进度业务对象 st_play_progres
 *
 * <AUTHOR>
 * @date 2023-11-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StPlayProgresBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 课时id
     */
    @NotNull(message = "课时id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long classhourId;

    /**
     * 播放时长
     */
    @NotNull(message = "播放时长不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long playDuration;

    /**
     * 进度
     */
    @NotNull(message = "进度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long progres;

    /**
     * 播放状态(1.已看完；0.未看完)
     */
    @NotNull(message = "播放状态(1.已看完；0.未看完)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long playStatus;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;


}
