package com.exam.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.File;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 图片对比结果
 * </p>
 *
 * <AUTHOR>
 * @since 2021-9-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ImageCompareVO", description="图片对比结果")
public class ImageCompareVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用于区分每一次请求的唯一的字符串")
    private String request_id;

    @ApiModelProperty(value = "比对结果置信度，范围 [0,100]，小数点后3位有效数字，数字越大表示两个人脸越可能是同一个人。 注：如果传入图片但图片中未检测到人脸，则无法进行比对，本字段不返回。")
    private Float confidence;

    @ApiModelProperty(value = "一组用于参考的置信度阈值，包含以下三个字段。每个字段的值为一个 [0,100] 的浮点数，小数点后 3 位有效数字。")
    private Object thresholds;

    @ApiModelProperty(value = "通过 image_url1、image_file1 或 image_base64_1 传入的图片在系统中的标识。")
    private String image_id1;

    @ApiModelProperty(value = "通过 image_url2、image_file2 或 image_base64_2 传入的图片在系统中的标识。")
    private String image_id2;


    @ApiModelProperty(value = "整个请求所花费的时间，单位为毫秒。")
    private int time_used;

    @ApiModelProperty(value = "当请求失败时才会返回此字符串，具体返回内容见后续错误信息章节。否则此字段不存在。")
    public String error_message;

    @ApiModelProperty(value = "识别出有几张人脸")
    private List faces2;


    @ApiModelProperty(value = "人脸识别失败的图片")
    private File failImageFile;

}
