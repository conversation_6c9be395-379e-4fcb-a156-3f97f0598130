package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 纠纷申诉业务对象 st_message
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StMessageBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 触发条件（1：分配课程，2：创建考试，3：强制交卷，4：纠纷申诉）
     */
    @NotNull(message = "触发条件（1：分配课程，2：创建考试，3：强制交卷，4：纠纷申诉）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer msgType;

    /**
     * 终端（1：PC，2：App，3：App,PC）
     */
    @NotNull(message = "终端（1：PC，2：App，3：App,PC）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer terminal;

    /**
     * 消息详情类型（0：无类型，1：强制交卷申诉待处理，2：强制交卷申诉已通过，3：强制交卷申诉已拒绝，4：强制交卷申诉已超过，5：纠纷申诉待处理，6：纠纷申诉已处理）
     */
    @NotNull(message = "消息详情类型（0：无类型，1：强制交卷申诉待处理，2：强制交卷申诉已通过，3：强制交卷申诉已拒绝，4：强制交卷申诉已超过，5：纠纷申诉待处理，6：纠纷申诉已处理）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer detailType;

    /**
     * 消息标题
     */
    @NotBlank(message = "消息标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String content;

    /**
     * 状态(0:停用，1：启用)
     */
    @NotNull(message = "状态(0:停用，1：启用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer msgStatus;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date createTime;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createBy;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateBy;

    /**
     * 领域
     */
    @NotBlank(message = "领域不能为空", groups = { AddGroup.class, EditGroup.class })
    private String domainCode;

    /**
     * 所属单位
     */
    @NotBlank(message = "所属单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;


}
