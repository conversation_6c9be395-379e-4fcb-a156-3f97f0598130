package com.exam.system.service.impl;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.exam.common.core.domain.entity.SysUserEntity;
import com.exam.common.core.domain.entity.SysUserEntity;
import com.exam.common.helper.LoginHelper;
import com.exam.common.utils.LocalCacheUtil;
import com.exam.common.utils.StreamUtils;
import com.exam.common.utils.redis.RedisUtils;
import com.exam.system.mapper.SysUserEntityMapper;
import com.exam.system.service.AsyncService;
import com.exam.system.service.ISysUserEntityService;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户 业务层处理
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysUserEntityServiceImpl implements ISysUserEntityService {

    private final SysUserEntityMapper userMapper;
    private final AsyncService asyncService;

    @Override
    public SysUserEntity getUser(String username) {
        // 缓存中不存在，从数据库查询
        LambdaQueryWrapper<SysUserEntity> lqw = Wrappers.lambdaQuery(SysUserEntity.class);
        lqw.select(SysUserEntity::getUserId, SysUserEntity::getDeptId, SysUserEntity::getUserName,
                SysUserEntity::getPassword, SysUserEntity::getUserType, SysUserEntity::getDelFlag,
                SysUserEntity::getStatus, SysUserEntity::getTenantId, SysUserEntity::getProjectId);
        lqw.and(and -> {
            and.eq(SysUserEntity::getUserName, username);
            and.or();
            and.eq(SysUserEntity::getIdNumber, username);
            and.or();
            and.eq(SysUserEntity::getPhonenumber, username);
        });
        SysUserEntity user = userMapper.selectOne(lqw);
        return user;
    }

    @Async
    @Override
    public void cacheExamUser(Long tenantId, List<Long> userIdList, LocalDateTime deadline) {
        LocalDateTime now = LocalDateTime.now();
        Duration between = Duration.between(now, deadline);
        List<SysUserEntity> userList = queryUserList(tenantId, userIdList);
        cacheExamUser(userList, between);
    }

    List<SysUserEntity> queryUserList(Long tenantId, List<Long> userIdList) {
        LambdaQueryWrapper<SysUserEntity> lqw = Wrappers.lambdaQuery(SysUserEntity.class);
        lqw.select(SysUserEntity::getUserId, SysUserEntity::getDeptId, SysUserEntity::getUserName,
                SysUserEntity::getPhonenumber, SysUserEntity::getIdNumber, SysUserEntity::getPassword,
                SysUserEntity::getUserType, SysUserEntity::getDelFlag, SysUserEntity::getStatus,
                SysUserEntity::getTenantId, SysUserEntity::getProjectId);
        lqw.eq(SysUserEntity::getDelFlag, 0);
        lqw.eq(SysUserEntity::getTenantId, tenantId);
        lqw.in(SysUserEntity::getUserId, userIdList);

        List<SysUserEntity> userList = userMapper.selectList(lqw);
        return userList;
    }

    final static String keyPrefix = "exam:";

    /**
     * 二级缓存
     * 
     * @param userList
     * @param duration
     */
    void cacheExamUser(List<SysUserEntity> userList, Duration duration) {
        log.info("加载考试用户");

        CompletableFuture<String> f1 = asyncService.async((ul) -> {
            Map<String, SysUserEntity> userListMap = StreamUtils.toIdentityMap(userList, SysUserEntity::getUserName);
            userListMap.forEach((k, v) -> {
                if (StrUtil.isNotBlank(k)) {
                    LocalCacheUtil.setCacheUserMap(keyPrefix + k, v);
                    RedisUtils.setCacheObject(keyPrefix + k, v, duration);
                }
            });
            return "";
        }, userList);
        CompletableFuture<String> f2 = asyncService.async((ul) -> {
            Map<String, SysUserEntity> userListMap = StreamUtils.toIdentityMap(userList, SysUserEntity::getPhonenumber);
            userListMap.forEach((k, v) -> {
                if (StrUtil.isNotBlank(k)) {
                    LocalCacheUtil.setCacheUserMap(keyPrefix + k, v);
                    RedisUtils.setCacheObject(keyPrefix + k, v, duration);
                }
            });
            return "";
        }, userList);
        CompletableFuture<String> f3 = asyncService.async((ul) -> {
            Map<String, SysUserEntity> userListMap = StreamUtils.toIdentityMap(userList, SysUserEntity::getIdNumber);
            userListMap.forEach((k, v) -> {
                if (StrUtil.isNotBlank(k)) {
                    LocalCacheUtil.setCacheUserMap(keyPrefix + k, v);
                    RedisUtils.setCacheObject(keyPrefix + k, v, duration);
                }
            });
            return "";
        }, userList);
        CompletableFuture.allOf(f1, f2, f3).join();
        log.info("加载考试用户完毕");
    }

    @Override
    public void cachePassword(Map<String, String> pwdMap) {
        pwdMap.forEach((k, v) -> {
            Map<String,String> newPwdMap = LocalCacheUtil.putPwd(k, v);
            RedisUtils.setCacheMap(k, newPwdMap);
        });
    }

    @Override
    public Boolean checkPwd(String pwd, String hash) {
        Map<String,String> hashMap = LocalCacheUtil.getPwd(pwd);
        if (Objects.nonNull(hashMap) && Objects.nonNull(hashMap.get(hash))) {
            return true;
        }
        
        hashMap = RedisUtils.getCacheMap("pwd");
        if (Objects.nonNull(hashMap) && Objects.nonNull(hashMap.get(hash))) {
            return true;
        }
        
        if (BCrypt.checkpw(pwd, hash)) {
            LocalCacheUtil.putPwd(pwd, hash);
            RedisUtils.setCacheMap("pwd", hashMap);
            return true;
        }
        return false;
    }

    @Override
    public SysUserEntity getUserCache(String username) {
        SysUserEntity cacheUser = LocalCacheUtil.getCacheUserMap(keyPrefix + username);
        if (Objects.nonNull(cacheUser)) {
            return cacheUser;
        }
        cacheUser = RedisUtils.getCacheObject(keyPrefix + username);
        if (Objects.nonNull(cacheUser)) {
            return cacheUser;
        }
        return getUser(username);
    }
}
