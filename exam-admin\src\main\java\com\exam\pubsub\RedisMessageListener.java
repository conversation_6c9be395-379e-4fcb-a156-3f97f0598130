package com.exam.pubsub;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.listener.MessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.exam.system.service.ISysUserEntityService;

/**
 * Redis消息监听器 用于处理接收到的Redis消息
 */
@Slf4j
@Component
public class RedisMessageListener implements MessageListener<String> {

    @Autowired
    ISysUserEntityService sysUserEntityService;

    /**
     * 消息处理
     *
     * @param channel 频道名称
     * @param msg     接收到的消息
     */
    @Override
    public void onMessage(CharSequence channel, String msg) {
        log.info("监听到消息：频道 => {}, 消息内容 => {}", channel, msg);
    }
}
