package com.exam.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.core.domain.R;
import com.exam.domain.qo.StCourseQo;
import com.exam.domain.vo.*;
import com.exam.service.CourseService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName CourseController
 * @Description 课程应用端
 * <AUTHOR>
 * @Date 2021/6/18 10:42 上午
 * @Version 1.0
 */

@ApiSort(value = 3)
@RequestMapping("CourseController")
@RestController
@Api(tags = "CourseController(课程培训)")
public class CourseController {
    private static final Logger logger = LoggerFactory.getLogger(com.exam.controller.CourseController.class);
    @Autowired
    CourseService courseService;

    @ApiOperationSupport(order = 1)
    @GetMapping(value = "/selectUserCourseCategoryList")
    @ApiOperation(value = "1.1 查询用户对应的课程类别列表")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<UserCourseCategoryVO>> selectUserCourseCategoryList() {
        List<UserCourseCategoryVO> userCourseCategoryVOList = courseService.selectUserCourseCategoryList();
        return R.ok(userCourseCategoryVOList);
    }
    @ApiOperationSupport(order = 2)
    @GetMapping(value = "/getUserCourseCategoryInfo")
    @ApiOperation(value = "1.2 查询用户对应的课程类别详细")
    @ApiImplicitParams({@ApiImplicitParam(name = "categoryId", value = "课程类别id", paramType = "query", required = false, dataType = "Long")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<UserCourseCategoryVO> getUserCourseCategoryInfo(Long categoryId) {
        UserCourseCategoryVO userCourseCategoryVO = courseService.getUserCourseCategoryInfo(categoryId);
        return R.ok(userCourseCategoryVO);
    }

    @ApiOperationSupport(order = 3)
    @PostMapping(value = "/saveUserPlayProgres")
    @ApiOperation(value = "1.3 保存用户播放时长")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<String> saveUserPlayProgres(@ApiParam @RequestBody StPlayProgresVo StPlayProgresVo) {
        return R.ok(String.valueOf(courseService.saveUserPlayProgres(StPlayProgresVo)));
    }

    @ApiOperationSupport(order = 4)
    @GetMapping(value = "/selectAllCourseList")
    @ApiOperation(value = "1.4 查询所有课程列表")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<StCourseVo>> selectAllCourseList() {
        List<StCourseVo> courseVOList = courseService.selectAllCourseList();
        return R.ok(courseVOList);
    }

    @ApiOperationSupport(order = 5)
    @GetMapping(value = "/getUserClasshourInfo")
    @ApiOperation(value = "1.5 查询用户课时详细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "classhourId", value = "课时id", paramType = "query", dataType = "long")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<StClasshourVo> getUserClasshourInfo(long classhourId) {
        StClasshourVo StClasshourVo = courseService.getUserClasshourInfo(classhourId);
        return R.ok(StClasshourVo);
    }

    @ApiOperationSupport(order = 6)
    @GetMapping(value = "/selectBoutiqueCourseList")
    @ApiOperation(value = "1.6 查询精品课程列表")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<StCourseVo>> selectBoutiqueCourseList() {
        StCourseQo stCourseQo = new StCourseQo();
        List<StCourseVo> courseVOList = courseService.selectBoutiqueCourseList(stCourseQo);
        return R.ok(courseVOList);
    }

    @ApiOperationSupport(order = 7)
    @GetMapping(value = "/selectBoutiqueCourseCategoryList")
    @ApiOperation(value = "1.7 查询精品课程类别列表")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<StCourseCategoryVo>> selectBoutiqueCourseCategoryList() {
        Map conditionParam = new HashMap<String,String>();
        List<StCourseCategoryVo> courseCategoryVOList = courseService.selectBoutiqueCourseCategoryList(conditionParam);
        return R.ok(courseCategoryVOList);
    }

    @ApiOperationSupport(order = 8)
    @GetMapping(value = "/selectCourseByCategoryId")
    @ApiOperation(value = "1.8 通过课程类别id查询课程(一级类别查所有)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "categoryId", value = "课程类别（多个逗号分割）", paramType = "query", dataType = "String")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<StCourseVo>> selectCourseByCategoryId(String categoryId) {
        StCourseQo stCourseQo = new StCourseQo();
        stCourseQo.setCategoryId(Long.valueOf(categoryId));
        List<StCourseVo> courseVOList = courseService.selectCourseByCategoryId(stCourseQo);
        return R.ok(courseVOList);
    }

    @ApiOperationSupport(order = 9)
    @GetMapping(value = "/selectUserAllCourseList")
    @ApiOperation(value = "1.9 查询一级类别下用户所有课程列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "categoryId", value = "课程类别", paramType = "query", dataType = "String")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<UserCourseCategoryVO> selectUserAllCourseList(String categoryId) {
        return R.ok(courseService.selectUserAllCourseList(categoryId));
    }

    @ApiOperationSupport(order = 10)
    @GetMapping(value = "/selectUserAllCourseListPage")
    @ApiOperation(value = "1.10 查询一级类别下用户所有课程")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "categoryId", value = "课程类别", paramType = "query", dataType = "String")
            , @ApiImplicitParam(name = "size", value = "每页条数", paramType = "query", required = true, dataType = "Integer")
            , @ApiImplicitParam(name = "current", value = "当前页数", paramType = "query", required = true, dataType = "Integer")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<UserCourseCategoryVO> selectUserAllCourseListPage(String categoryId,Integer size, Integer current) {
        return R.ok(courseService.selectUserAllCourseListPage(categoryId,new Page<>(current, size)));
    }

    @ApiOperationSupport(order = 11)
    @GetMapping(value = "/getUserClasshourListByCourseId")
    @ApiOperation(value = "1.11 通过课程id查询客户课时列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "courseId", value = "课程id", paramType = "query", dataType = "String")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<StClasshourVo>> getUserClasshourListByCourseId(String courseId) {
        List<StClasshourVo> StClasshourVolist = courseService.getUserClasshourListByCourseId(courseId);
        return R.ok(StClasshourVolist);
    }

    @ApiOperationSupport(order = 12)
    @GetMapping(value = "/getCurrClasshourByCategoryId")
    @ApiOperation(value = "1.12 通过课程类别id查询客户当前学习的课时")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "categoryId", value = "课程类别id", paramType = "query", dataType = "String")
    })
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<StClasshourVo> getCurrClasshourByCategoryId(String categoryId) {
        StClasshourVo StClasshourVo = courseService.getCurrClasshourByCategoryId(categoryId);
        return R.ok(StClasshourVo);
    }

    @ApiOperationSupport(order = 13)
    @GetMapping(value = "/selectUserCourseCategoryListHomePageMe")
    @ApiOperation(value = "1.13 查询用户对应的课程类别列表（经管首页）")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<UserCourseCategoryVO>> selectUserCourseCategoryListHomePageMe() {
        List<UserCourseCategoryVO> userCourseCategoryVOList = courseService.selectUserCourseCategoryListHomePageMe();
        return R.ok(userCourseCategoryVOList);
    }

    @ApiOperationSupport(order = 14)
    @GetMapping(value = "/getUserCourseCategoryInfoMe")
    @ApiImplicitParams({@ApiImplicitParam(name = "categoryId", value = "课程类别id", paramType = "query", required = true, dataType = "String"),
            @ApiImplicitParam(name = "courseId", value = "课程id", paramType = "query", required = false, dataType = "String")})
    @ApiOperation(value = "1.14 查询用户对应的课程类别详细（经管）")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<UserCourseCategoryVO> getUserCourseCategoryInfoMe(String categoryId,String courseId) {
        UserCourseCategoryVO userCourseCategoryVO = courseService.getUserCourseCategoryInfoMe(categoryId,courseId);
        return R.ok(userCourseCategoryVO);
    }

    @ApiOperationSupport(order = 15)
    @GetMapping(value = "/v2/selectUserCourseCategoryListV2")
    @ApiOperation(value = "1.15 查询用户对应的课程类别列表(2.3.2)")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<List<UserCourseCategoryVO>> selectUserCourseCategoryListV2() {
        List<UserCourseCategoryVO> userCourseCategoryVOList = courseService.selectUserCourseCategoryListV2();
        return R.ok(userCourseCategoryVOList);
    }


    @ApiOperationSupport(order = 16)
    @GetMapping(value = "/getUserCourseInfo")
    @ApiOperation(value = "1.16 查询用户对应的课程详细(安管人员)")
    @ApiImplicitParams({@ApiImplicitParam(name = "courseId", value = "课程id", paramType = "query", required = true, dataType = "String")})
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<StCourseVo> getUserCourseInfo(String courseId) {
        StCourseVo StCourseVo = courseService.getUserCourseInfo(courseId);
        return R.ok(StCourseVo);
    }
}


