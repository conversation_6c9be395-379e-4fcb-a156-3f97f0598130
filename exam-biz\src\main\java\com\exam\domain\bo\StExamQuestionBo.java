package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 考试与试题关系业务对象 st_exam_question
 *
 * <AUTHOR>
 * @date 2023-11-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StExamQuestionBo extends BaseEntity {

    /**
     * 主键 ID
     */
    @NotNull(message = "主键 ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long id;

    /**
     * 考试 ID
     */
    @NotNull(message = "考试 ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long examId;

    /**
     * 试题 ID
     */
    @NotNull(message = "试题 ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long questionId;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;


}
