--- # 临时文件存储位置 避免临时文件被系统清理报错
#spring.servlet.multipart.location: /exam/server/temp

--- # 监控中心配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: true
  url: http://localhost:9090/admin
  instance:
    service-host-type: IP
  username: exam
  password: 123456

--- # xxl-job 配置
xxl.job:
  # 执行器开关
  enabled: true
  # 调度中心地址：如调度中心集群部署存在多个地址则用逗号分隔。
  admin-addresses: http://localhost:9100/xxl-job-admin
  # 执行器通讯TOKEN：非空时启用
  access-token: xxl-job
  executor:
    # 执行器AppName：执行器心跳注册分组依据；为空则关闭自动注册
    appname: xxl-job-executor
    # 执行器端口号 执行器从9101开始往后写
    port: 9101
    # 执行器注册：默认IP:PORT
    address:
    # 执行器IP：默认自动获取IP
    ip:
    # 执行器运行日志文件存储磁盘路径
    logpath: ./logs/xxl-job
    # 执行器日志文件保存天数：大于3生效
    logretentiondays: 30

--- # 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: false
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          driverClassName: com.mysql.cj.jdbc.Driver
          # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
          # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
          url: ************************************************************************************************************************************************************************************************
          username: root
          password: root
        # 从库数据源
#        slave:
#          lazy: true
#          driverClassName: com.mysql.cj.jdbc.Driver
#          url:
#          username:
#          password:
#        oracle:
#          driverClassName: oracle.jdbc.OracleDriver
#          url: *************************************
#          username: ROOT
#          password: root
#          druid:
#            validationQuery: SELECT 1 FROM DUAL
#        postgres:
#          driverClassName: org.postgresql.Driver
#          url: ******************************************************************************************************************************************
#          username: root
#          password: root
#        sqlserver:
#          driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
#          url: *****************************************************************************************************
#          username: SA
#          password: root
      druid:
        # 初始连接数
        initialSize: 20
        # 最小连接池数量
        minIdle: 50
        # 最大连接池数量
        maxActive: 200
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        # 配置一个连接在池中最大生存的时间，单位是毫秒
        maxEvictableIdleTimeMillis: 900000
        # 配置检测连接是否有效
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 注意这个值和druid原生不一致，默认启动了stat
        filters: stat

--- # druid 配置
spring.datasource.druid:
  webStatFilter:
    enabled: true
  statViewServlet:
    enabled: true
    # 设置白名单，不填则允许所有访问
    allow:
    url-pattern: /druid/*
    # 控制台管理用户名和密码
    login-username: exam
    login-password: 123456
  filter:
    stat:
      enabled: true
      # 慢SQL记录
      log-slow-sql: true
      slow-sql-millis: 1000
      merge-sql: true
    wall:
      config:
        multi-statement-allow: true

--- # redis 集群配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    cluster:
      nodes:
        - localhost:6379
        - localhost:6380
        - localhost:6381
    # 密码(如没有密码请注释掉)
    # password:
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false

redisson:
  # redis key前缀
  keyPrefix:
  # 线程池数量
  threads: 16
  # Netty线程池数量
  nettyThreads: 32
  # 集群配置
  clusterServersConfig:
    # 客户端名称
    clientName: ${exam.name}
    # master最小空闲连接数
    masterConnectionMinimumIdleSize: 32
    # master连接池大小
    masterConnectionPoolSize: 64
    # slave最小空闲连接数
    slaveConnectionMinimumIdleSize: 32
    # slave连接池大小
    slaveConnectionPoolSize: 64
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50
    # 读取模式
    readMode: "SLAVE"
    # 订阅模式
    subscriptionMode: "MASTER"

--- # mail 邮件发送
mail:
  enabled: false
  host: smtp.163.com
  port: 465
  # 是否需要用户名密码验证
  auth: true
  # 发送方，遵循RFC-822标准
  from: <EMAIL>
  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）
  user: <EMAIL>
  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）
  pass: xxxxxxxxxx
  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。
  starttlsEnable: true
  # 使用SSL安全连接
  sslEnable: true
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 0
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 0

--- # sms 短信
sms:
  enabled: false
  # 阿里云 dysmsapi.aliyuncs.com
  # 腾讯云 sms.tencentcloudapi.com
  endpoint: "dysmsapi.aliyuncs.com"
  accessKeyId: xxxxxxx
  accessKeySecret: xxxxxx
  signName: 测试
  # 腾讯专用
  sdkAppId:

ansj:
  default:
    dic:
      path: ${train.file.upload.path}/default.dic

train:
  image:
    path: /train/
    upload:
      path: /home/<USER>/static/images/train/img/
    domain: img/
    server: https://www.idcgj.com/
  file:
    zip:
      path: /usr/local/train/certpic/
    path: /train/file/
    upload:
      path: /storage/static/files/train/file/
    domain: file/
    client: /img/client.zip
    server: https://www.idcgj.com/
  video:
    path: /train/video/
    upload:
      path: /storage/static/videos/train/video/
    domain: video/
    server: https://www.idcgj.com/
invigilator_sms:
  file:
    server: https://www.idcgj.com/
  #sms.file.server =  http://************:8085/
  aiplatform:
    api-url: https://ai.techin.top:10020/ai/user/match
    #sms.aiplatform.api-url = http://**************/ai/user/match
    group-id: UgvQPiQed6

spring:
  face:
    url: https://api-cn.faceplusplus.com/facepp/v3/compare
    #spring.face.appkey=p8JhvC72Y-jZGmFpOWV3LQEFHHLeGu1j
    #spring.face.appSecret=4em2j6MB780ovpu7gi_LBQ_KC2sPxcMM
    appkey: Qzj8J3TAdgtvMSRNb1H-VmoOaqoouiaG
    appSecret: 2nwUIxpZiX8_oojAW98kuvt0wRNXkZ4U


