package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 【请填写功能名称】视图对象 st_classhour_ask
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@Data
@ExcelIgnoreUnannotated
public class StClasshourAskVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 课时id
     */
    @ExcelProperty(value = "课时id")
    private Long classhourId;

    /**
     * 提问位置（百分比）
     */
    @ExcelProperty(value = "提问位置", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "百=分比")
    private Long askPort;

    /**
     * 回退位置（秒）
     */
    @ExcelProperty(value = "回退位置", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "秒=")
    private Long backPort;

    /**
     * 题干
     */
    @ExcelProperty(value = "题干")
    private String questionContent;

    /**
     * 选项内容
     */
    @ExcelProperty(value = "选项内容")
    private String optionContent;

    /**
     * 正确答案
     */
    @ExcelProperty(value = "正确答案")
    private String rightKey;

    /**
     * 图片路径
     */
    @ExcelProperty(value = "图片路径")
    private String picPath;

    /**
     * 试题类型
     */
    @ExcelProperty(value = "试题类型")
    private Long questionGenre;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long projectId;

}
