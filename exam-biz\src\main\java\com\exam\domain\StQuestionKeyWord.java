package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 试题关键词对象 st_question_key_word
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_question_key_word")
public class StQuestionKeyWord extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 试题id
     */
    private Long questionId;
    /**
     * 关键词内容
     */
    private String keyWordContent;
    /**
     * 分值占比
     */
    private Long scoreProportion;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 所属项目
     */
    private Long projectId;

}
