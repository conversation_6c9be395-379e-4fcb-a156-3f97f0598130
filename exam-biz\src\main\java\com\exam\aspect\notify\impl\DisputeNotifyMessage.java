package com.exam.aspect.notify.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.exam.aspect.notify.NotifyMessageObject;
import com.exam.constant.CommonConsts;
import com.exam.constant.CommonDataBaseConst;
import com.exam.constant.TableFieldNameConsts;
import com.exam.domain.StDisputeComplaint;
import com.exam.domain.StMessageUser;
import com.exam.domain.StMessageUserShield;
import com.exam.domain.vo.StMessageVo;
import com.exam.service.IStMessageService;
import com.exam.service.IStMessageUserService;
import com.exam.service.IStMessageUserShieldService;
import com.exam.utils.SequenceBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class DisputeNotifyMessage extends NotifyMessageObject {

    @Autowired
    private IStMessageService stMessageService;
    @Autowired
    private IStMessageUserService stMessageUserService;
    @Autowired
    private IStMessageUserShieldService iStMessageUserShieldService;

    @Override
    public void notifyMessage(int msgType, int msgDetailType, Object... args){


        StDisputeComplaint stDisputeComplaint = null;
        if(args.length != 0 && args[0] instanceof StDisputeComplaint) {
            stDisputeComplaint = (StDisputeComplaint) args[0];
        }

        // TODO 此处待改造，但涉及的内容较多，后续处理


        StMessageVo vo = stMessageService.getByMsgAndDetailType(msgType, msgDetailType, stDisputeComplaint.getTenantId());

        QueryWrapper<StMessageUserShield> queryWrapper = new QueryWrapper<StMessageUserShield>();
        queryWrapper.eq(TableFieldNameConsts.USER_ID, stDisputeComplaint.getUserId());
        queryWrapper.eq(TableFieldNameConsts.TENANT_ID, stDisputeComplaint.getTenantId());
        StMessageUserShield stMessageUserShield = iStMessageUserShieldService.getBaseMapper().selectOne(queryWrapper);

        if(stMessageUserShield == null || CommonConsts.NO.equals(stMessageUserShield.getIsShield())) {
            StMessageUser stMessageUser = new StMessageUser();
//            EntityUtils.setInsertInfo(stMessageUser);
            stMessageUser.setDelFlag(CommonDataBaseConst.YES_OR_NO.NO.getCode());
            stMessageUser.setId(SequenceBean.getSequence());
            stMessageUser.setMainTableId(stDisputeComplaint.getId());
            stMessageUser.setPushTime(LocalDateTime.now());
            stMessageUser.setStatus(Integer.valueOf(CommonDataBaseConst.MSG_READ_STATUS.UNREAD.getCode()));
            stMessageUser.setUserInfoId(stDisputeComplaint.getUserId());
            stMessageUser.setMessageId(vo.getId());
            stMessageUser.setMsgDetailType(msgDetailType);
//            stMessageUser.setDomainCode(stDisputeComplaint.getTenantId());
            stMessageUserService.save(stMessageUser);
        }
    }

}
