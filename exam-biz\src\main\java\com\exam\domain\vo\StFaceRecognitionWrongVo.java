package com.exam.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 用户组群关系视图对象 st_face_recognition_wrong
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StFaceRecognitionWrongVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 人脸照片
     */
    @ExcelProperty(value = "人脸照片")
    private String faceFilePath;

    /**
     * 人脸照片路径
     */
    @ExcelProperty(value = "人脸照片路径")
    private String faceFileUrl;

    /**
     * 身份证照片
     */
    @ExcelProperty(value = "身份证照片")
    private String idCardFilePath;

    /**
     * 身份证路径
     */
    @ExcelProperty(value = "身份证路径")
    private String idCardFileUrl;

    /**
     * 是否删除(1.是；0.否)
     */
    @ExcelProperty(value = "是否删除(1.是；0.否)")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long createBy;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long updateBy;


}
