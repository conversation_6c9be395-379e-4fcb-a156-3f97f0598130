package com.exam.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 用户组群关系对象 st_user_group
 *
 * <AUTHOR>
 * @date 2023-11-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_user_group")
public class StUserGroup extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 组群id
     */
    private Long groupId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 租户id
     */
    private Long tenantId;

}
