package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StFaceRecognitionWrongBo;
import com.exam.domain.vo.StFaceRecognitionWrongVo;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.service.IStFaceRecognitionWrongService;
import com.exam.common.core.page.TableDataInfo;

/**
 * 用户组群关系
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/faceRecognitionWrong")
public class StFaceRecognitionWrongController extends BaseController {

    private final IStFaceRecognitionWrongService iStFaceRecognitionWrongService;

    /**
     * 查询用户组群关系列表
     */
    @SaCheckPermission("exam:faceRecognitionWrong:list")
    @GetMapping("/list")
    public TableDataInfo<StFaceRecognitionWrongVo> list(StFaceRecognitionWrongBo bo, PageQuery pageQuery) {
        return iStFaceRecognitionWrongService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户组群关系列表
     */
    @SaCheckPermission("exam:faceRecognitionWrong:export")
    @Log(title = "用户组群关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StFaceRecognitionWrongBo bo, HttpServletResponse response) {
        List<StFaceRecognitionWrongVo> list = iStFaceRecognitionWrongService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户组群关系", StFaceRecognitionWrongVo.class, response);
    }

    /**
     * 获取用户组群关系详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:faceRecognitionWrong:query")
    @GetMapping("/{id}")
    public R<StFaceRecognitionWrongVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStFaceRecognitionWrongService.queryById(id));
    }

    /**
     * 新增用户组群关系
     */
    @SaCheckPermission("exam:faceRecognitionWrong:add")
    @Log(title = "用户组群关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StFaceRecognitionWrongBo bo) {
        return toAjax(iStFaceRecognitionWrongService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改用户组群关系
     */
    @SaCheckPermission("exam:faceRecognitionWrong:edit")
    @Log(title = "用户组群关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StFaceRecognitionWrongBo bo) {
        return toAjax(iStFaceRecognitionWrongService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户组群关系
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:faceRecognitionWrong:remove")
    @Log(title = "用户组群关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStFaceRecognitionWrongService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
