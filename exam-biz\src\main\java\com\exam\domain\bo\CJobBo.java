package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 岗位业务对象 c_job
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class CJobBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 职务名称
     */
    @NotBlank(message = "职务名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 是否管理者（0：否；1：是；）
     */
    @NotNull(message = "是否管理者（0：否；1：是；）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isManager;

    /**
     * 序号
     */
    @NotNull(message = "序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sn;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @NotNull(message = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dataStatus;

    /**
     * 所属公司
     */
    @NotBlank(message = "所属公司不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;

    /**
     * 注册时间(显示使用)
     */
    @NotBlank(message = "注册时间(显示使用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String signTimeStr;

    /**
     * 编辑时间(显示使用)
     */
    @NotBlank(message = "编辑时间(显示使用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String updateTimeStr;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long signUserId;

    /**
     * 编辑人id
     */
    @NotNull(message = "编辑人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateUserId;

    /**
     * 上级节点编码
     */
    @NotBlank(message = "上级节点编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentOrgCode;

    /**
     * 是否为外部岗位(0：否；1：是；)
     */
    @NotNull(message = "是否为外部岗位(0：否；1：是；)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isExternal;

    /**
     * 节点编码
     */
    @NotBlank(message = "节点编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgCode;


}
