package com.exam.pubsub;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.redisson.api.listener.MessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Redis订阅者服务
 * 用于订阅Redis频道并处理接收到的消息
 */
@Slf4j
@Component
public class RedisSubscriber {

    /**
     * 默认主题名称
     */
    public static final String DEFAULT_TOPIC_NAME = "test_topic";

    /**
     * 存储已注册的监听器ID，用于后续取消订阅
     */
    private final Map<String, Integer> registeredListeners = new ConcurrentHashMap<>();

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RedisMessageListener redisMessageListener;

    /**
     * 初始化时订阅默认主题
     */
    @PostConstruct
    public void init() {
        subscribeTopic(DEFAULT_TOPIC_NAME, String.class, redisMessageListener);
        log.info("已订阅默认主题: {}", DEFAULT_TOPIC_NAME);
    }

    /**
     * 订阅主题
     *
     * @param topicName 主题名
     * @param type      消息类型
     * @param listener  监听器
     * @param <T>       消息类型泛型
     * @return 监听器ID，可用于后续取消订阅
     */
    public <T> int subscribeTopic(String topicName, Class<T> type, MessageListener<T> listener) {
        RTopic topic = redissonClient.getTopic(topicName);
        int listenerId = topic.addListener(type, listener);
        registeredListeners.put(topicName, listenerId);
        return listenerId;
    }

    /**
     * 取消订阅主题
     *
     * @param topicName 主题名
     * @return 是否成功取消订阅
     */
    public boolean unsubscribeTopic(String topicName) {
        Integer listenerId = registeredListeners.get(topicName);
        if (listenerId != null) {
            RTopic topic = redissonClient.getTopic(topicName);
            topic.removeListener(listenerId);
            registeredListeners.remove(topicName);
            log.info("已取消订阅主题: {}", topicName);
            return true;
        }
        return false;
    }

    /**
     * 获取已注册的所有主题
     *
     * @return 主题名称集合
     */
    public Set<String> getSubscribedTopics() {
        return registeredListeners.keySet();
    }
}
