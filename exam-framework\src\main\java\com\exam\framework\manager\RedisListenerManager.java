package com.exam.framework.manager;

import com.exam.common.utils.redis.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.listener.MessageListener;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Redis监听管理器
 * 用于监听Redis键的变化，并同步更新本地缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisListenerManager implements ApplicationRunner {

    private final LocalCacheManager localCacheManager;

    /**
     * 存储已注册的监听器，用于后续取消监听
     */
    private final Map<String, Integer> registeredListeners = new ConcurrentHashMap<>();

    /**
     * 应用启动时初始化
     */
    @Override
    public void run(ApplicationArguments args) {
        log.info("------------- Redis Listener Manager 初始化 ---------------- ");
    }

    /**
     * 监听指定键的变化
     * 当键值发生变化时，会更新本地缓存
     *
     * @param key       要监听的键
     * @param valueType 值的类型
     */
    public <T> void listenKeyChanges(String key, Class<T> valueType) {
        if (registeredListeners.containsKey(key)) {
            log.debug("Listener for key {} already registered", key);
            return;
        }

        String channelName = "cache_sync:" + key;

        RTopic topic = RedisUtils.getClient().getTopic(channelName);

        MessageListener<T> listener = (channel, msg) -> {
            log.debug("key已更新: {}", key);
            if (msg == null) {
                // 如果值为null，表示键被删除
                localCacheManager.remove(key);
                log.debug("本地缓存key已删除: {}", key);
            } else {
                // 更新本地缓存
                localCacheManager.put(key, msg);
                log.debug("本地缓存key已更新: {}", key);
            }
        };

        // 注册监听器并获取监听器ID
        int listenerId = topic.addListener(valueType, listener);

        // 保存监听器ID，用于后续取消监听
        registeredListeners.put(key, listenerId);
    }

    /**
     * 取消对指定键的监听
     *
     * @param key 要取消监听的键
     */
    public void cancelKeyListener(String key) {
        Integer listenerId = registeredListeners.get(key);
        if (listenerId != null) {
            // 取消监听
            String channelName = "cache_sync:" + key;
            RedisUtils.getClient().getTopic(channelName).removeListener(listenerId);
            registeredListeners.remove(key);
            log.info("取消监听: {}", key);
        }
    }

    /**
     * 同步键值到Redis并通知其他节点
     *
     * @param key   键
     * @param value 值
     */
    public <T> void syncKeyToRedis(String key, T value) {
        // 更新本地缓存
        localCacheManager.put(key, value);

        // 发布到Redis，通知其他节点
        String channelName = "cache_sync:" + key;
        RedisUtils.publish(channelName, value);
    }

    /**
     * 删除键并通知其他节点
     *
     * @param key 键
     */
    public void removeKey(String key) {
        // 从本地缓存中删除
        localCacheManager.remove(key);

        // 发布null值到Redis，通知其他节点删除该键
        String channelName = "cache_sync:" + key;
        RedisUtils.publish(channelName, null);
    }
}
