package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 课程业务对象 st_classhour
 *
 * <AUTHOR>
 * @date 2023-10-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StClasshourBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 所属课程id
     */
    @NotNull(message = "所属课程id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseId;

    /**
     * 课时名称
     */
    @NotBlank(message = "课时名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String classhourName;

    /**
     * 课时序号
     */
    @NotNull(message = "课时序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long classhourSn;

    /**
     * 文件路径
     */
    @NotBlank(message = "文件路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String filePath;

    /**
     * 课程状态
     */
    @NotNull(message = "课程状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long classhourStatus;

    /**
     * 课时时长
     */
    @NotNull(message = "课时时长不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long classhourDuration;

    /**
     * 文件类型（1.视频；2.音频；3.文档）
     */
    @NotNull(message = "文件类型（1.视频；2.音频；3.文档）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fileType;

    /**
     * 文件名称
     */
    @NotBlank(message = "文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileName;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;


}
