package com.exam.service;

import com.exam.domain.RailwayProject;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface RailwayProjectService extends IService<RailwayProject>{


    void add(RailwayProject project);

    void editById(RailwayProject project);

    List<RailwayProject> queryByRfId(Long rfId);


    RailwayProject selectByUserId(Long userId);
}
