package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.FaceRatioDictionaryBo;
import com.exam.domain.vo.FaceRatioDictionaryVo;
import com.exam.service.IFaceRatioDictionaryService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 人脸对比成功比例
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/ratioDictionary")
public class FaceRatioDictionaryController extends BaseController {

    private final IFaceRatioDictionaryService iFaceRatioDictionaryService;

    /**
     * 查询人脸对比成功比例列表
     */
    @SaCheckPermission("exam:ratioDictionary:list")
    @GetMapping("/list")
    public TableDataInfo<FaceRatioDictionaryVo> list(FaceRatioDictionaryBo bo, PageQuery pageQuery) {
        return iFaceRatioDictionaryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出人脸对比成功比例列表
     */
    @SaCheckPermission("exam:ratioDictionary:export")
    @Log(title = "人脸对比成功比例", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FaceRatioDictionaryBo bo, HttpServletResponse response) {
        List<FaceRatioDictionaryVo> list = iFaceRatioDictionaryService.queryList(bo);
        ExcelUtil.exportExcel(list, "人脸对比成功比例", FaceRatioDictionaryVo.class, response);
    }

    /**
     * 获取人脸对比成功比例详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:ratioDictionary:query")
    @GetMapping("/{id}")
    public R<FaceRatioDictionaryVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iFaceRatioDictionaryService.queryById(id));
    }

    /**
     * 新增人脸对比成功比例
     */
    @SaCheckPermission("exam:ratioDictionary:add")
    @Log(title = "人脸对比成功比例", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FaceRatioDictionaryBo bo) {
        return toAjax(iFaceRatioDictionaryService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改人脸对比成功比例
     */
    @SaCheckPermission("exam:ratioDictionary:edit")
    @Log(title = "人脸对比成功比例", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FaceRatioDictionaryBo bo) {
        return toAjax(iFaceRatioDictionaryService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除人脸对比成功比例
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:ratioDictionary:remove")
    @Log(title = "人脸对比成功比例", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iFaceRatioDictionaryService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
