package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 模式考试题库视图对象 st_mock_question
 *
 * <AUTHOR>
 * @date 2023-11-06
 */
@Data
@ExcelIgnoreUnannotated
public class StMockQuestionVo {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 试题类别id
     */
    @ExcelProperty(value = "试题类别id")
    private Long questionTypeId;

    /**
     * 试题id
     */
    @ExcelProperty(value = "试题id")
    private Long questionId;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;


    /**
     * 题干
     */
    @ExcelProperty(value = "题干")
    private String questionContent;

    /**
     * 选项内容
     */
    @ExcelProperty(value = "选项内容")
    private String optionContent;

    /**
     * 正确答案
     */
    @ExcelProperty(value = "正确答案")
    private String rightKey;

    /**
     * 解析
     */
    @ExcelProperty(value = "解析")
    private String analysis;

    /**
     * 试题类别
     */
    @ExcelProperty(value = "试题类别")
    private String questionType;

    /**
     * 选择答案
     */
    @ExcelProperty(value = "试题类别")
    private String answer;

    /**
     * 试题类型
     */
    @ExcelProperty(value = "试题类型")
    private Integer questionGenre;

    private Integer questionSn;

    private Double singleScore;

    private Integer reviewResult;

    @ApiModelProperty(value = "创建人")
    private String userName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;
}
