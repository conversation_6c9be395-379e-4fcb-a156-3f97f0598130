package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StPlayProgresOptionalHistoryBo;
import com.exam.domain.vo.StPlayProgresOptionalHistoryVo;
import com.exam.service.IStPlayProgresOptionalHistoryService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;

import com.exam.common.core.page.TableDataInfo;

/**
 * 播放进度
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/playProgresOptionalHistory")
public class StPlayProgresOptionalHistoryController extends BaseController {

    private final IStPlayProgresOptionalHistoryService iStPlayProgresOptionalHistoryService;

    /**
     * 查询播放进度列表
     */
    @SaCheckPermission("exam:playProgresOptionalHistory:list")
    @GetMapping("/list")
    public TableDataInfo<StPlayProgresOptionalHistoryVo> list(StPlayProgresOptionalHistoryBo bo, PageQuery pageQuery) {
        return iStPlayProgresOptionalHistoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出播放进度列表
     */
    @SaCheckPermission("exam:playProgresOptionalHistory:export")
    @Log(title = "播放进度", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StPlayProgresOptionalHistoryBo bo, HttpServletResponse response) {
        List<StPlayProgresOptionalHistoryVo> list = iStPlayProgresOptionalHistoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "播放进度", StPlayProgresOptionalHistoryVo.class, response);
    }

    /**
     * 获取播放进度详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:playProgresOptionalHistory:query")
    @GetMapping("/{id}")
    public R<StPlayProgresOptionalHistoryVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStPlayProgresOptionalHistoryService.queryById(id));
    }

    /**
     * 新增播放进度
     */
    @SaCheckPermission("exam:playProgresOptionalHistory:add")
    @Log(title = "播放进度", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StPlayProgresOptionalHistoryBo bo) {
        return toAjax(iStPlayProgresOptionalHistoryService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改播放进度
     */
    @SaCheckPermission("exam:playProgresOptionalHistory:edit")
    @Log(title = "播放进度", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StPlayProgresOptionalHistoryBo bo) {
        return toAjax(iStPlayProgresOptionalHistoryService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除播放进度
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:playProgresOptionalHistory:remove")
    @Log(title = "播放进度", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStPlayProgresOptionalHistoryService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
