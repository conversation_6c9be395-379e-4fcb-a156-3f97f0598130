package com.exam.domain.vo;

import java.util.Date;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.utils.GyUtils;
import com.exam.utils.SpringContextUtil;
import lombok.Data;


/**
 * 课程类别视图对象 st_course_category
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StCourseCategoryVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private String id;

    /**
     * 二级课程类别
     */
    @ExcelProperty(value = "二级课程类别")
    private List<StCourseCategoryVo> children;

    /**
     * 类别名称
     */
    @ExcelProperty(value = "类别名称")
    private String categoryName;

    /**
     * 封面图片路径
     */
    @ExcelProperty(value = "封面图片路径")
    private String picPath;

    /**
     * 父id
     */
    @ExcelProperty(value = "父id")
    private String fatherId;

    /**
     * 是否安管人员(1.是；0.否)
     */
    @ExcelProperty(value = "是否安管人员(1.是；0.否)")
    private Long isSafety;

    /**
     * 分类层级
     */
    @ExcelProperty(value = "分类层级")
    private Integer level;


    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long projectId;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 用户姓名
     */
    @ExcelProperty(value = "用户姓名")
    private String userName;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private Long createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    @ExcelProperty(value = "更新者")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 删除标识
     */
    @ExcelProperty(value = "删除标识")
    private Integer delFlag;

    /**
     * 全路径
     */
    private String fullPicPath;

}
