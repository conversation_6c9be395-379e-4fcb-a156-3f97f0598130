package com.exam.domain.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 用户注册
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "身份证信息")
public class LaborInfoRegisterQO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "姓名")
    private String laborMyName;

    @ApiModelProperty(value = "性别:0男1女")
    private int laborSex;

    @ApiModelProperty(value = "民族")
    private String laborNation;

    @ApiModelProperty(value = "身份证号")
    private String laborIdNum;

    @ApiModelProperty(value = "出生年月")
    private String laborBirth;

    @ApiModelProperty(value = "家庭住址")
    private String laborAddr;

    @ApiModelProperty(value = "手机号")
    private String laborPhone;

//    @ApiModelProperty(value = "是否校验手机验证码。0未通过1通过校验")
//    private Integer isValidate;

    @ApiModelProperty(value = "发证机关")
    private String credentialsOffice;

    @ApiModelProperty(value = "证件有效期开始时间")
    private String credentialsStartValidity;

    @ApiModelProperty(value = "证件有效期结束时间")
    private String credentialsEndValidity;

    @ApiModelProperty(value = "身份证正面")
    private String fileFront;

    @ApiModelProperty(value = "身份证背面")
    private String fileBack;

    @ApiModelProperty(value = "身份证缩略图")
    private String fileAbbreviationFront;

    @ApiModelProperty(value = "身份证缩略图")
    private String fileAbbreviationBack;

    @ApiModelProperty(value = "短信验证码")
    private String verificationCode;

    @ApiModelProperty(value = "图片状态")
    private String imageStatus;

    @ApiModelProperty(value = "性别")
    private String laborSexStr;
}
