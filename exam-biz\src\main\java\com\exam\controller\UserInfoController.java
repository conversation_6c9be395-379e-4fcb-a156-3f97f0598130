package com.exam.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.core.domain.R;
import com.exam.common.core.domain.model.LoginUser;
import com.exam.common.helper.LoginHelper;
import com.exam.config.Configure;
import com.exam.constant.CommonDataBaseConst;
import com.exam.domain.Idcard;
import com.exam.domain.StCertificateTypeGroup;
import com.exam.domain.StDutiesCategory;
import com.exam.domain.StIndustry;
import com.exam.domain.StIssueAuthority;
import com.exam.domain.StTechnical;
import com.exam.domain.StUserGroup;
import com.exam.domain.qo.LaborInfoRegisterQO;
import com.exam.domain.qo.StComplaintQo;
import com.exam.domain.vo.CUserVo;
import com.exam.domain.vo.StComplaintVo;
import com.exam.domain.vo.StUserCertificateEntityVo;
import com.exam.domain.vo.StUserCertificateVo;
import com.exam.service.ICCompanyService;
import com.exam.service.ICUserService;
import com.exam.service.IStCertificateTypeGroupService;
import com.exam.service.IStCertificateTypeService;
import com.exam.service.IStDutiesCategoryService;
import com.exam.service.IStIndustryService;
import com.exam.service.IStIssueAuthorityService;
import com.exam.service.IStTechnicalService;
import com.exam.service.IStUserCertificateService;
import com.exam.service.IStUserGroupService;
import com.exam.service.UserInfoService;
import com.exam.utils.CopyUtils;
import com.exam.utils.GyUtils;
import com.exam.utils.SequenceBean;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户中心
 *
 * <AUTHOR>
 * @Date 2021/6/15 16:10 下午
 * @Version 1.0
 */

@ApiSort(value = 1)
@RequestMapping("userInfoController")
@RestController
@Api(tags = "userInfoController(用户中心)")
public class UserInfoController {

    private static final Logger logger = LoggerFactory.getLogger(UserInfoController.class);
    //配置文件
    @Autowired
    Configure configure;
    @Autowired
    UserInfoService userInfoService;
    @Autowired
    IStCertificateTypeGroupService stCertificateTypeGroupService;
    @Autowired
    IStUserCertificateService stUserCertificateService;
    @Autowired
    IStUserGroupService stUserGroupService;
    @Autowired
    ICUserService iCUserService;
    @Autowired
    IStCertificateTypeService stCertificateTypeService;
    @Autowired
    IStDutiesCategoryService stDutiesCategoryService;
    @Autowired
    IStTechnicalService stTechnicalService;
    @Autowired
    IStIndustryService stIndustryService;
    @Autowired
    IStIssueAuthorityService stIssueAuthorityService;
    @Autowired
    ICCompanyService cCompanyService;


    /**
     * 1.0 查看个人中心-个人信息
     */
    @GetMapping(value = "/getUserInfoById")
    @ResponseBody
    public R<CUserVo> getUserInfo(Long userId) {
        //判定是否是查询当前用户
        return R.ok(userInfoService.getUserInfoById(userId));
    }

    /**
     * 1.1 查看个人信息
     */
    @PostMapping(value = "/getUserInfo")
    @ResponseBody
    public R<IPage<CUserVo>> getUserInfo(@RequestParam(value = "userId", required = false) String userId,
        @RequestParam(value = "pbKey", required = false) String pbKey,
        @RequestParam(value = "size", required = false) String size,
        @RequestParam(value = "current", required = false) String current,
        @RequestBody(required = false) CUserVo CUserVo) {
        Long crrUserId = LoginHelper.getUserId();
        //判定是否是查询当前用户
        if ("DQ".equals(pbKey)) {
            CUserVo.setUserId(crrUserId);

        } else {
            CUserVo = CUserVo == null ? new CUserVo() : CUserVo;
            if (GyUtils.isNull(userId)) {
                CUserVo.setUserId(0L);
            } else {
                CUserVo.setUserId(Long.parseLong(userId));
            }
        }
        if (GyUtils.isNull(CUserVo.getDeptId())) {
        CUserVo.setDeptId(LoginHelper.getProjectId());
//        CUserVo.setTenantId(LoginHelper.getTenantId());
        }
        IPage<CUserVo> list = userInfoService.getUserInfo(new Page<>(Integer.parseInt(current), Integer.parseInt(size)), CUserVo);
        return R.ok(list);
    }

    /**
     * 1.2 保存证件信息
     */
    @PostMapping(value = "/saveCertificate")
    @ResponseBody
    public R<String> saveCertificate(@ApiParam @RequestBody StUserCertificateVo stUserCertificateVO) {
        long id = userInfoService.saveCertificate(stUserCertificateVO);
        LoginUser cUser = LoginHelper.getLoginUser();
        //获取证书类型id,根据类型查询是否在证书组群表里，判断是否是自动划分组群的类型
        String certTypeId = stUserCertificateVO.getCertificateType();
        List<StCertificateTypeGroup> certTypeGroupList = stCertificateTypeGroupService
            .selectStCertificateTypeGroupByTypeId(certTypeId);
        //判断不为空，依次插入用户组群表
        if (certTypeGroupList.size() > 0) {
            for (int i = 0; i < certTypeGroupList.size(); i++) {
                StUserGroup stUserGroup = new StUserGroup();
                stUserGroup.setId(SequenceBean.getSequence());
                stUserGroup.setUserId(cUser.getUserId().longValue());
                stUserGroup.setGroupId(certTypeGroupList.get(i).getGroupId());
                stUserGroup.setDelFlag(0);
                stUserGroup.setCreateBy(cUser.getUserId().longValue());
                stUserGroup.setCreateTime(new Date());
                stUserGroup.setUpdateBy(cUser.getUserId().longValue());
                stUserGroup.setUpdateTime(new Date());
                //插入
                stUserGroupService.saveUserGroupInfo(stUserGroup);
            }
        }

        return R.ok(String.valueOf(id));
    }

    /**
     * 1.3 查看证件信息
     */
    @GetMapping(value = "/selectCertificateList")
    @ResponseBody
    public R<List<StUserCertificateEntityVo>> selectCertificateList(String userId, String pbKey) {
        Map conditionParam = new HashMap<String, String>();
        //判定是否是查询当前用户
        if ("DQ".equals(pbKey)) {
            LoginUser cUser = LoginHelper.getLoginUser();
            conditionParam.put("userId", cUser.getUserId());

        } else {
            conditionParam.put("userId", userId);
        }
        conditionParam.put("tenantId", LoginHelper.getTenantId());
        List<StUserCertificateVo> stUserCertificateVOList = userInfoService.selectCertificateList(conditionParam);

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("del_flag", CommonDataBaseConst.YES_OR_NO.NO.getCode());
        List<StDutiesCategory> stDutiesCategoryList = stDutiesCategoryService.list(queryWrapper);
        List<StTechnical> stTechnicalList = stTechnicalService.list(queryWrapper);
        List<StIndustry> stIndustryList = stIndustryService.list(queryWrapper);
        List<StIssueAuthority> stIssueAuthorityList = stIssueAuthorityService.list(queryWrapper);
        Map<String, String> stDutiesCategoryMap = stDutiesCategoryList.stream()
            .collect(Collectors.toMap(StDutiesCategory::getId, StDutiesCategory::getName));
        Map<String, String> stTechnicalMap = stTechnicalList.stream()
            .collect(Collectors.toMap(StTechnical::getId, StTechnical::getName));
        Map<String, String> stIndustryMap = stIndustryList.stream()
            .collect(Collectors.toMap(StIndustry::getId, StIndustry::getName));
        Map<String, String> stIssueAuthorityMap = stIssueAuthorityList.stream()
            .collect(Collectors.toMap(StIssueAuthority::getId, StIssueAuthority::getName));

        List<StUserCertificateEntityVo> newList = new ArrayList<>();
        for (int i = 0; i < stUserCertificateVOList.size(); i++) {
            StUserCertificateEntityVo stvo = new StUserCertificateEntityVo();
            CopyUtils.copyPropertiesEtyToVo(stUserCertificateVOList.get(i), stvo);
            stvo.setCompanyCode(stvo.getWork());
            stvo.setDuties(stDutiesCategoryMap.get(stvo.getDuties()));
            stvo.setTechnical(stTechnicalMap.get(stvo.getTechnical()));
            stvo.setIndustry(stIndustryMap.get(stvo.getIndustry()));
            stvo.setIssueAuthority(stIssueAuthorityMap.get(stvo.getIssueAuthority()));
            newList.add(stvo);
        }
        return R.ok(newList);
    }

    //批量上传证件

    /**
     * 1.4 批量保存证件信息
     */
    @PostMapping(value = "/saveCertificatePl")
    @ResponseBody
    public R<List> saveCertificatePl(@ApiParam MultipartFile file,
        @RequestParam(required = false, defaultValue = "{}") String stUploadRecordSaveVOJson) throws Exception {

        //"/Users/<USER>/Desktop/home/<USER>"
        List<StUserCertificateVo> failList = null;
        try {
            failList = userInfoService.saveCertificatePl(file, stUploadRecordSaveVOJson);
        } catch (Exception e) {
            logger.info("文件格式不正确");
            return R.fail("文件格式不正确！" + e.getMessage());
        }
        return R.ok(failList);
    }

    /**
     * 1.5 查看个人培训情况
     */
    @GetMapping(value = "/getTrainInfoByUserId")
    @ResponseBody
    public R<Map<String, List<Map<String, Object>>>> getTrainInfoByUserId(
        @RequestParam(value = "userId", required = false) String userId) {
        List<Map<String, Object>> list = userInfoService.getTrainInfoByUserId(userId);
        final Map<String, List<Map<String, Object>>> index = GyUtils.getCollectionIndexByKey(list, "timet");

        return R.ok(index);
    }

    /**
     * 1.6 查看个人考试情况
     */
    @GetMapping(value = "/getExamInfoByUserId")
    @ResponseBody
    public R<List<Map<String, Object>>> getExamInfoByUserId(@RequestParam(value = "userId", required = false) String userId
        , @RequestParam(value = "timeType", required = false) String timeType
        , @RequestParam(value = "examtime", required = false) String examtime) {
        List<Map<String, Object>> list = userInfoService.getExamInfoByUserId(userId, timeType, examtime);

        return R.ok(list);
    }

    /**
     * 1.9 保存身份证照片
     */
    @PostMapping(value = "/saveUserIdentityPic")
    @ResponseBody
    public R<String> saveUserIdentityPic(@RequestBody CUserVo CUserVo) {
        return R.ok(userInfoService.saveUserIdentityPic(CUserVo));
    }

    /**
     * 1.10 查看身份证照片
     */
    @GetMapping(value = "/selectUserIdentityPic")
    @ResponseBody
    public R<CUserVo> selectUserIdentityPic(@RequestParam(value = "userId") String userId) {
        return R.ok(userInfoService.selectUserIdentityPic(userId));
    }

    /**
     * 识别身份证信息
     */
    @PostMapping(value = "/identityIdCard")
    @ApiResponse(code = 1000, message = "操作成功")
    @ResponseBody
    public R<LaborInfoRegisterQO> identityIdCard(String id_card_side,
        @ApiParam(value = "上传的文件", required = true) MultipartFile file) {
        return R.ok(Idcard.idcard(id_card_side, file, configure));
    }

    /**
     * 1.17 分页查询申诉
     */
    @PostMapping(value = "/pageComplaint")
    @ResponseBody
    public R<IPage<StComplaintVo>> pageComplaint(@RequestBody StComplaintQo stComplaintQo) {
        return R.ok(userInfoService.pageComplaint(stComplaintQo));
    }

    /**
     * 1.17 职称下拉列表
     */
    @GetMapping(value = "/selectProfessionalTitleList")
    @ResponseBody
    public R<List<Map<String, String>>> selectProfessionalTitleList() {
        Long tenantId = LoginHelper.getTenantId();

        // 经管职称列表
        if ("3".equals(tenantId)) {
            return R.ok(CommonDataBaseConst.PROFESSIONAL_TITLE_MANAGEMENT.getSelectList());
        }
        return R.ok(CommonDataBaseConst.PROFESSIONAL_TITLE.getSelectList());
    }


    /**
     * 1.19 证件类别下拉列表（技能提升）
     */
    @PostMapping(value = "/selectCertificateTypeList")
    @ResponseBody
    public R<List<Map<String, Object>>> selectCertificateTypeList() {

        List<Map<String, Object>> certificateTypeList = CommonDataBaseConst.CERTIFICATE_TYPE.getSelectList();
        certificateTypeList.stream().forEach(ct -> {
            ct.put("professionalNameList",
                CommonDataBaseConst.PROFESSIONAL_NAME.getProfessionalNameList(ct.get("certificateType") + ""));
        });

        return R.ok(certificateTypeList);

    }

    /**
     * 1.20 政治面貌下拉列表
     */
    @GetMapping(value = "/selectPoliticalStatuslist")
    @ResponseBody
    public R<List<Map<String, String>>> selectPoliticalStatuslist() {
        return R.ok(CommonDataBaseConst.POLITICAL_STATUS.getSelectList());
    }
}
