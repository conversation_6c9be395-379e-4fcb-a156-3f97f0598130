package com.exam.domain.vo;

import lombok.Data;

import java.util.Date;

/**
 * （国铁局，企业）考试统计类
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
public class ExamStatisticsVo {

    /**
     * 考试id
     */
    private Long id;

    /**
     * 考试名
     */
    private String examName;

    /**
     * 租戶id
     */
    private Long tenantId;
    /**
     * 报名开始时间
     */
    private Date applyStartTime;
    /**
     * 报名结束时间
     */
    private Date applyEndTime;
    /**
     * 考试开始时间
     */
    private Date startTime;
    /**
     * 考试入场时间
     */
    private Date endTime;
    /**
     * 发布时间
     */
    private Date createTime;

    /**
     * 报名人数
     */
    private String userCount;

    /**
     * 考试状态
     */
    private String examStatus;

    /**
     * 通过人数
     */
    private String passUserCount;

    /**
     * 未通过人数
     */
    private String noPassUserCount;


}
