package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 区域的子，区域与省的关联业务对象 con_area_pro
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ConAreaProBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * area表主键id
     */
    @NotNull(message = "area表主键id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long areaId;

    /**
     * 省表主键id
     */
    @NotNull(message = "省表主键id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long provinceId;


}
