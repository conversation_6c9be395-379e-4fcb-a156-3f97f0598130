package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.constant.MockAnswerConsts.QuestionGenre;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;


/**
 * 答题卡视图对象 st_mock_answer_card
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@Data
@ExcelIgnoreUnannotated
public class StMockAnswerCardVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private String id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 课程类别id集合
     */
    @ExcelProperty(value = "课程类别id集合")
    private String questionTypeIds;

    /**
     * 课程类别名称集合
     */
    @ExcelProperty(value = "课程类别名称集合")
    private String questionTypeName;

    /**
     * 试题总分
     */
    @ExcelProperty(value = "试题总分")
    private Integer totalScore;

    /**
     * 答题卡状态
     */
    @ExcelProperty(value = "答题卡状态")
    private Long answerCardStatus;

    /**
     * 分数
     */
    @ExcelProperty(value = "分数")
    private String score;

    /**
     * 考试时长
     */
    @ExcelProperty(value = "考试时长")
    private Integer examLength;

    /**
     * 考试开始时间
     */
    @ExcelProperty(value = "考试开始时间")
    private Date examTime;

    /**
     * 租户 ID
     */
    @ExcelProperty(value = "租户 ID")
    private Long tenantId;

    /**
     * 所属项目
     */
    @ExcelProperty(value = "所属项目")
    private Long projectId;


    /**
     * 考试说明
     */
    private List<QuestionGenre> genreList;

    /**
     * 试题每类成绩
     */
    private List<AnswerScore> scoreList;

    /**
     * 试题显示Map
     */
    private Map<Integer, List<StMockAnswerCardDetailVo>> stMockAnswerCardDetailVOMap;


    @Data
    public static class AnswerScore {
        String name;
        Double score;
    }



}
