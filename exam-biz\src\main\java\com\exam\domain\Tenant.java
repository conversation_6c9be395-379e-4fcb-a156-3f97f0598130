package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.domain.BaseEntity2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 租户对象 tenant
 *
 * <AUTHOR>
 * @date 2023-10-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenant")
public class Tenant extends BaseEntity2 {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 租户名称
     */
    private String name;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 项目数量限制
     */
    private Long projectLimit;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 截至时间
     */
    private Date endTime;
    /**
     * 企业介绍
     */
    private String enterpriseIntroduction;

}
