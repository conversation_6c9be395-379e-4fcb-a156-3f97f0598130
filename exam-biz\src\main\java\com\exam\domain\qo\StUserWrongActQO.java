package com.exam.domain.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 用户错误信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StUserWrongActQO extends SearchQO implements Serializable {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 违规行为
     */
    private String wrongAction;

    /**
     * 考试名称
     */
    private String examName;

    /**
     * 编号
     */
    private String code;

    /**
     * 人脸识别错误编码
     */
    private String errorCode;

    /**
     * 是否提醒(1.是；0.否)
     */
    private String isRemind;

    /**
     * 是否强制提交(1.是；0.否)
     */
    private String isForceSubmit;

    /**
     * 创建日期
     */
    private String tabDate;

    /**
     * 所属项目
     */
    private String projectId;

    /**
     * 所属租户
     */
    private Long tenantId;

    /**
     * 选中的选项
     */
    private List<String> ids;
}
