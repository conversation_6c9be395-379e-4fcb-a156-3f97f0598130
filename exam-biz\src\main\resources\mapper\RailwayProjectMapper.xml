<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.exam.mapper.RailwayProjectMapper">
  <resultMap id="BaseResultMap" type="com.exam.domain.RailwayProject">
    <!--@mbg.generated-->
    <!--@Table railway_project-->
    <id column="rp_id" jdbcType="BIGINT" property="rpId" />
    <result column="rf_id" jdbcType="BIGINT" property="rfId" />
    <result column="rp_name" jdbcType="VARCHAR" property="rpName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    rp_id, rf_id, rp_name
  </sql>
    <select id="selectByUserId" resultType="com.exam.domain.RailwayProject">
        select b.*
        from railway_user_info a
        left join railway_project b on a.project_id = b.rp_id
        where a.user_id = #{userId}
        limit 1
    </select>
</mapper>
