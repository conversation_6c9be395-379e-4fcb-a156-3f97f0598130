package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import com.exam.common.helper.LoginHelper;
import com.exam.controller.AccountController;
import com.exam.utils.GyUtils;
import com.exam.utils.ReadFileUtil;
import com.exam.utils.SpringContextUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;


/**
 * 课程视图对象 st_classhour
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@Data
//@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
@Slf4j
@ExcelIgnoreUnannotated
public class StClasshourVo {

    private static final Logger logger = LoggerFactory.getLogger(StClasshourVo.class);
    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 所属课程id
     */
    @ExcelProperty(value = "所属课程id")
    private Long courseId;

    /**
     * 课时名称
     */
    @ExcelProperty(value = "课时名称")
    private String classhourName;

    /**
     * 课时序号
     */
    @ExcelProperty(value = "课时序号")
    private Long classhourSn;

    /**
     * 文件路径
     */
    @ExcelProperty(value = "文件路径")
    private String filePath;

    /**
     * 全路径
     */
    @ExcelProperty(value = "文件全路径路径")
    private String fullFilePath;

    /**
     * 课程状态
     */
    @ExcelProperty(value = "课程状态")
    private Long classhourStatus;

    /**
     * 课时时长
     */
    @ExcelProperty(value = "课时时长")
    private Long classhourDuration;

    /**
     * 播放时长
     */
    private Long playDuration;

    /**
     * 文件类型（1.视频；2.音频；3.文档）
     */
    @ExcelProperty(value = "文件类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=.视频；2.音频；3.文档")
    private Long fileType;

    /**
     * 文件名称
     */
    @ExcelProperty(value = "文件名称")
    private String fileName;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private Long tenantId;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private Long projectId;

    /**
     * 创建人id
     */
    @ExcelProperty(value = "创建人id")
    private Long createBy;

    /**
     * 创建人姓名
     */
    @ExcelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @ExcelProperty(value = "修改人id")
    private Long updateBy;

    /**
     * 修改时间
     */
    @ExcelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "播放进度id")
    private String playProgresId;

    @ApiModelProperty(value = "播放进度")
    private String progres;

    @ApiModelProperty(value = "是否为最后播放")
    private boolean lastPlay;

    public String getFullFilePath() {
        if(GyUtils.isNull(filePath)){
            return "";
        }
        logger.info("1111111111"+SpringContextUtil.getProperty("train.image.server"));
        return SpringContextUtil.getProperty("train.image.server")+filePath;
    }


    public String getClasshourDurationDis() {
        if(classhourDuration!=null){
            return ReadFileUtil.secondToTime(classhourDuration);
        }
        return "";
    }

    public String getProgresDis() {
//        if(classhourDuration!=null && classhourDuration!=0){
//            return (playDuration*100/classhourDuration) + "%";
//        }
        if(progres!=null){
            return progres + "%";
        }
        return "0%";
    }


}
