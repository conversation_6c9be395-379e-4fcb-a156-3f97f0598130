package com.exam.constant;

/**
 * <AUTHOR>
 * @date 2022/8/25 10:37
 */
public interface RedisKeyConstant {

    // 用户信息过期时间 一天 单位:秒
    Long USERINFO_EXPIRATION_TIME = 24 * 60 * 60L;
    // 结算月过期时间  一天
    Long CALCULATION_EXPIRATION_TIME = 24 * 60 * 60L;
    Long PROJECT_EXPIRATION_TIME = 2 * 60 * 60L;

    // 组织树集团-工区   reids key名称
    String COMPANY_WORKAREA_TREE_KEY =  "treeWorkAreaCC19";
    String REPORT_ORGMAP_KEY =  "report_orgmap";
    String USER_MAP =  "userMap";

    // 用户基础信息 key名称
    String BASIC_USERINFO_KEY =  "BASIC_USERINFO:";
    String BASIC_CALCULATION_KEY =  "BASIC_CALCULATION:";
    String BASIC_PROJECT_KEY =  "BASIC_PROJECT:";
    String QUESTION_GENRE_SCORE_KEY =  "QUESTION_GENRE_SCORE_";
    String EXAM_INFO_KEY =  "EXAM_INFO_";


    //短信验证码key
    String VERIFICATION_CODE =  "verification_code:";

}
