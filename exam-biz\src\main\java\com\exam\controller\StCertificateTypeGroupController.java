package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StCertificateTypeGroupBo;
import com.exam.domain.vo.StCertificateTypeGroupVo;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.service.IStCertificateTypeGroupService;
import com.exam.common.core.page.TableDataInfo;

/**
 * 证件类别组群关系
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/certificateTypeGroup")
public class StCertificateTypeGroupController extends BaseController {

    private final IStCertificateTypeGroupService iStCertificateTypeGroupService;

    /**
     * 查询证件类别组群关系列表
     */
    @SaCheckPermission("exam:certificateTypeGroup:list")
    @GetMapping("/list")
    public TableDataInfo<StCertificateTypeGroupVo> list(StCertificateTypeGroupBo bo, PageQuery pageQuery) {
        return iStCertificateTypeGroupService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出证件类别组群关系列表
     */
    @SaCheckPermission("exam:certificateTypeGroup:export")
    @Log(title = "证件类别组群关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StCertificateTypeGroupBo bo, HttpServletResponse response) {
        List<StCertificateTypeGroupVo> list = iStCertificateTypeGroupService.queryList(bo);
        ExcelUtil.exportExcel(list, "证件类别组群关系", StCertificateTypeGroupVo.class, response);
    }

    /**
     * 获取证件类别组群关系详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:certificateTypeGroup:query")
    @GetMapping("/{id}")
    public R<StCertificateTypeGroupVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iStCertificateTypeGroupService.queryById(id));
    }

    /**
     * 新增证件类别组群关系
     */
    @SaCheckPermission("exam:certificateTypeGroup:add")
    @Log(title = "证件类别组群关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StCertificateTypeGroupBo bo) {
        return toAjax(iStCertificateTypeGroupService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改证件类别组群关系
     */
    @SaCheckPermission("exam:certificateTypeGroup:edit")
    @Log(title = "证件类别组群关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StCertificateTypeGroupBo bo) {
        return toAjax(iStCertificateTypeGroupService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除证件类别组群关系
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:certificateTypeGroup:remove")
    @Log(title = "证件类别组群关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iStCertificateTypeGroupService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
