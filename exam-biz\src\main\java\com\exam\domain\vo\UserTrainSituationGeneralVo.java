package com.exam.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 培训情况总-vo
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserTrainSituationGeneralVO", description="培训情况总-vo")
public class UserTrainSituationGeneralVo implements Serializable {

    @ApiModelProperty(value = "播放时长")
    private long playDuration;

    @ApiModelProperty(value = "已完成课时数")
    private Integer finishClasshourCount;

    @ApiModelProperty(value = "列表数据")
    private List<UserTrainSituationVO> listData;



}

