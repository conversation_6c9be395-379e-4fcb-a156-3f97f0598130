package com.exam.domain.bo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.domain.qo.StBlogrollQo;
import com.exam.domain.vo.StBlogrollVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 友情链接业务对象 st_blogroll
 *
 * <AUTHOR>
 * @date 2023-11-27
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StBlogrollBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;



    /**
     * 链接名称
     */
    @NotBlank(message = "链接名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 链接地址
     */
    @NotBlank(message = "链接地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String link;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long tenantId;


}
