package com.exam.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 操作日志视图对象 operation_log
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class OperationLogVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 操作类型
     */
    @ExcelProperty(value = "操作类型")
    private String operationType;

    /**
     * 操作模块
     */
    @ExcelProperty(value = "操作模块")
    private String operationModule;

    /**
     * 操作内容
     */
    @ExcelProperty(value = "操作内容")
    private String operationContent;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间")
    private Date operationTime;

    /**
     * 操作人id
     */
    @ExcelProperty(value = "操作人id")
    private Long operationUserId;

    /**
     * 操作人名称
     */
    @ExcelProperty(value = "操作人名称")
    private String operationUserName;

    /**
     * 领域
     */
    @ExcelProperty(value = "领域")
    private String domainCode;

    /**
     * 导入文件路径
     */
    @ExcelProperty(value = "导入文件路径")
    private String importFilePath;


}
