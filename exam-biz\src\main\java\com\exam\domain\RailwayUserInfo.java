package com.exam.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 铁路局——个人信息
 */
@ApiModel(description = "铁路局——个人信息")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "railway_user_info")
public class RailwayUserInfo {
    /**
     * 个人信息Id
     */
    @TableId(value = "rui_id", type = IdType.AUTO)
    @ApiModelProperty(value = "个人信息Id")
    private Long ruiId;

    /**
     * 用户Id
     */
    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户Id")
    private Long userId;

    /**
     * 用户Id
     */
    @TableField(value = "tenant_id")
    @ApiModelProperty(value = "企业Id")
    private Long tenantId;

    /**
     * 用户Id
     */
    @TableField(value = "project_id")
    @ApiModelProperty(value = "子公司或项目的id")
    private Long projectId;

    /**
     * 姓名
     */
    @TableField(value = "rui_name")
    @ApiModelProperty(value = "姓名")
    private String ruiName;

    /**
     * 身份证号
     */
    @TableField(value = "rui_id_card")
    @ApiModelProperty(value = "身份证号")
    private String ruiIdCard;

    /**
     * 民族
     */
    @TableField(value = "rui_nation")
    @ApiModelProperty(value = "民族")
    private String ruiNation;

    /**
     * 性别：0女｜1男
     */
    @TableField(value = "rui_sex")
    @ApiModelProperty(value = "性别：0女｜1男")
    private Integer ruiSex;

    /**
     * 证件有效期限：开始时间
     */
    @TableField(value = "rui_start_date")
    @ApiModelProperty(value = "证件有效期限：开始时间")
    private String ruiStartDate;

    /**
     * 证件有效期限：结束时间
     */
    @TableField(value = "rui_end_date")
    @ApiModelProperty(value = "证件有效期限：结束时间")
    private String ruiEndDate;

    /**
     * 是否永久：0否｜1是
     */
    @TableField(value = "rui_is_perpetual")
    @ApiModelProperty(value = "是否永久：0否｜1是")
    private Integer ruiIsPerpetual;

    /**
     * 出生日期
     */
    @TableField(value = "rui_birthday")
    @ApiModelProperty(value = "出生日期")
    private String ruiBirthday;

    /**
     * 年龄
     */
    @TableField(value = "rui_age")
    @ApiModelProperty(value = "年龄")
    private Long ruiAge;

    /**
     * 证书注册单位
     */
    @TableField(value = "rui_certificate_unit")
    @ApiModelProperty(value = "证书注册单位")
    private String ruiCertificateUnit;

    /**
     * 一寸照片ssoId
     */
    @TableField(value = "rui_oneInch")
    @ApiModelProperty(value = "一寸照片")
    private String ruiOneInch;

    /**
     * 身份证照片ssoId 人像面
     */
    @TableField(value = "rui_id_card_front")
    @ApiModelProperty(value = "身份证照片 人像面")
    private String ruiIdCardFrontSsoId;

    /**
     * 身份证照片ssoId 国徽面
     */
    @TableField(value = "rui_id_card_back")
    @ApiModelProperty(value = "身份证照片 国徽面")
    private String ruiIdCardBackSsoId;

    /**
     * 社保证明多个证明多个Id 用逗号隔开
     */
    @TableField(value = "rui_social_security")
    @ApiModelProperty(value = "社保证明多个证明多个 用逗号隔开")
    private String ruiSocialSecuritySsoIds;

    /**
     * 劳动合同，可以多个劳动合同，多个ssoid用逗号隔开
     */
    @TableField(value = "rui_labor_contract")
    @ApiModelProperty(value = "劳动合同，可以多个劳动合同，多个用逗号隔开")
    private String ruiLaborContractSsoIds;

    /**
     * 行业证书，可多个，用逗号隔开
     */
    @TableField(value = "rui_Industry_certificate")
    @ApiModelProperty(value = "行业证书，可多个，用逗号隔开")
    private String ruiIndustryCertificate;


    /**
     * 行业证书，可多个，用逗号隔开
     */
    @TableField(value = "declaration_type")
    @ApiModelProperty(value = "申报类型")
    private String declarationType;


    /**
     * 省id
     */
    @TableField(value = "province_id")
    @ApiModelProperty(value = "省id")
    private Long provinceId;

    /**
     * 市id
     */
    @TableField(value = "city_id")
    @ApiModelProperty(value = "市id")
    private Long cityId;

    /**
     * 区id
     */
    @TableField(value = "county_id")
    @ApiModelProperty(value = "区id")
    private Long countyId;


    /**
     * 省
     */
    @TableField(value = "province")
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 市
     */
    @TableField(value = "city")
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区
     */
    @TableField(value = "county")
    @ApiModelProperty(value = "区")
    private String county;

    /**
     * 创建者
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建者")
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "更新者")
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "rui_approve_status")
    @ApiModelProperty(value = "审核状态:0未审核｜1通过｜2驳回")
    private Integer ruiApproveStatus;
}
