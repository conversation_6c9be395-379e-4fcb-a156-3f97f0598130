package com.exam.domain.vo;

import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 试题类别抽题比例DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StQuestionProportionDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 试题类别
     */
    @NotNull(message = "试题类别不能未空")
    private Long questionTypeId;

    /**
     * 试题比例
     */
    @NotNull(message = "试题比例不能未空")
    private Integer questionProportion;

}
