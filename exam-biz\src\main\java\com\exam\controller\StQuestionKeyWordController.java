package com.exam.controller;

import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StQuestionKeyWordBo;
import com.exam.domain.vo.StQuestionKeyWordVo;
import com.exam.service.IStQuestionKeyWordService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.common.core.page.TableDataInfo;

/**
 * 试题关键词
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/questionKeyWord")
public class StQuestionKeyWordController extends BaseController {

    private final IStQuestionKeyWordService iStQuestionKeyWordService;

    /**
     * 查询试题关键词列表
     */
    @SaCheckPermission("exam:questionKeyWord:list")
    @GetMapping("/list")
    public TableDataInfo<StQuestionKeyWordVo> list(StQuestionKeyWordBo bo, PageQuery pageQuery) {
        return iStQuestionKeyWordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出试题关键词列表
     */
    @SaCheckPermission("exam:questionKeyWord:export")
    @Log(title = "试题关键词", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StQuestionKeyWordBo bo, HttpServletResponse response) {
        List<StQuestionKeyWordVo> list = iStQuestionKeyWordService.queryList(bo);
        ExcelUtil.exportExcel(list, "试题关键词", StQuestionKeyWordVo.class, response);
    }

    /**
     * 获取试题关键词详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:questionKeyWord:query")
    @GetMapping("/{id}")
    public R<StQuestionKeyWordVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStQuestionKeyWordService.queryById(id));
    }

    /**
     * 新增试题关键词
     */
    @SaCheckPermission("exam:questionKeyWord:add")
    @Log(title = "试题关键词", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StQuestionKeyWordBo bo) {
        return toAjax(iStQuestionKeyWordService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改试题关键词
     */
    @SaCheckPermission("exam:questionKeyWord:edit")
    @Log(title = "试题关键词", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StQuestionKeyWordBo bo) {
        return toAjax(iStQuestionKeyWordService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除试题关键词
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:questionKeyWord:remove")
    @Log(title = "试题关键词", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable Long[] ids) {
        return toAjax(iStQuestionKeyWordService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
