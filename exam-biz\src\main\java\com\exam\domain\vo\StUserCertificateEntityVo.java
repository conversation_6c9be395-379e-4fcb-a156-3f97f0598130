package com.exam.domain.vo;

import com.exam.common.utils.StringUtils;
import com.exam.utils.GyUtils;
import com.exam.utils.SpringContextUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 用户证件表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
@ApiModel(value="StUserCertificateEntityVO", description="用户证件表")
public class StUserCertificateEntityVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "行业")
    private String industry;

    @ApiModelProperty(value = "工作单位")
    private String work;

    @ApiModelProperty(value = "单位Code")
    private String companyCode;

    @ApiModelProperty(value = "证件类别")
    private String certificateType;

    @ApiModelProperty(value = "证件类别名称")
    private String certificateTypeName;

    @ApiModelProperty(value = "专业名称")
    private String professionalName;

    @ApiModelProperty(value = "职务")
    private String duties;

    @ApiModelProperty(value = "技术职称")
    private String technical;

    @ApiModelProperty(value = "证书编号")
    private String certificateNo;

    @ApiModelProperty(value = "发证日期")
    private String issueDate;

    @ApiModelProperty(value = "有效期")
    private String validityDate;

    @ApiModelProperty(value = "发证机关")
    private String issueAuthority;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "判定用当前时间")
    private String nowTime;
    private String fullPicPath;
    public String getFullPicPath() {
        if (GyUtils.isNull(filePath)) {
            return "";
        }
        if (StringUtils.startsWith(filePath, "/img")) {
            return SpringContextUtil.getProperty("train.upload.local.server") + filePath;
        } else if (StringUtils.startsWith(filePath, "img")) {
            return SpringContextUtil.getProperty("train.upload.local.server") + "/" + filePath;
        } else {
            return SpringContextUtil.getProperty("train.image.server") + filePath;
        }
    }
    @ApiModelProperty(value = "领域 1：安全教育培训  2：技能提升 3：经管考试")
    private String domainCode;

}
