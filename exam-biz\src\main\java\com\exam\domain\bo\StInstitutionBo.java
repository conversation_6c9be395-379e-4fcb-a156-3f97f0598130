package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 制度办法业务对象 st_institution
 *
 * <AUTHOR>
 * @date 2023-11-27
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StInstitutionBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空", groups = {AddGroup.class, EditGroup.class})
    private String title;

    /**
     * 附件
     */
    @NotBlank(message = "附件不能为空", groups = {AddGroup.class, EditGroup.class})
    private String annex;

    /**
     * 是否置顶
     */
    @NotNull(message = "是否置顶不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long isTop;

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;

    /**
     * 所属项目
     */
    @NotNull(message = "所属项目不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;


}
