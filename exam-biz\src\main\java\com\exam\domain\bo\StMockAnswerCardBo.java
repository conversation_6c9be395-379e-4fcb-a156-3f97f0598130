package com.exam.domain.bo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.domain.qo.SearchQO;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 答题卡业务对象 st_mock_answer_card
 *
 * <AUTHOR>
 * @date 2023-11-14
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StMockAnswerCardBo extends SearchQO {

    /**
     * id
     */
    @NotBlank(message = "id不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 课程类别id集合
     */
    @NotBlank(message = "课程类别id集合不能为空", groups = {AddGroup.class, EditGroup.class})
    private String questionTypeIds;

    /**
     * 课程类别名称集合
     */
    @NotBlank(message = "课程类别名称集合不能为空", groups = {AddGroup.class, EditGroup.class})
    private String questionTypeName;

    /**
     * 试题总分
     */
    @NotNull(message = "试题总分不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long totalScore;

    /**
     * 答题卡状态
     */
    @NotNull(message = "答题卡状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long answerCardStatus;

    /**
     * 分数
     */
    @NotNull(message = "分数不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal score;

    /**
     * 考试时长
     */
    @NotNull(message = "考试时长不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long examLength;

    /**
     * 考试开始时间
     */
    @NotNull(message = "考试开始时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date examTime;

    /**
     * 租户 ID
     */
    @NotNull(message = "租户 ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;

    /**
     * 所属项目
     */
    @NotNull(message = "所属项目 不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long projectId;

    // 下列属性 来自于 原来的 BaseEntity。
    /**
     * 搜索值
     */
    @TableField(exist = false)
    private String searchValue;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
