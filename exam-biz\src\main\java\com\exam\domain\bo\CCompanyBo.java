package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 公司业务对象 c_company
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class CCompanyBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 公司全名称
     */
    @NotBlank(message = "公司全名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 公司简称
     */
    @NotBlank(message = "公司简称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String simpleName;

    /**
     * 产权单位（简）
     */
    @NotBlank(message = "产权单位（简）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String firm;

    /**
     * 公司级别（0：局级；1：处级；2：分公司；3:外部单位）
     */
    @NotNull(message = "公司级别（0：局级；1：处级；2：分公司；3:外部单位）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long level;

    /**
     * 父级ID(-1为顶级)
     */
    @NotNull(message = "父级ID(-1为顶级)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fatherId;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String linkman;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tel;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long longitude;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long latitude;

    /**
     * 序号
     */
    @NotNull(message = "序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sn;

    /**
     * 公司编码(系统编码)
     */
    @NotBlank(message = "公司编码(系统编码)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyCode;

    /**
     * 是否为内部单位，0为是，1为否
     */
    @NotNull(message = "是否为内部单位，0为是，1为否不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isInternal;

    /**
     * 法人代表名称
     */
    @NotBlank(message = "法人代表名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String legalPerson;

    /**
     * 法人代表身份证号
     */
    @NotBlank(message = "法人代表身份证号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String legalPersonIdCard;

    /**
     * 营业执照号码
     */
    @NotBlank(message = "营业执照号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String businessNo;

    /**
     * 组织机构代码
     */
    @NotBlank(message = "组织机构代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String organizationNo;

    /**
     * 税务登记证号
     */
    @NotBlank(message = "税务登记证号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taxRegistrationNo;

    /**
     * 安全生产许可证编号
     */
    @NotBlank(message = "安全生产许可证编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String workSafetyNo;

    /**
     * 纳税人识别号
     */
    @NotBlank(message = "纳税人识别号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taxpayerIdentificationNumber;

    /**
     * 银行开户户名
     */
    @NotBlank(message = "银行开户户名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountName;

    /**
     * 银行账号
     */
    @NotBlank(message = "银行账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountNo;

    /**
     * 单位地址
     */
    @NotBlank(message = "单位地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String concatTel;

    /**
     * 资质证书编号
     */
    @NotBlank(message = "资质证书编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String qualificationCertificateNo;

    /**
     * 公司编码(显示使用)
     */
    @NotBlank(message = "公司编码(显示使用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyNo;

    /**
     * 注册时间(显示使用)
     */
    @NotBlank(message = "注册时间(显示使用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String signTimeStr;

    /**
     * 编辑时间(显示使用)
     */
    @NotBlank(message = "编辑时间(显示使用)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String updateTimeStr;

    /**
     * 创建人id
     */
    @NotNull(message = "创建人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long signUserId;

    /**
     * 编辑人id
     */
    @NotNull(message = "编辑人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateUserId;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @NotNull(message = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dataStatus;

    /**
     * 开户银行
     */
    @NotBlank(message = "开户银行不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountBank;

    /**
     * 上级节点编码
     */
    @NotBlank(message = "上级节点编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentOrgCode;

    /**
     * 节点编码
     */
    @NotBlank(message = "节点编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgCode;

    /**
     * 联系电话 区号
     */
    @NotBlank(message = "联系电话 区号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phoneNumberCode;

    /**
     * 国外区域：1      国内区域：0
     */
    @NotNull(message = "国外区域：1      国内区域：0不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isOverseas;

    /**
     * 所在区县ID
     */
    @NotNull(message = "所在区县ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long countyId;


}
