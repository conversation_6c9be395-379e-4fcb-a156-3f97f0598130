package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 【请填写功能名称】视图对象 user_error_pic
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class UserErrorPicVo {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 身份证正面照片地址
     */
    @ExcelProperty(value = "身份证正面照片地址")
    private String idImageUrl;

    /**
     * 错误图片base64编码
     */
    @ExcelProperty(value = "错误图片base64编码")
    private String errorPicBase;

    /**
     * 注册时间(显示使用)
     */
    @ExcelProperty(value = "注册时间(显示使用)")
    private String signTimeStr;

    /**
     * 编辑时间(显示使用)
     */
    @ExcelProperty(value = "编辑时间(显示使用)")
    private String updateTimeStr;

    /**
     * 创建人id
     */
    @ExcelProperty(value = "创建人id")
    private Long signUserId;

    /**
     * 编辑人id
     */
    @ExcelProperty(value = "编辑人id")
    private Long updateUserId;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @ExcelProperty(value = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))")
    private Long dataStatus;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息")
    private String errorMessage;

    /**
     * 错误编码
     */
    @ExcelProperty(value = "错误编码")
    private String errorCode;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String userName;


}
