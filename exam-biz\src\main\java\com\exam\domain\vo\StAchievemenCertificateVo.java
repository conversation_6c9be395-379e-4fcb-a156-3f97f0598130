package com.exam.domain.vo;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.exam.common.core.domain.BaseEntity;
import com.exam.utils.EntityUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;


/**
 * 成绩证书视图对象 st_achievemen_certificate
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StAchievemenCertificateVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private String userId;

//    /**
//     * 用户姓名
//     */
//    @ExcelProperty(value = "用户姓名")
//    private String userName;

    /**
     * 答题卡id
     */
    @ExcelProperty(value = "答题卡id")
    private Long answerCardId;

    /**
     * 证书编号
     */
    @ExcelProperty(value = "证书编号")
    private String certificateNo;

    /**
     * 单选题得分
     */
    @ExcelProperty(value = "单选题得分")
    private  double singleChoiceScore;

    /**
     * 多选题得分
     */
    @ExcelProperty(value = "多选题得分")
    private  double multipleChoiceScore;

    /**
     * 判断题得分
     */
    @ExcelProperty(value = "判断题得分")
    private  double judgeScore;

    /**
     * 填空题得分
     */
    @ExcelProperty(value = "填空题得分")
    private  double completionScore;

    /**
     * 简答题得分
     */
    @ExcelProperty(value = "简答题得分")
    private  double shortAnswerScore;

    /**
     * 案例题得分
     */
    @ExcelProperty(value = "案例题得分")
    private  double caseScore;

    /**
     * 总分数
     */
    @ExcelProperty(value = "总分数")
    private  double totalScore;

    /**
     * 成绩级别
     */
    @ExcelProperty(value = "成绩级别")
    private String achievemenLevel;

    /**
     * 是否删除(1.是；0.否)
     */
    @ExcelProperty(value = "是否删除(1.是；0.否)")
    private Integer delFlag;

    @ExcelProperty(value = "第几次补考")
    private Long retestTimes;

    /**
     * 考试名称
     */
    @ExcelProperty(value = "考试名称")
    private String examName;

    /**
     * 允许补考次数
     */
    @ExcelProperty(value = "允许补考次数")
    private Integer allowedTimes;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String userName;

}
