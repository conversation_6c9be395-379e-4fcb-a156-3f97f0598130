package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户证件对象 st_user_certificate
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_user_certificate")
public class StUserCertificate extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户
     */
    private Long userId;
    /**
     * 行业
     */
    private String industry;
    /**
     * 工作单位
     */
    private String workName;
    /**
     * 证件类别
     */
    private String certificateType;
    /**
     * 专业名称
     */
    private String professionalName;
    /**
     * 职务
     */
    private String duties;
    /**
     * 技术职称
     */
    private String technical;
    /**
     * 证书编号
     */
    private String certificateNo;
    /**
     * 发证日期
     */
    private String issueDate;
    /**
     * 有效期
     */
    private String validityDate;
    /**
     * 发证机关
     */
    private String issueAuthority;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 是否删除(1.是；0.否)
     */
    private Integer delFlag;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人id
     */
    private Long createBy;
    /**
     * 修改人id
     */
    private Long updateBy;
    /**
     * 领域 1：安全教育培训  2：技能提升 3：经管考试
     */
//    private String domainCode;

}
