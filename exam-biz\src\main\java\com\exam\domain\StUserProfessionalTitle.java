package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户职称关联对象 st_user_professional_title
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_user_professional_title")
public class StUserProfessionalTitle extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 职称（1：正高级工程师，2：高级工程师、3：中级工程师、4：助理工程师、5：高级技师、6：技师）
     */
    private String professionalTitle;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 是否删除(1.是；0.否)
     */
    private Integer delFlag;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人id
     */
    private Long createBy;
    /**
     * 修改人id
     */
    private Long updateBy;

}
