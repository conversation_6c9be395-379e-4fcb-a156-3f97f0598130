package com.exam.domain.vo;


import com.exam.common.core.domain.entity.SysUser;
import com.exam.domain.RailwayUserInfo;
import com.exam.domain.StUserExam;
import com.exam.domain.StUserExamVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "查询个人中心")
public class RailwayUserInfoVo extends RailwayUserInfo {

    private static final long serialVersionUID = 1L;

    /**
     * 账户信息
     */
    @ApiModelProperty("账号信息")
    private SysUser sysUser;



    @ApiModelProperty("准考证")
    private List<StUserExamVo> examList;




}
