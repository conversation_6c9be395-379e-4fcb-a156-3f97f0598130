package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity2;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 试题类型业务对象 st_question_genre
 *
 * <AUTHOR>
 * @date 2023-10-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StQuestionGenreBo extends BaseEntity2 {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 试题类型名称
     */
    @NotBlank(message = "试题类型名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionGenreName;

    /**
     * 分值
     */
    @NotNull(message = "分值不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long questionScore;

    /**
     * 类型排序
     */
    @NotNull(message = "类型排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long questionGenreSn;

    /**
     * 是否删除(1.是；0.否)
     */
    @NotNull(message = "是否删除(1.是；0.否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer delFlag;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date createTime;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createBy;

    /**
     * 修改人id
     */
    @NotNull(message = "修改人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long updateBy;


}
