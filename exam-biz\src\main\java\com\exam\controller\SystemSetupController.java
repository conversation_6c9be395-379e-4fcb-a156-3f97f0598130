package com.exam.controller;

import com.exam.common.core.domain.R;
import com.exam.domain.StFunctionOpen;
import com.exam.domain.vo.StFunctionOpenDTO;
import com.exam.domain.vo.StFunctionOpenVo;
import com.exam.domain.vo.StRotationPictureVo;
import com.exam.service.IExamService;
import com.exam.service.IStFunctionOpenService;
import com.exam.service.SystemSetupService;
import io.swagger.annotations.ApiParam;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统设置管理端
 *
 * <AUTHOR>
 */
@RequestMapping("SystemSetupController")
@RestController
public class SystemSetupController {

    private static final Logger logger = LoggerFactory.getLogger(SystemSetupController.class);
    @Autowired
    IExamService examService;
    @Autowired
    SystemSetupService systemSetupService;
    @Autowired
    IStFunctionOpenService functionOpenService;


    /**
     * 获取轮播图信息
     *
     * @param differentiate 区分（web,1;  APP,2
     */
    @GetMapping(value = "/getRotationPicture")
    @ResponseBody
    public R<StRotationPictureVo> getRotationPicture(String differentiate) {
        Map conditionParam = new HashMap();
        conditionParam.put("differentiate", differentiate);
        return R.ok(systemSetupService.getRotationPicture(conditionParam));
    }


    /**
     * 保存轮播图信息
     */
    @PostMapping(value = "/saveRotationPicture")
    @ResponseBody
    public R<String> saveRotationPicture(@ApiParam @RequestBody StRotationPictureVo stRotationPictureVO) {
        return R.ok(systemSetupService.saveRotationPicture(stRotationPictureVO));
    }


    /**
     * 添加推荐课程
     *
     * @param courseIds 课程id(多个逗号分割)
     */
    @GetMapping(value = "/addRecommendedCourse")
    @ResponseBody
    public R<String> addRecommendedCourse(String courseIds) {
        return R.ok(systemSetupService.addRecommendedCourse(courseIds));
    }


    /**
     * 删除推荐课程
     *
     * @param courseIds 课程id(多个逗号分割)
     */
    @GetMapping(value = "/delRecommendedCourse")
    @ResponseBody
    public R<String> delRecommendedCourse(String courseIds) {
        return R.ok(systemSetupService.delRecommendedCourse(courseIds));
    }


    /**
     * 获取功能入口
     */
    @GetMapping(value = "/getFunctionOpen")
    @ResponseBody
    public R<StFunctionOpen> getFunctionOpen(Integer type) {
        return R.ok(functionOpenService.getFunctionOpen(type));
    }


    /**
     * 保存功能入口
     */
    @PostMapping(value = "/saveFunctionOpen")
    @ResponseBody
    public R<Void> saveFunctionOpen(@ApiParam @RequestBody StFunctionOpenDTO stFunctionOpenDTO) {
        functionOpenService.saveStFunctionOpen(stFunctionOpenDTO);
        return R.ok();
    }


    /**
     * 查询功能入口
     */
    @PostMapping(value = "/listFunctionOpen")
    @ResponseBody
    public R<List<StFunctionOpenVo>> listFunctionOpen() {
        List<StFunctionOpenVo> voList = functionOpenService.listFunctionOpen();
        return R.ok(voList);
    }


    /**
     * 是否拥有功能入口权限
     *
     * @param type 类型1：岗位属性，2：模拟考试
     */
    @GetMapping(value = "/haveAuthFunctionOpen")
    @ResponseBody
    public R<Integer> haveAuthFunctionOpen(Integer type) {
        return R.ok(functionOpenService.haveAuthFunctionOpen(type));
    }
}
