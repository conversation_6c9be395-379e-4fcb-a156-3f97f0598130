package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 临时处理数据对象 temp_user_table
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("temp_user_table")
public class TempUserTable extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;
    /**
     *
     */
    private String companyCode;
    /**
     *
     */
    private String projectName;
    /**
     *
     */
    private String name;
    /**
     *
     */
    private String phoneNum;
    /**
     *
     */
    private String idNum;
    /**
     *
     */
    private String deptName;
    /**
     *
     */
    private String jobName;
    /**
     *
     */
    private String nation;
    /**
     *
     */
    private String sex;
    /**
     *
     */
    private String email;
    /**
     *
     */
    private String userType;
    /**
     *
     */
    private String isLeader;
    /**
     *
     */
    private String insertResult;
    /**
     * 批量标识
     */
    private String flag;

}
