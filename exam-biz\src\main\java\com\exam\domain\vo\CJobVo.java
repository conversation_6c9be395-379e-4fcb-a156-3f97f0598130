package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 岗位视图对象 c_job
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class CJobVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 职务名称
     */
    @ExcelProperty(value = "职务名称")
    private String name;

    /**
     * 是否管理者（0：否；1：是；）
     */
    @ExcelProperty(value = "是否管理者", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=：否；1：是；")
    private Long isManager;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Long sn;

    /**
     * 数据状态(0:删除；1:启用；2：停用(暂时不用停用))
     */
    @ExcelProperty(value = "数据状态(0:删除；1:启用；2：停用(暂时不用停用))")
    private Long dataStatus;

    /**
     * 所属公司
     */
    @ExcelProperty(value = "所属公司")
    private String companyCode;

    /**
     * 注册时间(显示使用)
     */
    @ExcelProperty(value = "注册时间(显示使用)")
    private String signTimeStr;

    /**
     * 编辑时间(显示使用)
     */
    @ExcelProperty(value = "编辑时间(显示使用)")
    private String updateTimeStr;

    /**
     * 创建人id
     */
    @ExcelProperty(value = "创建人id")
    private Long signUserId;

    /**
     * 编辑人id
     */
    @ExcelProperty(value = "编辑人id")
    private Long updateUserId;

    /**
     * 上级节点编码
     */
    @ExcelProperty(value = "上级节点编码")
    private String parentOrgCode;

    /**
     * 是否为外部岗位(0：否；1：是；)
     */
    @ExcelProperty(value = "是否为外部岗位(0：否；1：是；)")
    private Long isExternal;

    /**
     * 节点编码
     */
    @ExcelProperty(value = "节点编码")
    private String orgCode;


}
