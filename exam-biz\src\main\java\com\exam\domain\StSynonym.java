package com.exam.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 同义词替换对象 st_synonym
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_synonym")
public class StSynonym extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 同义词名称，逗号分割
     */
    private String synonymName;
    /**
     * 同义词替换名称
     */
    private String replaceName;
    /**
     * 顺序
     */
    private Long sn;
    /**
     * 租户 ID
     */
    private Long tenantId;

}
