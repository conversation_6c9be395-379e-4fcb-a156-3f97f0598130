package com.exam.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.exam.domain.RailwayMessage;
import com.exam.domain.RailwayMessageDto;
import com.exam.domain.qo.SearchQO;
import com.exam.domain.vo.RailwayMessagePageVo;
import com.exam.domain.vo.RailwayMessageVo;

public interface RailwayMessageService extends IService<RailwayMessage>{


    Boolean publishMessage(RailwayMessageDto railwayMessageDto);

    RailwayMessageVo getMessageById(Long rmId);

    Boolean updateMessageRead();

    IPage<RailwayMessagePageVo> fingMessagePage(SearchQO searchQO);

    Boolean updateMessageReadOne(Long rmId);
}
