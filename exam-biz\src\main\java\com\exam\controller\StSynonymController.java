package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.page.TableDataInfo;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.bo.StSynonymBo;
import com.exam.domain.vo.StSynonymVo;
import com.exam.service.IStSynonymService;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 同义词替换
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/synonym")
public class StSynonymController extends BaseController {

    private final IStSynonymService iStSynonymService;

    /**
     * 查询同义词替换列表
     */
    @SaCheckPermission("exam:synonym:list")
    @GetMapping("/list")
    public TableDataInfo<StSynonymVo> list(StSynonymBo bo, PageQuery pageQuery) {
        return iStSynonymService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出同义词替换列表
     */
    @SaCheckPermission("exam:synonym:export")
    @Log(title = "同义词替换", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StSynonymBo bo, HttpServletResponse response) {
        List<StSynonymVo> list = iStSynonymService.queryList(bo);
        ExcelUtil.exportExcel(list, "同义词替换", StSynonymVo.class, response);
    }

    /**
     * 获取同义词替换详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:synonym:query")
    @GetMapping("/{id}")
    public R<StSynonymVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStSynonymService.queryById(id));
    }

    /**
     * 新增同义词替换
     */
    @SaCheckPermission("exam:synonym:add")
    @Log(title = "同义词替换", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StSynonymBo bo) {
        return toAjax(iStSynonymService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改同义词替换
     */
    @SaCheckPermission("exam:synonym:edit")
    @Log(title = "同义词替换", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StSynonymBo bo) {
        return toAjax(iStSynonymService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除同义词替换
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:synonym:remove")
    @Log(title = "同义词替换", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable Long[] ids) {
        return toAjax(iStSynonymService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
