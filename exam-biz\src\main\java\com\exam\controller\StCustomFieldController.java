package com.exam.controller;


import com.exam.common.core.domain.R;
import com.exam.domain.StCustomField;
import com.exam.service.IStCustomFieldService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 考试后台管理端
 * <AUTHOR>
 * @Date 2022/04/09
 * @Version 1.0
 */
@RequestMapping("stCustomFiled")
@RestController
public class StCustomFieldController {

    @Autowired
    IStCustomFieldService stCustomFieldService;

    /**
     * 1.0 根据类型查找
     * @param type
     * @return
     */
    @GetMapping(value = "/getByType")
    @ResponseBody
    public R<StCustomField> getByType(String type) {
        return R.ok(stCustomFieldService.getByType(type));
    }

    /**
     * 1.1 保存自定义列
     * @param stCustomField
     * @return
     */
    @PostMapping(value = "/saveCustomField")
    @ResponseBody
    public R<Void> saveCustomField(@ApiParam @RequestBody StCustomField stCustomField) {
        stCustomFieldService.saveCustomField(stCustomField);
        return R.ok();
    }

}
