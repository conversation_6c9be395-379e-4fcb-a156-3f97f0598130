package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 同义词替换视图对象 st_synonym
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@Data
@ExcelIgnoreUnannotated
public class StSynonymVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 同义词名称，逗号分割
     */
    @ExcelProperty(value = "同义词名称，逗号分割")
    private String synonymName;

    /**
     * 同义词替换名称
     */
    @ExcelProperty(value = "同义词替换名称")
    private String replaceName;

    /**
     * 顺序
     */
    @ExcelProperty(value = "顺序")
    private Long sn;

    /**
     * 租户 ID
     */
    @ExcelProperty(value = "租户 ID")
    private Long tenantId;


}
