package com.exam.domain.qo;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 模拟试题表QO
 *
 * <AUTHOR>
 * @since 2021-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StMockQuestionQO extends SearchQO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 试题类别Id
     */
    private Long questionTypeId;

    /**
     * 试题类别IdStr
     */
    private String questionTypeIdStr;

    /**
     * 试题类别
     */
    private Long questionGenre;

    /**
     * 试题内容
     */
    private String questionContent;

    /**
     * 选择日期
     */
    private String questionTime;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 试题类别Ids
     */
    private String[] questionTypeIds;

}

