package com.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.R;
import com.exam.constant.CommonDataBaseConst;
import com.exam.domain.qo.StUserWrongActQO;
import com.exam.domain.vo.StUserWrongActVo;
import com.exam.service.IStUserWrongActService;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 违规动作
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/userWrongAct")
public class StUserWrongActController extends BaseController {

    @Resource
    private final IStUserWrongActService stUserWrongActService;
//
//    /**
//     * 查询用户组群关系列表
//     */
//    @SaCheckPermission("exam:userWrongAct:list")
//    @GetMapping("/list")
//    public TableDataInfo<StUserWrongActVo> list(StUserWrongActBo bo, PageQuery pageQuery) {
//        return iStUserWrongActService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出用户组群关系列表
//     */
//    @SaCheckPermission("exam:userWrongAct:export")
//    @Log(title = "用户组群关系", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(StUserWrongActBo bo, HttpServletResponse response) {
//        List<StUserWrongActVo> list = iStUserWrongActService.queryList(bo);
//        ExcelUtil.exportExcel(list, "用户组群关系", StUserWrongActVo.class, response);
//    }
//
//    /**
//     * 获取用户组群关系详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("exam:userWrongAct:query")
//    @GetMapping("/{id}")
//    public R<StUserWrongActVo> getInfo(@NotNull(message = "主键不能为空")
//    @PathVariable Long id) {
//        return R.ok(iStUserWrongActService.queryById(id));
//    }
//
//    /**
//     * 新增用户组群关系
//     */
//    @SaCheckPermission("exam:userWrongAct:add")
//    @Log(title = "用户组群关系", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody StUserWrongActBo bo) {
//        return toAjax(iStUserWrongActService.insertByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 修改用户组群关系
//     */
//    @SaCheckPermission("exam:userWrongAct:edit")
//    @Log(title = "用户组群关系", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StUserWrongActBo bo) {
//        return toAjax(iStUserWrongActService.updateByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 删除用户组群关系
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("exam:userWrongAct:remove")
//    @Log(title = "用户组群关系", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//    @PathVariable Long[] ids) {
//        return toAjax(iStUserWrongActService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
//    }


    /**
     * 查询违规动作(分页)
     */
    @PostMapping(value = "/queryUserWrongActList")
    @ResponseBody
    public R<IPage<StUserWrongActVo>> queryUserWrongActList(@RequestBody StUserWrongActQO queryCondition) {
        IPage<StUserWrongActVo> userWrongActList = stUserWrongActService.queryUserWrongActList(queryCondition);
        return R.ok(userWrongActList);
    }

    /**
     * 导出违规动作记录
     */
    @PostMapping(value = "/exportWrongActList")
    @ResponseBody
    public void exportUserWrongActList(@RequestBody StUserWrongActQO queryCondition) {
        stUserWrongActService.exportUserWrongActList(queryCondition);
    }


    /**
     * 下载人脸识别错误图片
     */
    @PostMapping(value = "/downUserWrongActList")
    @ResponseBody
    public R downUserWrongActList(@RequestBody StUserWrongActQO queryCondition) {
        stUserWrongActService.downUserWrongActList(queryCondition);
        return R.ok();
    }

    /**
     * 查询用户违规动作
     *
     * @param userId 用户id
     * @param examId 考试id
     * @param id id
     * @param retestTimes 补考次数
     */
    @GetMapping(value = "/getUserWrongByUserId")
    @ResponseBody
    public R<List<StUserWrongActVo>> getUserWrongByUserId(String userId, String examId, String id, String retestTimes) {
        return R.ok(stUserWrongActService.getUserWrongByUserId(userId, examId, id, retestTimes));
    }

    /**
     * 查询用户违规动作下拉列表
     */
    @GetMapping(value = "/getWrongActionComboBox")
    @ResponseBody
    public R<List<Map<String, String>>> getWrongActionComboBox() {
        return R.ok(CommonDataBaseConst.WRONG_ACTION.getSelectList());
    }
}
