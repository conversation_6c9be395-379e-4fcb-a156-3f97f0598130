package com.exam.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.exam.domain.StExamRoom;
import com.exam.domain.vo.ExamRoomVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 考场表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface StExamRoomMapper extends BaseMapper<StExamRoom> {

    IPage<ExamRoomVo> getExamRoomList(Page page, @Param("name") String name, @Param("provinceId") Long provinceId, @Param("cityId") Long cityId, @Param("countyId") Long countyId, @Param("status") Long status);

    @Select("select count(1) from st_exam_room")
    @InterceptorIgnore(tenantLine = "true")
    int getCount();

    List<ExamRoomVo> getNotOccupiedExamRoomList();
}
