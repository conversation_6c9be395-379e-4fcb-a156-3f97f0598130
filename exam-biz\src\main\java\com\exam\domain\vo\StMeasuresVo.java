package com.exam.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.annotation.ExcelDictFormat;
import com.exam.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 重点措施视图对象 st_measures
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StMeasuresVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 是否删除(1.是；0.否)
     */
    @ExcelProperty(value = "是否删除(1.是；0.否)")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long createBy;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long updateBy;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String text;

    /**
     * 所属公司
     */
    @ExcelProperty(value = "所属公司")
    private String companyCode;

    /**
     * 领域
     */
    @ExcelProperty(value = "领域")
    private String domainCode;

    /**
     * 是否置顶
     */
    @ExcelProperty(value = "是否置顶")
    private Long isTop;


}
