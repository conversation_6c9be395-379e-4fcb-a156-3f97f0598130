package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.exam.service.IStExamQuestionGenreService;
import com.exam.common.annotation.Log;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.page.TableDataInfo;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.domain.bo.StExamQuestionGenreBo;
import com.exam.domain.vo.StExamQuestionGenreVo;
import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 试题类型
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/examQuestionGenre")
public class StExamQuestionGenreController extends BaseController {

    private final IStExamQuestionGenreService iStExamQuestionGenreService;

    /**
     * 查询试题类型列表
     */
    @SaCheckPermission("exam:examQuestionGenre:list")
    @GetMapping("/list")
    public TableDataInfo<StExamQuestionGenreVo> list(StExamQuestionGenreBo bo, PageQuery pageQuery) {
        return iStExamQuestionGenreService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出试题类型列表
     */
    @SaCheckPermission("exam:examQuestionGenre:export")
    @Log(title = "试题类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StExamQuestionGenreBo bo, HttpServletResponse response) {
        List<StExamQuestionGenreVo> list = iStExamQuestionGenreService.queryList(bo);
        ExcelUtil.exportExcel(list, "试题类型", StExamQuestionGenreVo.class, response);
    }

    /**
     * 获取试题类型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("exam:examQuestionGenre:query")
    @GetMapping("/{id}")
    public R<StExamQuestionGenreVo> getInfo(@NotNull(message = "主键不能为空")
    @PathVariable Long id) {
        return R.ok(iStExamQuestionGenreService.queryById(id));
    }

    /**
     * 新增试题类型
     */
    @SaCheckPermission("exam:examQuestionGenre:add")
    @Log(title = "试题类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StExamQuestionGenreBo bo) {
        return toAjax(iStExamQuestionGenreService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改试题类型
     */
    @SaCheckPermission("exam:examQuestionGenre:edit")
    @Log(title = "试题类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StExamQuestionGenreBo bo) {
        return toAjax(iStExamQuestionGenreService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除试题类型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("exam:examQuestionGenre:remove")
    @Log(title = "试题类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
    @PathVariable Long[] ids) {
        return toAjax(iStExamQuestionGenreService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
