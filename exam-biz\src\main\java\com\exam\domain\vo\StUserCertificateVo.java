package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.exam.common.utils.StringUtils;
import com.exam.utils.GyUtils;
import com.exam.utils.SpringContextUtil;
import java.util.Date;
import lombok.Data;


/**
 * 用户证件视图对象 st_user_certificate
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@ExcelIgnoreUnannotated
public class StUserCertificateVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 用户
     */
    @ExcelProperty(value = "用户")
    private Long userId;

    /**
     * 行业
     */
    @ExcelProperty(value = "行业")
    private String industry;

    /**
     * 工作单位
     */
    @ExcelProperty(value = "工作单位")
    private String work;

    /**
     * 证件类别
     */
    @ExcelProperty(value = "证件类别")
    private String certificateType;

    /**
     * 专业名称
     */
    @ExcelProperty(value = "专业名称")
    private String professionalName;

    /**
     * 职务
     */
    @ExcelProperty(value = "职务")
    private String duties;

    /**
     * 技术职称
     */
    @ExcelProperty(value = "技术职称")
    private String technical;

    /**
     * 证书编号
     */
    @ExcelProperty(value = "证书编号")
    private String certificateNo;

    /**
     * 发证日期
     */
    @ExcelProperty(value = "发证日期")
    private String issueDate;

    /**
     * 有效期
     */
    @ExcelProperty(value = "有效期")
    private String validityDate;

    /**
     * 发证机关
     */
    @ExcelProperty(value = "发证机关")
    private String issueAuthority;

    /**
     * 文件路径
     */
    @ExcelProperty(value = "文件路径")
    private String filePath;

    /**
     * 是否删除(1.是；0.否)
     */
    @ExcelProperty(value = "是否删除(1.是；0.否)")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long createBy;

    /**
     * 修改人id
     */
    @ExcelProperty(value = "修改人id")
    private Long updateBy;

    /**
     * 领域 1：安全教育培训  2：技能提升 3：经管考试
     */
    @ExcelProperty(value = "领域 1：安全教育培训  2：技能提升 3：经管考试")
    private String domainCode;


    /**
     * 用户姓名
     */
    private String userName;


    /**
     * 证件类别名称
     */
    private String certificateTypeName;


    /**
     * 判定用当前时间
     */
    private String nowTime;

    /**
     * 身份证号码
     */
    private String idNumber;

    /**
     * 错误信息
     */
    private String errorMsg;
    private String fullPicPath;

    public String getFullPicPath() {
        if (GyUtils.isNull(filePath)) {
            return "";
        }
        if (StringUtils.startsWith(filePath, "/img")) {
            return SpringContextUtil.getProperty("train.upload.local.server") + filePath;
        } else if (StringUtils.startsWith(filePath, "img")) {
            return SpringContextUtil.getProperty("train.upload.local.server") + "/" + filePath;
        } else { return SpringContextUtil.getProperty("train.image.server") + filePath; }
    }
}
