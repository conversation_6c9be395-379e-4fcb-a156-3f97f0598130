package com.exam.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.exam.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 【请填写功能名称】对象 st_user
 *
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("st_user")
public class StUser extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 照片
     */
    private String profilePicture;
    /**
     * 出生日期
     */
    private Date birthDate;
    /**
     * 籍贯
     */
    private String nativePlace;
    /**
     * 职称
     */
    private Long title;
    /**
     * 政治面貌
     */
    private Integer policitalStatus;
    /**
     * 毕业学校
     */
    private String graduationSchool;
    /**
     * 毕业时间
     */
    private Date graduationTime;
    /**
     * 专业
     */
    private String major;
    /**
     * 工作经历
     */
    private String workExperience;
    /**
     * 培训经历
     */
    private String trainExperience;
    /**
     * 个人业绩
     */
    private String achievement;
    /**
     * 获奖情况
     */
    private String award;
    /**
     * 是否删除(1.是；0.否)
     */
    private Integer delFlag;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人id
     */
    private Long createBy;
    /**
     * 修改人id
     */
    private Long updateBy;

}
