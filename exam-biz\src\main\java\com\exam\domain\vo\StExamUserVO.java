package com.exam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.exam.constant.CommonConsts;
import com.exam.constant.CommonDataBaseConst;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <p>
 * 考试VO
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Data
@ExcelIgnoreUnannotated
@ApiModel(value="StExamUserVO", description="用户考试vo对象")
public class StExamUserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "考试")
    private String examId;

    @ApiModelProperty(value = "答题卡id")
    private String answerCardId;

    @ApiModelProperty(value = "考试名称")
    private String examName;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "考试时长")
    private Integer examLength;

    @ApiModelProperty(value = "试卷分值")
    private double examScore;

    @ApiModelProperty(value = "答题分数")
    private double score;

    @ApiModelProperty(value = "考试状态")
    private String examStatus;
    /**
     * 入场密码（现场公布不可泄露）
     */
    @ApiModelProperty(value = "入场密码")
    private String admissionPassword;

    @ApiModelProperty(value = "考试人数")
    private Integer personCount;
}
