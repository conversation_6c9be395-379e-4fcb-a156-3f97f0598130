package com.exam.controller;

import com.exam.common.helper.LoginHelper;
import java.util.List;
import java.util.Arrays;

import com.exam.domain.bo.StQuestionProportionBo;
import com.exam.domain.vo.StQuestionProportionVo;
import com.exam.service.IStQuestionProportionService;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.exam.common.annotation.RepeatSubmit;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import com.exam.common.enums.BusinessType;
import com.exam.common.utils.poi.ExcelUtil;
import com.exam.common.core.page.TableDataInfo;

/**
 * 试题类别抽题比例
 *
 * <AUTHOR>
 * @date 2023-11-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/questionProportion")
public class StQuestionProportionController extends BaseController {

    @Resource
    private final IStQuestionProportionService iStQuestionProportionService;

//    /**
//     * 查询试题类别抽题比例列表
//     */
//    @SaCheckPermission("exam:questionProportion:list")
//    @GetMapping("/list")
//    public TableDataInfo<StQuestionProportionVo> list(StQuestionProportionBo bo, PageQuery pageQuery) {
//        return iStQuestionProportionService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 导出试题类别抽题比例列表
//     */
//    @SaCheckPermission("exam:questionProportion:export")
//    @Log(title = "试题类别抽题比例", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(StQuestionProportionBo bo, HttpServletResponse response) {
//        List<StQuestionProportionVo> list = iStQuestionProportionService.queryList(bo);
//        ExcelUtil.exportExcel(list, "试题类别抽题比例", StQuestionProportionVo.class, response);
//    }
//
//    /**
//     * 获取试题类别抽题比例详细信息
//     *
//     * @param id 主键
//     */
//    @SaCheckPermission("exam:questionProportion:query")
//    @GetMapping("/{id}")
//    public R<StQuestionProportionVo> getInfo(@NotNull(message = "主键不能为空")
//    @PathVariable String id) {
//        return R.ok(iStQuestionProportionService.queryById(id));
//    }
//
//    /**
//     * 新增试题类别抽题比例
//     */
//    @SaCheckPermission("exam:questionProportion:add")
//    @Log(title = "试题类别抽题比例", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
//    public R<Void> add(@Validated(AddGroup.class) @RequestBody StQuestionProportionBo bo) {
//        return toAjax(iStQuestionProportionService.insertByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 修改试题类别抽题比例
//     */
//    @SaCheckPermission("exam:questionProportion:edit")
//    @Log(title = "试题类别抽题比例", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StQuestionProportionBo bo) {
//        return toAjax(iStQuestionProportionService.updateByBo(bo) ? 1 : 0);
//    }
//
//    /**
//     * 删除试题类别抽题比例
//     *
//     * @param ids 主键串
//     */
//    @SaCheckPermission("exam:questionProportion:remove")
//    @Log(title = "试题类别抽题比例", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//    @PathVariable String[] ids) {
//        return toAjax(iStQuestionProportionService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
//    }


    /**
     * 查询设置比例
     */
    @GetMapping(value = "/listStQuestionProportion")
    @ResponseBody
    public R<List<StQuestionProportionVo>> listStQuestionProportion() {
        StQuestionProportionBo stQuestionProportionBo = new StQuestionProportionBo();
        stQuestionProportionBo.setTenantId(LoginHelper.getTenantId());
        stQuestionProportionBo.setProjectId(LoginHelper.getProjectId());
        List<StQuestionProportionVo> stQuestionProportionVOList = iStQuestionProportionService
            .listStQuestionProportionByBO(stQuestionProportionBo);
        return R.ok(stQuestionProportionVOList);
    }
}
