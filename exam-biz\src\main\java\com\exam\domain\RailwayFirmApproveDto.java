package com.exam.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 铁路局-企业管理-审批管理
    */
@ApiModel(description="铁路局-企业管理-审批管理")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RailwayFirmApproveDto {

    /**
     * 企业管理Id
     */
    @ApiModelProperty(value="企业管理Ids,多个Id 用逗号拼接")
    private String rfIds;

    /**
     * 原因：驳回时有值
     */
    @ApiModelProperty(value="原因：驳回时有值")
    private String rfaCause;

    /**
     * 0待审核｜1审核通过｜2审核驳回
     */
    @ApiModelProperty(value="0待审核｜1审核通过｜2审核驳回")
    private Integer rfaApprove;

}
