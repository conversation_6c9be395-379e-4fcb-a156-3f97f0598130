package com.exam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.entity.SysUser;
import com.exam.common.core.domain.model.LoginUser;
import com.exam.common.helper.LoginHelper;
import com.exam.common.utils.DateUtils;
import com.exam.common.utils.StringUtils;
import com.exam.domain.RailwayFirm;
import com.exam.domain.RailwayFirmApprove;
import com.exam.domain.RailwayFirmDto;
import com.exam.domain.RailwayProject;
import com.exam.domain.vo.RailwayFirmTreeVo;
import com.exam.domain.vo.RailwayFirmVo;
import com.exam.mapper.RailwayFirmApproveMapper;
import com.exam.mapper.RailwayFirmMapper;
import com.exam.mapper.RailwayProjectMapper;
import com.exam.service.RailwayFirmService;
import com.exam.system.domain.vo.SysOssVo;
import com.exam.system.service.ISysOssService;
import com.exam.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class RailwayFirmServiceImpl extends ServiceImpl<RailwayFirmMapper, RailwayFirm> implements RailwayFirmService{


    @Autowired
    private RailwayFirmApproveMapper firmApproveMapper;
    @Autowired
    private RailwayProjectMapper projectMapper;
    @Autowired
    private ISysOssService ossService;
    @Autowired
    private ISysUserService userService;


    @Override
    public void add(RailwayFirmDto firmDto) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        // todo 校验验证码
        RailwayFirm railwayFirm = new RailwayFirm();
        BeanUtil.copyProperties(firmDto, railwayFirm);
        LocalDateTime dateTime = LocalDateTime.now();
        railwayFirm.setCreateBy(loginUser.getUserId());
        railwayFirm.setCreateTime(dateTime);
        railwayFirm.setUpdateBy(loginUser.getUserId());
        railwayFirm.setUpdateTime(dateTime);
        save(railwayFirm);

        RailwayFirmApprove railwayFirmApprove = new RailwayFirmApprove();
        railwayFirmApprove.setRfId(railwayFirm.getRfId());
        firmApproveMapper.insert(railwayFirmApprove);
    }

    @Override
    public IPage<RailwayFirmVo> queryPage(String rfName, String rfContact, String rfPhone, Integer rfApprove, Integer pageSize, Integer pageNum) {
        IPage<RailwayFirmVo> pageQuery = new Page<>(pageNum, pageSize);
        IPage<RailwayFirmVo> queryPage = baseMapper.queryPage(pageQuery, rfName, rfContact, rfPhone, rfApprove);
        queryPage.getRecords().forEach(rf-> {
            SysUser createUser = userService.selectUserById(rf.getCreateBy());
            SysUser updateUser = userService.selectUserById(rf.getUpdateBy());
            SysUser approverUser = userService.selectUserById(rf.getRfaApprover());
            rf.setCreateByUser(createUser.getNickName());
            rf.setUpdateByUser(updateUser.getNickName());
            rf.setRfaApproverName(ObjectUtil.isEmpty(approverUser) ? null : approverUser.getNickName());
            rf.setCreateTimeStr(rf.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
            rf.setUpdateTimeStr(rf.getUpdateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
            rf.setRfaTimeStr(ObjectUtil.isEmpty(rf.getRfaTime()) ? null :
                rf.getRfaTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        });
        return queryPage;
    }

    @Override
    public void editById(RailwayFirmDto firmDto) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        RailwayFirm railwayFirm = new RailwayFirm();
        BeanUtil.copyProperties(firmDto, railwayFirm);
        LocalDateTime dateTime = LocalDateTime.now();
        railwayFirm.setUpdateBy(loginUser.getUserId());
        railwayFirm.setUpdateTime(dateTime);

        updateById(railwayFirm);

        firmApproveMapper.delete(new LambdaQueryWrapper<RailwayFirmApprove>()
            .eq(RailwayFirmApprove::getRfId, firmDto.getRfId()));

        RailwayFirmApprove railwayFirmApprove = new RailwayFirmApprove();
        railwayFirmApprove.setRfId(railwayFirm.getRfId());
        firmApproveMapper.insert(railwayFirmApprove);
    }

    @Override
    public List<RailwayFirmTreeVo> queryFirmTree(String rfName, Long rfId, Integer isParent) {
        if (isParent == 1) {
            List<RailwayFirm> firmList = list(new LambdaQueryWrapper<RailwayFirm>()
                .like(StringUtils.isNotEmpty(rfName), RailwayFirm::getRfName, rfName));
            return firmList.stream().map(f-> {
                Integer count = userService.queryCountByRfId(f.getRfId(), null);
                RailwayFirmTreeVo railwayFirmTreeVo = new RailwayFirmTreeVo();
                railwayFirmTreeVo.setId(f.getRfId());
                railwayFirmTreeVo.setName(f.getRfName());
                railwayFirmTreeVo.setCount(count);
                return railwayFirmTreeVo;
            }).collect(Collectors.toList());
        }

        Integer count = userService.queryCountByRfId(rfId, null);

        RailwayFirm byId = getById(rfId);
        RailwayFirmTreeVo firmTreeVo = new RailwayFirmTreeVo();
        firmTreeVo.setId(rfId);
        firmTreeVo.setName(byId.getRfName());
        firmTreeVo.setCount(count);
        List<RailwayProject> projectList = projectMapper.selectList(new LambdaQueryWrapper<RailwayProject>()
            .eq(RailwayProject::getRfId, rfId)
            .like(StringUtils.isNotEmpty(rfName), RailwayProject::getRpName, rfName));
        List<RailwayFirmTreeVo> railwayFirmTreeVos = new ArrayList<>(projectList.size());
        projectList.forEach(rp-> {
            Integer rpCount = userService.queryCountByRfId(null, rp.getRpId());
            RailwayFirmTreeVo railwayFirmTreeVo = new RailwayFirmTreeVo();
            railwayFirmTreeVo.setId(rp.getRpId());
            railwayFirmTreeVo.setName(rp.getRpName());
            railwayFirmTreeVo.setCount(rpCount);
            railwayFirmTreeVos.add(railwayFirmTreeVo);
        });
        firmTreeVo.setFirmTreeVoList(railwayFirmTreeVos);
        return Collections.singletonList(firmTreeVo);
    }

    @Override
    public RailwayFirmVo queryByRfId(Long rfId) {
        RailwayFirm rf = getById(rfId);
        RailwayFirmVo railwayFirmVo = new RailwayFirmVo();
        BeanUtil.copyProperties(rf, railwayFirmVo);

        RailwayFirmApprove railwayFirmApprove = firmApproveMapper.selectOne(new LambdaQueryWrapper<RailwayFirmApprove>()
            .eq(RailwayFirmApprove::getRfId, rfId));

        SysUser createUser = userService.selectUserById(rf.getCreateBy());
        SysUser updateUser = userService.selectUserById(rf.getUpdateBy());
        SysUser approverUser = userService.selectUserById(railwayFirmApprove.getRfaApprover());
        railwayFirmVo.setCreateByUser(createUser.getNickName());
        railwayFirmVo.setUpdateByUser(updateUser.getNickName());
        railwayFirmVo.setRfaApproverName(ObjectUtil.isEmpty(approverUser) ? null : approverUser.getNickName());
        railwayFirmVo.setCreateTimeStr(rf.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        railwayFirmVo.setUpdateTimeStr(rf.getUpdateTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));
        railwayFirmVo.setRfaTimeStr(ObjectUtil.isEmpty(railwayFirmApprove.getRfaTime()) ? null : railwayFirmApprove.getRfaTime().format(DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS)));

        return railwayFirmVo;
    }

    @Override
    public RailwayFirm selectById(Long tenantId) {

        return baseMapper.getById(tenantId);
    }


}
