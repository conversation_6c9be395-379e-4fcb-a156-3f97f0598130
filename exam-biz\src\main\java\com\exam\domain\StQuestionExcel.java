package com.exam.domain;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 试题导入用 StQuestion对象
 *
 * <AUTHOR>
 * @since 2021-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StQuestionExcel implements Serializable {

    /**
     * 序号
     */
    private String no;

    /**
     * 试题类别
     */
    private String questionType;

    /**
     * 题干
     */
    private String questionContent;

    /**
     * 选项内容_a
     */
    private String optionContent_a;

    /**
     * 选项内容_b
     */
    private String optionContent_b;

    /**
     * 选项内容_c
     */
    private String optionContent_c;

    /**
     * 选项内容_d
     */
    private String optionContent_d;

    /**
     * 选项内容_e
     */
    private String optionContent_e;

    /**
     * 选项内容_f
     */
    private String optionContent_f;

    /**
     * 选项内容_g
     */
    private String optionContent_g;

    /**
     * 选项内容_h
     */
    private String optionContent_h;

    /**
     * 正确答案
     */
    private String rightKey;

    /**
     * 解析
     */
    private String analysis;

    /**
     * 试题类型
     */
    private Integer questionGenre;

    /**
     * 选项内容
     */
    private String optionContent;

    /**
     * 试题类别名称
     */
    private String questionTypeName;

    /**
     * 试题类型名称
     */
    private String questionGenreName;

    /**
     * 创建日期
     */
    private String createTime;

    /**
     * 创建人
     */
    private String createBy;
}
