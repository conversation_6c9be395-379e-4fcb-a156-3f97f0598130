package com.exam.domain.qo;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 试题表QO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StQuestionProportionQO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 试题类别
     */
    private Long questionType;

    /**
     * 租户
     */
    private String tenantId;

    /**
     * 项目
     */
    private String projectId;

    /**
     * 公司权限数组
     */
    private String[] companyCodes;

}
