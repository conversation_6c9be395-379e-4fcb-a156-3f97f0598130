package com.exam.constant;


import com.exam.exception.BaseResultInfoInterface;

public enum InvigilatorBaseE<PERSON> implements BaseResultInfoInterface {

    NAME_PASSWORD_ISNULL(InvigilatorConsts.NAME_PASSWORD_ISNULL, "用户名或密码不能为空!"),
    NAME_PASSWORD_ERROR(InvigilatorConsts.NAME_PASSWORD_ERROR, "用户名或密码错误!"),
    USER_PASSWORD_ERROR(InvigilatorConsts.USER_PASSWORD_ERROR, "用户密码错误!"),
    USER_INFO_INVALID(InvigilatorConsts.USER_INFO_INVALID, "该用户不存在!"),
    ACCOUNTS_FROZEN(InvigilatorConsts.ACCOUNTS_FROZEN, "账户已冻结!"),
    ACCOUNTS_QUIT(InvigilatorConsts.ACCOUNTS_QUIT, "用户已离职!"),
    ACCOUNTS_EXIST(InvigilatorConsts.ACCOUNTS_EXIST, "用户已存在!"),
    ORIGINAL_PWDERROR(InvigilatorConsts.ORIGINAL_PWDERROR, "原始密码错误!"),
    FILE_TYPE_ERROR(InvigilatorConsts.FILE_TYPE_ERROR, "文件类型错误！"),
    FACE_APP_ERROR(InvigilatorConsts.FACE_ERROR, "APP系统参数缺失!"),
    FACE_RATIO_ERROR(InvigilatorConsts.FACE_ERROR, "此系统暂未设置人脸对比精度!"),
    FACE_ERROR(InvigilatorConsts.FACE_ERROR, "人脸对比无结果!"),
    FACE_COMPARE_UNQUALIFIED(InvigilatorConsts.FACE_COMPARE_UNQUALIFIED, "人脸识别失败：识别结果差距过大!"),
    FACE_COUNT_ERROR(InvigilatorConsts.FACE_ERROR, "识别出多张人脸，请重新识别!"),
    FACE_CONNECTION_TIME_OUT(InvigilatorConsts.FACE_ERROR, "人脸识别超时!"),
    FILE_SCRIP_TYPE_ERROR(InvigilatorConsts.FILE_TYPE_ERROR, "禁止上传脚本文件！"),
    USER_IDCARD_FRONT_ERROR(InvigilatorConsts.USER_FILE_NULL_ERROR, "请上传身份证正面照片!"),
    USER_IDCARD_REVERSE_ERROR(InvigilatorConsts.USER_FILE_NULL_ERROR, "请上传身份证反面照片!");

    /**
     * 错误码
     */
    private String resultCode;

    /**
     * 错误描述
     */
    private String resultMsg;

    InvigilatorBaseEnum(String resultCode, String resultMsg) {
        this.resultCode = resultCode;
        this.resultMsg = resultMsg;
    }

    @Override
    public String getResultCode() {
        return resultCode;
    }

    @Override
    public String getResultMsg() {
        return resultMsg;
    }

}
