package com.exam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
    * 铁路局-企业管理-审批管理
    */
@ApiModel(description="铁路局-企业管理-审批管理")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "railway_firm_approve")
public class RailwayFirmApprove {
    /**
     * 企业管理审核Id
     */
    @TableId(value = "rfa_id")
    @ApiModelProperty(value="企业管理审核Id")
    private Long rfpId;

    /**
     * 企业管理Id
     */
    @TableField(value = "rf_id")
    @ApiModelProperty(value="企业管理Id")
    private Long rfId;

    /**
     * 原因：驳回时有值
     */
    @TableField(value = "rfa_cause")
    @ApiModelProperty(value="原因：驳回时有值")
    private String rfaCause;

    /**
     * 0待审核｜1审核通过｜2审核驳回
     */
    @TableField(value = "rfa_approve")
    @ApiModelProperty(value="0待审核｜1审核通过｜2审核驳回")
    private Integer rfaApprove;

    /**
     * 审核时间
     */
    @TableField(value = "rfa_time")
    @ApiModelProperty(value="审核时间")
    private LocalDateTime rfaTime;

    /**
     * 审核人
     */
    @TableField(value = "rfa_approver")
    @ApiModelProperty(value="审核人")
    private Long rfaApprover;

    /**
     * 审核人
     */
    @TableField(value = "rfa_type")
    @ApiModelProperty(value="审核类型：1企业审核｜2企业用户审核")
    private Integer rfaType;
}
