package com.exam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.exam.common.annotation.Log;
import com.exam.common.core.controller.BaseController;
import com.exam.common.core.domain.PageQuery;
import com.exam.common.core.domain.R;
import com.exam.common.enums.BusinessType;
import com.exam.constant.CommonConsts;
import com.exam.constant.CommonDataBaseConst;
import com.exam.domain.bo.StCourseBo;
import com.exam.domain.bo.StCourseCategoryBo;
import com.exam.domain.qo.StClasshourAskQo;
import com.exam.domain.qo.StCourseCategoryQo;
import com.exam.domain.qo.StCourseQo;
import com.exam.domain.vo.*;
import com.exam.service.IStCourseService;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课程
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/exam/course")
public class StCourseController extends BaseController {

    private final IStCourseService iStCourseService;

    /**
     * 1.1保存课程类别
     *
     * @param stCourseCategoryBo
     * @return
     */
    @SaCheckPermission("exam:course:categoryadd")
    @Log(title = "课程", businessType = BusinessType.INSERT)
    @PostMapping(value = "/saveCourseCategory")
    @ResponseBody
    public R<String> saveCourseCategory(@RequestBody StCourseCategoryBo stCourseCategoryBo) {

        long result = iStCourseService.saveCertificate(stCourseCategoryBo);
        return R.ok(String.valueOf(result));
    }

    /**
     * 1.2修改课程类别
     */
    @SaCheckPermission("exam:course:categoryedit")
    @PutMapping(value = "/updateCertificate")
    @ResponseBody
    public R<String> updateCertificate(@RequestBody StCourseCategoryBo stCourseCategoryBo) {
        long result = iStCourseService.updateCertificate(stCourseCategoryBo);
        return R.ok(String.valueOf(result));
    }

    /**
     * 1.3查询课程类别列表(分页)
     *
     * @param stCourseCategoryQo
     * @param pageQuery
     * @return
     */
    @ResponseBody
    @GetMapping("/selectCourseCategoryListPage")
    public R<IPage<StCourseCategoryVo>> selectCourseCategoryListPage(@RequestBody StCourseCategoryQo stCourseCategoryQo, PageQuery pageQuery) {
        return R.ok(iStCourseService.selectCourseCategoryListPage(pageQuery, stCourseCategoryQo));
    }

    /**
     * 1.4查询课程类别详细
     *
     * @param categoryId 测试ID
     */
    @GetMapping("/getCourseCategoryInfo/{categoryId}")
    @ResponseBody
    public R<StCourseCategoryVo> getCourseCategoryInfo(@PathVariable("categoryId") Long categoryId) {

        return R.ok(iStCourseService.getCourseCategoryInfo(categoryId));
    }

    /**
     * 1.5删除课程类别
     *
     * @param categoryId 测试ID串
     */
    @SaCheckPermission("exam:course:categoryremove")
    @GetMapping(value = "/deleteCourseCategory/{categoryId}")
    @ResponseBody
    public R<Integer> deleteCourseCategory(@PathVariable("categoryId") Long categoryId) {
        int ret = iStCourseService.deleteCourseCategory(categoryId);
        return R.ok(String.valueOf(ret));
    }

    /**
     * 1.6保存课程
     *
     * @param stCourseBo
     * @return
     */
    @SaCheckPermission("exam:course:add")
    @PostMapping(value = "/saveCourse")
    @ResponseBody
    public R<StCourseVo> saveCourse(@RequestBody StCourseBo stCourseBo) {
        return R.ok(iStCourseService.saveCourse(stCourseBo));
    }

    /**
     * 1.7修改课程信息
     *
     * @param stCourseBo
     * @return
     */
    @SaCheckPermission("exam:course:edit")
    @PutMapping(value = "updateCourse")
    @ResponseBody
    public R<StCourseVo> updateCourse(@RequestBody StCourseBo stCourseBo) {
        return R.ok(iStCourseService.updateCourse(stCourseBo));
    }

    /**
     * 1.8批量分配课程
     *
     * @param assignCoursesVO
     * @return
     */
    @SaCheckPermission("exam:course:batchassign")
    @PostMapping(value = "/assignCourseBatch")
    @ResponseBody
    public R<Map> assignCourseBatch(@ApiParam @RequestBody AssignCoursesVo assignCoursesVO) {
        return R.ok(iStCourseService.assignCourseBatch(assignCoursesVO));
    }

    /**
     * 1.9批量删除课程
     *
     * @param deleteCoursesVo
     * @return
     */
    @SaCheckPermission("exam:course:batchdelete")
    @PostMapping(value = "/deleteCourseBatch")
    @ResponseBody
    public R<Integer> deleteCourseBatch(@ApiParam @RequestBody DeleteCoursesVo deleteCoursesVo) {
        return R.ok(iStCourseService.deleteCourseBatch(deleteCoursesVo));
    }

    /**
     * 1.10删除课程
     *
     * @param courseId
     * @return
     */
    @SaCheckPermission("exam:course:delete")
    @GetMapping(value = "/deleteCourse/{courseId}")
    @ResponseBody
    public R deleteCourse(@PathVariable("courseId") Long courseId) {
        iStCourseService.deleteCourse(courseId);
        iStCourseService.deAssignCourse(courseId, null);
        return R.ok();
    }

    /**
     * 1.11查询课程详情
     *
     * @param courseId
     * @return
     */
    @GetMapping(value = "/getCourseInfo/{courseId}")
    @ResponseBody
    public R<StCourseVo> getCourseInfo(@PathVariable("courseId") Long courseId) {
        return R.ok(iStCourseService.getCourseInfo(courseId));
    }

    /**
     * 1.12保存课时
     *
     * @param StClasshourVo
     * @return
     */
    @SaCheckPermission("exam:course:add")
    @PostMapping(value = "/saveClasshour")
    @ResponseBody
    public R<String> saveClasshour(@ApiParam @RequestBody StClasshourVo StClasshourVo) {
        long id = iStCourseService.saveClasshour(StClasshourVo);
        return R.ok();
    }

    /**
     * 1.13查询课时详情
     *
     * @param classhourId
     * @return
     */
    @GetMapping(value = "/getClasshourInfo/{classhourId}")
    @ResponseBody
    public R<StClasshourVo> getClasshourInfo(@PathVariable("classhourId") Long classhourId) {
        StClasshourVo StClasshourVo = iStCourseService.getClasshourInfo(classhourId);
        return R.ok(StClasshourVo);
    }

    /**
     * 1.14通过课程id查询课时列表
     *
     * @param courseId
     * @return
     */
    @GetMapping(value = "/getClasshourListByCourseId/{courseId}")
    @ResponseBody
    public R<List<StClasshourVo>> getClasshourListByCourseId(@PathVariable("courseId") Long courseId) {
        List<StClasshourVo> StClasshourVolist = iStCourseService.getClasshourListByCourseId(courseId);
        return R.ok(StClasshourVolist);
    }

    /**
     * 1.15删除课时
     *
     * @param classhourId
     * @return
     */
    @SaCheckPermission("exam:course:classhourdelete")
    @GetMapping(value = "/deleteClasshour/{classhourId}")
    @ResponseBody
    public R deleteClasshour(@PathVariable("classhourId") Long classhourId) {

        return R.ok(iStCourseService.deleteClasshour(classhourId));
    }

    /**
     * 1.16查询课程类别列表
     *
     * @param fatherId
     * @return
     */
    @GetMapping(value = "/selectCourseCategoryList/{fatherId}")
    @ResponseBody
    public R<List<StCourseCategoryVo>> selectCourseCategoryList(@PathVariable("fatherId") String fatherId) {
        StCourseCategoryQo stCourseCategoryQo = new StCourseCategoryQo();
        stCourseCategoryQo.setFatherId(Long.valueOf(fatherId));
        stCourseCategoryQo.setIsSafety(Long.valueOf(CommonDataBaseConst.YES_OR_NO.NO.getCode()));
//        Map conditionParam = new HashMap<String,String>();
//        conditionParam.put("companyCode", UserHelpBean.getCompanyCode());
//        conditionParam.put("domainCode", UserHelpBean.getDomainCode());
//        conditionParam.put("isSafety", CommonDataBaseConst.YES_OR_NO.NO.getCode());
//        conditionParam.put("fatherId", fatherId);
        List<StCourseCategoryVo> courseCategoryVOs = iStCourseService.selectCourseCategoryList(stCourseCategoryQo);
        return R.ok(courseCategoryVOs);
    }

    /**
     * 1.17查询课程列表(分页)
     */
    @PostMapping("/selectCourseListPage")
    @ResponseBody
    public R<IPage<StCourseVo>> selectCourseListPage(@RequestBody StCourseQo stCourseQo, PageQuery pageQuery) {
        return R.ok(iStCourseService.selectCourseListPage(pageQuery, stCourseQo));
    }

    /**
     * 1.18通过课程类别id查询课程
     *
     * @param categoryId
     * @return
     */
    @GetMapping(value = "/selectCourseByCategoryId/{categoryId}")
    @ResponseBody
    public R<List<StCourseVo>> selectCourseByCategoryId(@PathVariable("categoryId") String categoryId) {
        StCourseQo stCourseQo = new StCourseQo();
        stCourseQo.setCategoryId(Long.valueOf(categoryId));
        List<StCourseVo> courseVOList = iStCourseService.selectCourseByCategoryId(stCourseQo);
        return R.ok(courseVOList);
    }

    /**
     * 1.19判断是否可以删除课程
     *
     * @param courseId
     * @return
     */
    @GetMapping(value = "/deleteCourseCheck/{courseId}")
    @ResponseBody
    public R deleteCourseCheck(@PathVariable("courseId") String courseId) {
        int ret = iStCourseService.deleteCourseCheck(Long.parseLong(courseId));
        if (ret == CommonConsts.DELETE_COURSE_NE1) {
            // 该课程下存在课时，不能删除！
            R.fail(String.valueOf(CommonConsts.DELETE_COURSE_NE1));
        } else if (ret == CommonConsts.DELETE_COURSE_NE2) {
            // 该课程下存在试题，不能删除！
            R.fail(String.valueOf(CommonConsts.DELETE_COURSE_NE2));
        }
        return R.ok();
    }

    /**
     * 1.20查询课程类别集合(级联）--------
     *
     * @param categoryName
     * @param createTime
     * @return
     */
    @GetMapping(value = "/selectSecCourseCategoryList")
    @ResponseBody
    public R<List<StCourseCategoryVo>> selectCasCourseCategoryList(@RequestParam(required = false) String categoryName, @RequestParam(required = false) String createTime) {

//        Map conditionParam = new HashMap<String,String>();
//        conditionParam.put("categoryName",categoryName);
////        conditionParam.put("createTime",createTime);
////        conditionParam.put("isSafety", CommonDataBaseConst.YES_OR_NO.NO.getCode());
        StCourseCategoryQo stCourseCategoryQo = new StCourseCategoryQo();
        stCourseCategoryQo.setCategoryName(categoryName);
        stCourseCategoryQo.setCreateTime(createTime);
        stCourseCategoryQo.setIsSafety(Long.valueOf(CommonDataBaseConst.YES_OR_NO.NO.getCode()));
//        conditionParam.put("domainCode",UserHelpBean.getDomainCode());
//        conditionParam.put("companyCode", UserHelpBean.getCompanyCode());
        List<StCourseCategoryVo> courseCategoryVOs = iStCourseService.selectCasCourseCategoryList(stCourseCategoryQo);
        System.out.println(courseCategoryVOs);
        return R.ok(courseCategoryVOs);
    }

    /**
     * 1.21查询课程类别集合（级联全部可见）-------------
     *
     * @param categoryName
     * @param createTime
     * @return
     */
    @GetMapping(value = "/selectSecCourseCategoryListVisible")
    @ResponseBody
    public R<List<StCourseCategoryVo>> selectSecCourseCategoryListVisible(@RequestParam(required = false) String categoryName, @RequestParam(required = false) String createTime) {
        StCourseCategoryQo stCourseCategoryQo = new StCourseCategoryQo();
        stCourseCategoryQo.setCategoryName(categoryName);
        stCourseCategoryQo.setCreateTime(createTime);
        stCourseCategoryQo.setIsSafety(Long.valueOf(CommonDataBaseConst.YES_OR_NO.NO.getCode()));
//        Map conditionParam = new HashMap<String,String>();
//        conditionParam.put("categoryName",categoryName);
//        conditionParam.put("createTime",createTime);
//        conditionParam.put("isSafety",CommonDataBaseConst.YES_OR_NO.NO.getCode());
//        conditionParam.put("domainCode",UserHelpBean.getDomainCode());
        List<StCourseCategoryVo> courseCategoryVOs = iStCourseService.selectCasCourseCategoryList(stCourseCategoryQo);
        return R.ok(courseCategoryVOs);
    }

    /**
     * 1.22 查询课时间提问试题集合
     *
     * @param classhourId
     * @return
     */
    @GetMapping(value = "/selectClasshourAskList/{classhourId}")
    @ResponseBody
    public R<List<StClasshourAskVo>> selectClasshourAskList(@PathVariable("classhourId") String classhourId) {
        StClasshourAskQo stClasshourAskQo = new StClasshourAskQo();
        stClasshourAskQo.setClasshourId(Long.valueOf(classhourId));
//        Map conditionParam = new HashMap<String,String>();
//        conditionParam.put("classhourId",classhourId);
        List<StClasshourAskVo> questionVOs = iStCourseService.selectClasshourAskList(stClasshourAskQo);
        return R.ok(questionVOs);
    }

    /**
     * 1.23保存课时间提问试题----------------
     *
     * @param classhourAskVo
     * @return
     */
    @SaCheckPermission("exam:course:addask")
    @PostMapping(value = "/saveClasshourAsk")
    @ResponseBody
    public R<String> saveClasshourAsk(@ApiParam @RequestBody ClasshourAskVo classhourAskVo) {
        return R.ok(iStCourseService.saveClasshourAsk(classhourAskVo.getStClasshourAskVoList()));
    }

    /**
     * 1.24删除课时间提问试题
     *
     * @param classhourAskId
     * @return
     */
    @SaCheckPermission("exam:course:deleteask")
    @GetMapping(value = "/deleteClasshourAsk/{classhourAskId}")
    @ResponseBody
    public R<String> deleteClasshourAsk(@PathVariable("classhourAskId") Long classhourAskId) {
        return R.ok(iStCourseService.deleteClasshourAsk(classhourAskId));
    }

    /**
     * 1.25 检验数据重复名称（1，通过； 0，不通过）
     *
     * @param id
     * @param checkName
     * @param checkType
     * @return
     */
    @GetMapping(value = "/checkRepeatName")
    @ResponseBody
    public R<String> checkRepeatName(String id, String checkName, String checkType) {
        Map conditionParam = new HashMap<String, String>();
        conditionParam.put("id", id);
        conditionParam.put("isSafety", "0");
        conditionParam.put("checkName", checkName);
        conditionParam.put("checkType", checkType);
//        conditionParam.put("companyCode", UserHelpBean.getCompanyCode());
//        conditionParam.put("domainCode", UserHelpBean.getDomainCode());
        return R.ok(iStCourseService.checkRepeatName(conditionParam));
    }

    /**
     * 1.26 查询课时间提问试题详情
     *
     * @param classhourAskId
     * @return
     */
    @GetMapping(value = "/selectClasshourAskInfo/{classhourAskId}")
    @ResponseBody
    public R<StClasshourAskVo> selectClasshourAskInfo(@PathVariable("classhourAskId") Long classhourAskId) {
        StClasshourAskVo StClasshourAskVo = iStCourseService.selectClasshourAskInfo(classhourAskId);
        return R.ok(StClasshourAskVo);
    }

    /**
     * 1.27根据课程id，重置对应客户的培训进度
     *
     * @param courseId
     * @return
     */
    @SaCheckPermission("exam:course:reset")
    @PostMapping(value = "/resetPlayProgresByCourseId/{courseId}")
    @ResponseBody
    public R<String> resetPlayProgresByCourseId(@PathVariable("courseId") String courseId) {
        return R.ok(iStCourseService.resetPlayProgresByCourseId(courseId));
    }

    /**
     * 1.28查询课程列表
     *
     * @param stCourseQo
     * @return
     */
    @PostMapping(value = "/selectCourseList")
    @ResponseBody
    public R<List<StCourseVo>> selectCourseList(@RequestBody StCourseQo stCourseQo) {
        stCourseQo.setIsSafety(Long.valueOf(CommonDataBaseConst.YES_OR_NO.NO.getCode()));
//        Map conditionParam = new HashMap<String,String>();
//        conditionParam.put("courseName",courseName);
//        conditionParam.put("createTime",createTime);
//        conditionParam.put("categoryId",categoryId);
//        conditionParam.put("groupId",groupId);
//        conditionParam.put("isSafety", CommonDataBaseConst.YES_OR_NO.NO.getCode());
        //课程不需要权限 2021.9.10修改
        //conditionParam.put("companyCode",UserHelpBean.getCompanyCode());
//        conditionParam.put("domainCode",UserHelpBean.getDomainCode());
        List<StCourseVo> courseVOList = iStCourseService.selectCourseList(stCourseQo);
        return R.ok(courseVOList);
    }

    /**
     * 1.29查询所有课程
     * @param stCourseQo
     * @return
     */
    @PostMapping(value = "/selectAllCourseList")
    @ResponseBody
    public R<List<StCourseVo>> selectAllCourseList(@RequestBody StCourseQo stCourseQo) {
        //课程不需要权限 2021.9.10修改
        //conditionParam.put("companyCode",UserHelpBean.getCompanyCode());
//        conditionParam.put("domainCode",UserHelpBean.getDomainCode());
        List<StCourseVo> courseVOList = iStCourseService.selectCourseList(stCourseQo);
        courseVOList.stream().forEach(item -> {
            if (String.valueOf(CommonDataBaseConst.YES_OR_NO.YES.getCode()).equals(item.getIsSafety())) {
                String itemCourseName = item.getCourseName();
                item.setCourseName(itemCourseName + "(安全管理)");
            }
        });
        return R.ok(courseVOList);
    }

    /**
     * 1.30根据特定条件 查询课程列表
     *
     * @param queryCon 查询条件，0：全部 1：有课时的课程 2：精品课程
     * @return 满足条件的课程列表
     */
    @GetMapping(value = "/selectCourseListByCondition")
    @ResponseBody
    public R<List<StCourseVo>> selectCourseListByCondition(@RequestParam(required=false)Integer queryCon) {
        if(queryCon == null){
            queryCon = 0;
        }
        Map<String,Integer> conditionParam = new HashMap<>();
        conditionParam.put("queryCon",queryCon);
        List<StCourseVo> courseVOList = iStCourseService.selectCourseListByCondition(conditionParam);
        return R.ok(courseVOList);
    }
}
