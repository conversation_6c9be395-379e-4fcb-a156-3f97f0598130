package com.exam.domain.bo;

import com.exam.common.core.domain.BaseEntity;
import com.exam.common.core.validate.AddGroup;
import com.exam.common.core.validate.EditGroup;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 用户组群关系业务对象 st_user_group
 *
 * <AUTHOR>
 * @date 2023-11-07
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StUserGroupBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 组群id
     */
    @NotNull(message = "组群id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long groupId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tenantId;


}
