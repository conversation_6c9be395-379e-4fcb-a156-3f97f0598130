package com.exam.domain.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 培训情况
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserTrainSituationQO", description="培训情况vo")
public class UserTrainSituationQO implements Serializable {

    @ApiModelProperty(value = "人员id")
    private String userId;

    @ApiModelProperty(value = "开始培训时间")
    private String startTrainTime;

    @ApiModelProperty(value = "培训时间")
    private String endTrainTime;

    @ApiModelProperty(value = "课程来源（1，规定；2，自选）")
    private String courseSource;

    @ApiModelProperty(value = "培训状态（1，已完成；2，培训中；3，待培训）")
    private String trainStatus;

}

